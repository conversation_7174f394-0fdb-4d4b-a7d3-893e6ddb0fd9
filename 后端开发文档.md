# 心镜轨迹 - Golang 后端开发文档

## 📋 项目概述

**项目名称**: 心镜轨迹后端服务  
**技术栈**: Golang + Gin + GORM + MySQL + Redis  
**架构模式**: 分层架构 (Controller → Service → Repository)  
**部署方式**: 单体服务部署（Docker 为主，可部署至云服务器）  

## 🏗️ 初期架构设计

### 技术选型

| 模块 | 技术 | 说明 |
|------|------|------|
| Web 框架 | Gin | 高性能、轻量化 HTTP 服务 |
| ORM | GORM | 数据库抽象层 |
| 数据库 | MySQL | 主数据库，存储用户、测试、报告等信息 |
| 缓存 | Redis | 存储用户 session、测试题缓存、限流等 |
| 鉴权 | JWT | 用户登录后的 Token 认证机制 |
| 配置 | viper | 配置文件管理与热更新支持 |
| 日志 | zap | 结构化日志输出 |

### 项目结构
```
mbti-backend/
├── cmd/                    # 应用入口
│   └── server/
│       └── main.go
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── controller/        # 控制器层
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   ├── model/             # 数据模型
│   ├── middleware/        # 中间件
│   └── utils/             # 工具函数
├── pkg/                   # 外部公共包
│   ├── auth/              # 认证相关
│   ├── database/          # 数据库连接
│   ├── cache/             # 缓存管理
│   └── logger/            # 日志管理
├── config/                # 配置文件
├── scripts/               # 脚本文件
├── docker/                # Docker配置
├── go.mod
├── go.sum
├── Dockerfile
└── docker-compose.yml
```

## 🗄️ 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(64) UNIQUE NOT NULL COMMENT '微信openid',
    nickname VARCHAR(64) NOT NULL COMMENT '用户昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    total_tests INT DEFAULT 0 COMMENT '总测试次数',
    most_frequent_type VARCHAR(4) COMMENT '最多出现性格类型',
    points INT DEFAULT 0 COMMENT '积分余额',
    status TINYINT DEFAULT 1 COMMENT '状态: 1-正常 0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_openid (openid),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### MBTI测试记录表 (mbti_tests)
```sql
CREATE TABLE mbti_tests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    mbti_type VARCHAR(4) NOT NULL COMMENT 'MBTI类型',
    ie_score INT NOT NULL COMMENT 'IE维度得分',
    ns_score INT NOT NULL COMMENT 'NS维度得分',
    tf_score INT NOT NULL COMMENT 'TF维度得分',
    pj_score INT NOT NULL COMMENT 'PJ维度得分',
    ie_preference ENUM('I', 'E') NOT NULL COMMENT 'IE偏好',
    ns_preference ENUM('N', 'S') NOT NULL COMMENT 'NS偏好',
    tf_preference ENUM('T', 'F') NOT NULL COMMENT 'TF偏好',
    pj_preference ENUM('P', 'J') NOT NULL COMMENT 'PJ偏好',
    keywords JSON COMMENT '关键词',
    description TEXT COMMENT '类型描述',
    duration INT COMMENT '测试时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_mbti_type (mbti_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MBTI测试记录表';
```

### 测试题目表 (mbti_questions)
```sql
CREATE TABLE mbti_questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL COMMENT '题目内容',
    dimension ENUM('IE', 'NS', 'TF', 'PJ') NOT NULL COMMENT '测试维度',
    option_a VARCHAR(255) NOT NULL COMMENT '选项A',
    option_b VARCHAR(255) NOT NULL COMMENT '选项B',
    weight_a INT DEFAULT 1 COMMENT '选项A权重',
    weight_b INT DEFAULT 1 COMMENT '选项B权重',
    order_num INT NOT NULL COMMENT '题目顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_dimension (dimension),
    INDEX idx_order_num (order_num),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MBTI测试题目表';
```

### AI报告表 (ai_reports)
```sql
CREATE TABLE ai_reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    mbti_type VARCHAR(4) NOT NULL COMMENT 'MBTI类型',
    type ENUM('career', 'relationship', 'personal') NOT NULL COMMENT '报告类型',
    title VARCHAR(255) NOT NULL COMMENT '报告标题',
    content TEXT NOT NULL COMMENT '报告内容',
    recommendations JSON COMMENT '建议列表',
    is_paid BOOLEAN DEFAULT FALSE COMMENT '是否付费报告',
    price INT DEFAULT 0 COMMENT '价格(分)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_mbti_type (mbti_type),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分析报告表';
```

### 支付订单表 (payment_orders)
```sql
CREATE TABLE payment_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(64) UNIQUE NOT NULL COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id VARCHAR(64) NOT NULL COMMENT '产品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '产品名称',
    amount INT NOT NULL COMMENT '支付金额(分)',
    status ENUM('pending', 'paid', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '订单状态',
    payment_method ENUM('wechat', 'alipay') COMMENT '支付方式',
    transaction_id VARCHAR(64) COMMENT '第三方交易号',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';
```

### 积分记录表 (point_records)
```sql
CREATE TABLE point_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type ENUM('earn', 'spend') NOT NULL COMMENT '类型: 赚取/消费',
    action VARCHAR(64) NOT NULL COMMENT '动作类型',
    amount INT NOT NULL COMMENT '积分变动',
    description VARCHAR(255) COMMENT '描述',
    balance_after INT NOT NULL COMMENT '变动后余额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';
```

## 🏛️ 代码结构

### 1. 数据模型 (internal/model/)

```go
// user.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID                uint      `json:"id" gorm:"primaryKey"`
    OpenID            string    `json:"openid" gorm:"uniqueIndex;size:64;not null"`
    Nickname          string    `json:"nickname" gorm:"size:64;not null"`
    Avatar            string    `json:"avatar" gorm:"size:255"`
    TotalTests        int       `json:"totalTests" gorm:"default:0"`
    MostFrequentType  string    `json:"mostFrequentType" gorm:"size:4"`
    Points            int       `json:"points" gorm:"default:0"`
    Status            int8      `json:"status" gorm:"default:1"`
    CreatedAt         time.Time `json:"createdAt"`
    UpdatedAt         time.Time `json:"updatedAt"`
}

// mbti_test.go
type MBTITest struct {
    ID             uint      `json:"id" gorm:"primaryKey"`
    UserID         uint      `json:"userId" gorm:"not null;index"`
    MBTIType       string    `json:"mbtiType" gorm:"size:4;not null;index"`
    IEScore        int       `json:"ieScore" gorm:"not null"`
    NSScore        int       `json:"nsScore" gorm:"not null"`
    TFScore        int       `json:"tfScore" gorm:"not null"`
    PJScore        int       `json:"pjScore" gorm:"not null"`
    IEPreference   string    `json:"iePreference" gorm:"size:1;not null"`
    NSPreference   string    `json:"nsPreference" gorm:"size:1;not null"`
    TFPreference   string    `json:"tfPreference" gorm:"size:1;not null"`
    PJPreference   string    `json:"pjPreference" gorm:"size:1;not null"`
    Keywords       JSON      `json:"keywords"`
    Description    string    `json:"description" gorm:"type:text"`
    Duration       int       `json:"duration"`
    CreatedAt      time.Time `json:"createdAt"`
    User           User      `json:"user" gorm:"foreignKey:UserID"`
}

// ai_report.go
type AIReport struct {
    ID             uint      `json:"id" gorm:"primaryKey"`
    UserID         uint      `json:"userId" gorm:"not null;index"`
    MBTIType       string    `json:"mbtiType" gorm:"size:4;not null;index"`
    Type           string    `json:"type" gorm:"size:20;not null;index"`
    Title          string    `json:"title" gorm:"size:255;not null"`
    Content        string    `json:"content" gorm:"type:text;not null"`
    Recommendations JSON     `json:"recommendations"`
    IsPaid         bool      `json:"isPaid" gorm:"default:false"`
    Price          int       `json:"price" gorm:"default:0"`
    CreatedAt      time.Time `json:"createdAt"`
    User           User      `json:"user" gorm:"foreignKey:UserID"`
}
```

### 2. 控制器层 (internal/controller/)

```go
// user_controller.go
package controller

import (
    "github.com/gin-gonic/gin"
    "net/http"
    "your-project/internal/service"
    "your-project/pkg/response"
)

type UserController struct {
    userService *service.UserService
}

func NewUserController(userService *service.UserService) *UserController {
    return &UserController{userService: userService}
}

// 微信登录
func (c *UserController) Login(ctx *gin.Context) {
    var req struct {
        Code     string `json:"code" binding:"required"`
        UserInfo struct {
            NickName string `json:"nickName"`
            AvatarUrl string `json:"avatarUrl"`
            Gender   int    `json:"gender"`
            Country  string `json:"country"`
            Province string `json:"province"`
            City     string `json:"city"`
        } `json:"userInfo"`
    }

    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误", err)
        return
    }

    result, err := c.userService.Login(ctx, req.Code, req.UserInfo)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "登录失败", err)
        return
    }

    response.Success(ctx, result)
}

// 获取用户信息
func (c *UserController) GetProfile(ctx *gin.Context) {
    userID := ctx.GetUint("userID")
    
    user, err := c.userService.GetProfile(ctx, userID)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "获取用户信息失败", err)
        return
    }

    response.Success(ctx, user)
}

// 获取用户统计数据
func (c *UserController) GetStatistics(ctx *gin.Context) {
    userID := ctx.GetUint("userID")
    
    stats, err := c.userService.GetStatistics(ctx, userID)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "获取统计数据失败", err)
        return
    }

    response.Success(ctx, stats)
}
```

### 3. 服务层 (internal/service/)

```go
// user_service.go
package service

import (
    "context"
    "your-project/internal/model"
    "your-project/internal/repository"
    "your-project/pkg/auth"
    "your-project/pkg/cache"
)

type UserService struct {
    userRepo    *repository.UserRepository
    cache       *cache.RedisCache
    authService *auth.WechatAuth
}

func NewUserService(userRepo *repository.UserRepository, cache *cache.RedisCache, authService *auth.WechatAuth) *UserService {
    return &UserService{
        userRepo:    userRepo,
        cache:       cache,
        authService: authService,
    }
}

// 微信登录
func (s *UserService) Login(ctx context.Context, code string, userInfo struct {
    NickName string `json:"nickName"`
    AvatarUrl string `json:"avatarUrl"`
    Gender   int    `json:"gender"`
    Country  string `json:"country"`
    Province string `json:"province"`
    City     string `json:"city"`
}) (*model.User, string, error) {
    // 1. 通过code获取微信openid
    openid, err := s.authService.GetOpenID(ctx, code)
    if err != nil {
        return nil, "", err
    }

    // 2. 查找或创建用户
    user, err := s.userRepo.FindByOpenID(ctx, openid)
    if err != nil {
        // 用户不存在，创建新用户
        user = &model.User{
            OpenID:   openid,
            Nickname: userInfo.NickName,
            Avatar:   userInfo.AvatarUrl,
            Status:   1,
        }
        err = s.userRepo.Create(ctx, user)
        if err != nil {
            return nil, "", err
        }
    } else {
        // 更新用户信息
        user.Nickname = userInfo.NickName
        user.Avatar = userInfo.AvatarUrl
        err = s.userRepo.Update(ctx, user)
        if err != nil {
            return nil, "", err
        }
    }

    // 3. 生成JWT token
    token, err := s.authService.GenerateToken(user.ID)
    if err != nil {
        return nil, "", err
    }

    // 4. 缓存用户信息
    s.cache.SetUser(ctx, user.ID, user)

    return user, token, nil
}

// 获取用户信息
func (s *UserService) GetProfile(ctx context.Context, userID uint) (*model.User, error) {
    // 先从缓存获取
    user, err := s.cache.GetUser(ctx, userID)
    if err == nil {
        return user, nil
    }

    // 缓存未命中，从数据库获取
    user, err = s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return nil, err
    }

    // 更新缓存
    s.cache.SetUser(ctx, userID, user)

    return user, nil
}

// 获取用户统计数据
func (s *UserService) GetStatistics(ctx context.Context, userID uint) (*model.UserStatistics, error) {
    stats, err := s.userRepo.GetStatistics(ctx, userID)
    if err != nil {
        return nil, err
    }

    return stats, nil
}
```

### 4. 数据访问层 (internal/repository/)

```go
// user_repository.go
package repository

import (
    "context"
    "your-project/internal/model"
    "gorm.io/gorm"
)

type UserRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) *UserRepository {
    return &UserRepository{db: db}
}

func (r *UserRepository) FindByOpenID(ctx context.Context, openid string) (*model.User, error) {
    var user model.User
    err := r.db.WithContext(ctx).Where("openid = ?", openid).First(&user).Error
    if err != nil {
        return nil, err
    }
    return &user, nil
}

func (r *UserRepository) FindByID(ctx context.Context, id uint) (*model.User, error) {
    var user model.User
    err := r.db.WithContext(ctx).First(&user, id).Error
    if err != nil {
        return nil, err
    }
    return &user, nil
}

func (r *UserRepository) Create(ctx context.Context, user *model.User) error {
    return r.db.WithContext(ctx).Create(user).Error
}

func (r *UserRepository) Update(ctx context.Context, user *model.User) error {
    return r.db.WithContext(ctx).Save(user).Error
}

func (r *UserRepository) GetStatistics(ctx context.Context, userID uint) (*model.UserStatistics, error) {
    var stats model.UserStatistics
    
    // 获取总测试次数
    err := r.db.WithContext(ctx).Model(&model.MBTITest{}).
        Where("user_id = ?", userID).Count(&stats.TotalTests).Error
    if err != nil {
        return nil, err
    }

    // 获取最多出现性格类型
    err = r.db.WithContext(ctx).Model(&model.MBTITest{}).
        Select("mbti_type, COUNT(*) as count").
        Where("user_id = ?", userID).
        Group("mbti_type").
        Order("count DESC").
        Limit(1).
        Scan(&struct {
            MBTIType string `json:"mbtiType"`
            Count    int    `json:"count"`
        }{}).Error
    if err != nil {
        return nil, err
    }

    // 获取测试历史趋势
    err = r.db.WithContext(ctx).Model(&model.MBTITest{}).
        Select("DATE(created_at) as date, COUNT(*) as count").
        Where("user_id = ?", userID).
        Group("DATE(created_at)").
        Order("date DESC").
        Limit(30).
        Scan(&stats.TestHistory).Error
    if err != nil {
        return nil, err
    }

    // 获取性格类型分布
    err = r.db.WithContext(ctx).Model(&model.MBTITest{}).
        Select("mbti_type as type, COUNT(*) as count").
        Where("user_id = ?", userID).
        Group("mbti_type").
        Scan(&stats.TypeDistribution).Error
    if err != nil {
        return nil, err
    }

    return &stats, nil
}
```

## 🔧 核心功能实现

### 1. MBTI测试计算逻辑

```go
// mbti_service.go
package service

import (
    "context"
    "your-project/internal/model"
)

type MBTIService struct {
    questionRepo *repository.QuestionRepository
    testRepo     *repository.TestRepository
}

// 计算MBTI类型
func (s *MBTIService) CalculateMBTI(answers []model.MBTIAnswer) (*model.MBTIResult, error) {
    // 初始化维度得分
    scores := map[string]int{
        "E": 0, "I": 0,
        "S": 0, "N": 0,
        "T": 0, "F": 0,
        "J": 0, "P": 0,
    }

    // 计算各维度得分
    for _, answer := range answers {
        question, err := s.questionRepo.FindByID(context.Background(), answer.QuestionID)
        if err != nil {
            return nil, err
        }

        if answer.Answer == "A" {
            scores[question.OptionA] += question.WeightA
        } else {
            scores[question.OptionB] += question.WeightB
        }
    }

    // 确定偏好
    preferences := make(map[string]string)
    if scores["E"] > scores["I"] {
        preferences["IE"] = "E"
    } else {
        preferences["IE"] = "I"
    }

    if scores["N"] > scores["S"] {
        preferences["NS"] = "N"
    } else {
        preferences["NS"] = "S"
    }

    if scores["T"] > scores["F"] {
        preferences["TF"] = "T"
    } else {
        preferences["TF"] = "F"
    }

    if scores["J"] > scores["P"] {
        preferences["PJ"] = "J"
    } else {
        preferences["PJ"] = "P"
    }

    // 生成MBTI类型
    mbtiType := preferences["IE"] + preferences["NS"] + preferences["TF"] + preferences["PJ"]

    // 获取类型描述和关键词
    description, keywords := s.getTypeDescription(mbtiType)

    return &model.MBTIResult{
        MBTIType: mbtiType,
        Dimensions: model.MBTIDimensions{
            IE: model.Dimension{Score: scores["E"] - scores["I"], Preference: preferences["IE"]},
            NS: model.Dimension{Score: scores["N"] - scores["S"], Preference: preferences["NS"]},
            TF: model.Dimension{Score: scores["T"] - scores["F"], Preference: preferences["TF"]},
            PJ: model.Dimension{Score: scores["J"] - scores["P"], Preference: preferences["PJ"]},
        },
        Keywords:    keywords,
        Description: description,
    }, nil
}
```

### 2. AI报告生成

```go
// ai_service.go
package service

import (
    "context"
    "encoding/json"
    "your-project/internal/model"
    "your-project/pkg/ai"
)

type AIService struct {
    aiClient    *ai.OpenAIClient
    reportRepo  *repository.ReportRepository
}

// 生成职业建议
func (s *AIService) GenerateCareerAdvice(ctx context.Context, mbtiType string) (*model.AIReport, error) {
    prompt := s.buildCareerPrompt(mbtiType)
    
    response, err := s.aiClient.GenerateText(ctx, prompt)
    if err != nil {
        return nil, err
    }

    // 解析AI响应
    var result struct {
        Title          string   `json:"title"`
        Content        string   `json:"content"`
        Recommendations []string `json:"recommendations"`
    }
    
    err = json.Unmarshal([]byte(response), &result)
    if err != nil {
        return nil, err
    }

    report := &model.AIReport{
        MBTIType:       mbtiType,
        Type:           "career",
        Title:          result.Title,
        Content:        result.Content,
        Recommendations: result.Recommendations,
    }

    return report, nil
}

// 构建职业建议提示词
func (s *AIService) buildCareerPrompt(mbtiType string) string {
    return `基于MBTI类型${mbtiType}，生成职业发展建议。请包含以下内容：
1. 适合的职业方向
2. 职业发展建议
3. 技能提升建议
4. 工作环境偏好
请以JSON格式返回，包含title、content、recommendations字段。`
}
```

## 🔐 认证与安全

### JWT认证中间件

```go
// middleware/auth.go
package middleware

import (
    "github.com/gin-gonic/gin"
    "your-project/pkg/auth"
    "your-project/pkg/response"
    "net/http"
    "strings"
)

func JWTAuth() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            response.Error(c, http.StatusUnauthorized, "未提供认证token", nil)
            c.Abort()
            return
        }

        // 移除Bearer前缀
        token = strings.TrimPrefix(token, "Bearer ")

        // 验证token
        claims, err := auth.ValidateToken(token)
        if err != nil {
            response.Error(c, http.StatusUnauthorized, "token无效", err)
            c.Abort()
            return
        }

        // 设置用户信息到上下文
        c.Set("userID", claims.UserID)
        c.Next()
    }
}
```

### 微信认证服务

```go
// pkg/auth/wechat.go
package auth

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
)

type WechatAuth struct {
    appID     string
    appSecret string
}

func NewWechatAuth(appID, appSecret string) *WechatAuth {
    return &WechatAuth{
        appID:     appID,
        appSecret: appSecret,
    }
}

// 获取微信openid
func (w *WechatAuth) GetOpenID(ctx context.Context, code string) (string, error) {
    url := fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
        w.appID, w.appSecret, code)

    resp, err := http.Get(url)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()

    var result struct {
        OpenID     string `json:"openid"`
        SessionKey string `json:"session_key"`
        ErrCode    int    `json:"errcode"`
        ErrMsg     string `json:"errmsg"`
    }

    err = json.NewDecoder(resp.Body).Decode(&result)
    if err != nil {
        return "", err
    }

    if result.ErrCode != 0 {
        return "", fmt.Errorf("微信API错误: %s", result.ErrMsg)
    }

    return result.OpenID, nil
}
```

## 📊 缓存策略

### Redis缓存服务

```go
// pkg/cache/redis.go
package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    "github.com/go-redis/redis/v8"
)

type RedisCache struct {
    client *redis.Client
}

func NewRedisCache(addr, password string, db int) *RedisCache {
    client := redis.NewClient(&redis.Options{
        Addr:     addr,
        Password: password,
        DB:       db,
    })

    return &RedisCache{client: client}
}

// 缓存用户信息
func (r *RedisCache) SetUser(ctx context.Context, userID uint, user interface{}) error {
    key := fmt.Sprintf("user:%d", userID)
    data, err := json.Marshal(user)
    if err != nil {
        return err
    }

    return r.client.Set(ctx, key, data, 30*time.Minute).Err()
}

// 获取用户信息
func (r *RedisCache) GetUser(ctx context.Context, userID uint) (*model.User, error) {
    key := fmt.Sprintf("user:%d", userID)
    data, err := r.client.Get(ctx, key).Bytes()
    if err != nil {
        return nil, err
    }

    var user model.User
    err = json.Unmarshal(data, &user)
    if err != nil {
        return nil, err
    }

    return &user, nil
}

// 缓存测试题目
func (r *RedisCache) SetQuestions(ctx context.Context, questions []model.MBTIQuestion) error {
    data, err := json.Marshal(questions)
    if err != nil {
        return err
    }

    return r.client.Set(ctx, "mbti:questions", data, 24*time.Hour).Err()
}

// 获取测试题目
func (r *RedisCache) GetQuestions(ctx context.Context) ([]model.MBTIQuestion, error) {
    data, err := r.client.Get(ctx, "mbti:questions").Bytes()
    if err != nil {
        return nil, err
    }

    var questions []model.MBTIQuestion
    err = json.Unmarshal(data, &questions)
    if err != nil {
        return nil, err
    }

    return questions, nil
}
```

## 🚀 部署配置

### Docker配置

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

EXPOSE 8080
CMD ["./main"]
```

### Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=mbti
      - DB_PASSWORD=mbti123
      - DB_NAME=mbti_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: mbti_db
      MYSQL_USER: mbti
      MYSQL_PASSWORD: mbti123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:6.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 📈 性能优化

### 1. 数据库优化
- 使用索引优化查询性能
- 使用连接池管理数据库连接
- 定期清理历史数据

### 2. 缓存优化
- 缓存热点数据（用户信息、测试题目）
- 实现缓存预热机制

### 3. 并发处理
- 使用goroutine处理并发请求
- 实现请求限流机制

## 🔍 监控与日志

### 日志配置

```go
// pkg/logger/logger.go
package logger

import (
    "go.uber.org/zap"
    "go.uber.org/zap/zapcore"
)

func NewLogger() *zap.Logger {
    config := zap.NewProductionConfig()
    config.EncoderConfig.TimeKey = "timestamp"
    config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
    
    logger, err := config.Build()
    if err != nil {
        panic(err)
    }
    
    return logger
}
```

### 健康检查

```go
// internal/controller/health.go
package controller

import (
    "github.com/gin-gonic/gin"
    "net/http"
    "time"
)

func HealthCheck(c *gin.Context) {
    c.JSON(http.StatusOK, gin.H{
        "status": "ok",
        "timestamp": time.Now().Unix(),
    })
}
```

## 📝 开发规范

### 1. 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 使用golint检查代码质量
- 编写单元测试和集成测试

### 2. API规范
- 使用RESTful API设计
- 统一响应格式
- 完善的错误处理
- API版本控制

### 3. 数据库规范
- 使用事务保证数据一致性
- 实现软删除机制
- 定期备份数据
- 监控慢查询

## 🚀 开发流程

### 1. 环境搭建
```bash
# 克隆项目
git clone https://github.com/your-org/mbti-backend.git
cd mbti-backend

# 安装依赖
go mod download

# 启动开发环境
docker-compose up -d

# 运行项目
go run cmd/server/main.go
```

### 2. 开发步骤
1. 创建数据模型
2. 实现Repository层
3. 实现Service层
4. 实现Controller层
5. 编写单元测试
6. 集成测试
7. 部署测试

### 3. 测试
```bash
# 运行单元测试
go test ./...

# 运行集成测试
go test -tags=integration ./...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 📚 参考资源

- [Gin框架文档](https://gin-gonic.com/docs/)
- [GORM文档](https://gorm.io/docs/)
- [Go官方文档](https://golang.org/doc/)
- [Docker文档](https://docs.docker.com/)

---

这份后端开发文档提供了轻量级的Golang后端架构设计，专注于单体服务部署，包括数据库设计、代码结构、核心功能实现、部署配置等。开发团队可以按照这个文档进行后端服务的开发。 