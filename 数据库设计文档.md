# MBTI小程序数据库设计文档

## 📑 目录

- [📊 数据库概述](#-数据库概述)
  - [技术选型](#技术选型)
  - [设计原则](#设计原则)
- [🗂️ 数据库表结构](#️-数据库表结构)
  - [1. 用户表 (users)](#1-用户表-users)
  - [2. 用户Token表 (user_tokens)](#2-用户token表-user_tokens)
  - [3. MBTI题目表 (mbti_questions)](#3-mbti题目表-mbti_questions)
  - [4. 题目选项表 (question_options)](#4-题目选项表-question_options)
  - [5. 测试记录表 (test_records)](#5-测试记录表-test_records)
  - [6. 测试答案表 (test_answers)](#6-测试答案表-test_answers)
  - [7. 报告模板表 (report_templates)](#7-报告模板表-report_templates)
  - [8. 用户报告表 (user_reports)](#8-用户报告表-user_reports)
  - [9. 商品表 (products)](#9-商品表-products)
  - [10. 订单表 (orders)](#10-订单表-orders)
  - [11. 优惠券表 (coupons)](#11-优惠券表-coupons)
  - [12. 用户优惠券表 (user_coupons)](#12-用户优惠券表-user_coupons)
  - [13. 系统配置表 (system_configs)](#13-系统配置表-system_configs)
  - [14. 意见反馈表 (feedbacks)](#14-意见反馈表-feedbacks)
  - [15. 操作日志表 (operation_logs)](#15-操作日志表-operation_logs)
  - [16. 人格轨迹表 (personality_trajectories)](#16-人格轨迹表-personality_trajectories)
  - [17. 分享图卡表 (share_cards)](#17-分享图卡表-share_cards)
  - [18. 匿名用户表 (anonymous_users)](#18-匿名用户表-anonymous_users)
  - [19. 积分表 (user_points)](#19-积分表-user_points)
  - [20. 兑换码表 (redeem_codes)](#20-兑换码表-redeem_codes)
  - [21. 兑换记录表 (redeem_records)](#21-兑换记录表-redeem_records)
  - [22. 成就表 (achievements)](#22-成就表-achievements)
  - [23. 用户成就表 (user_achievements)](#23-用户成就表-user_achievements)
  - [24. 邀请记录表 (invite_records)](#24-邀请记录表-invite_records)
- [🔗 表关系图](#-表关系图)
- [📊 索引设计](#-索引设计)
  - [主要索引](#主要索引)
  - [复合索引](#复合索引)
- [🔒 数据安全](#-数据安全)
  - [数据加密](#数据加密)
  - [数据备份](#数据备份)
  - [访问控制](#访问控制)
- [📈 性能优化](#-性能优化)
  - [查询优化](#查询优化)
  - [存储优化](#存储优化)
  - [缓存策略](#缓存策略)
- [🚀 部署建议](#-部署建议)
  - [数据库配置](#数据库配置)
  - [监控指标](#监控指标)

---

## 📊 数据库概述

### 技术选型
- **数据库**: MySQL 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

### 设计原则
- 遵循第三范式
- 合理使用索引
- 考虑扩展性
- 数据安全性

---

## 🗂️ 数据库表结构

### 1. 用户表 (users)

```sql
CREATE TABLE `users` (
  `id` varchar(32) NOT NULL COMMENT '用户ID',
  `open_id` varchar(64) NOT NULL COMMENT '微信openId',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信unionId',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `mbti_type` varchar(4) DEFAULT NULL COMMENT 'MBTI类型',
  `is_vip` tinyint(1) DEFAULT 0 COMMENT '是否VIP',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP过期时间',
  `test_count` int(11) DEFAULT 0 COMMENT '测试次数',
  `report_count` int(11) DEFAULT 0 COMMENT '报告数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-正常 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_mbti_type` (`mbti_type`),
  KEY `idx_vip_expire` (`vip_expire_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 2. 用户Token表 (user_tokens)

```sql
CREATE TABLE `user_tokens` (
  `id` varchar(32) NOT NULL COMMENT 'Token ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `token` varchar(255) NOT NULL COMMENT '访问令牌',
  `refresh_token` varchar(255) NOT NULL COMMENT '刷新令牌',
  `expires_in` int(11) NOT NULL COMMENT '过期时间(秒)',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-有效 0-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`token`),
  KEY `idx_expire_time` (`expire_time`),
  CONSTRAINT `fk_user_tokens_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户Token表';
```

### 3. MBTI题目表 (mbti_questions)

```sql
CREATE TABLE `mbti_questions` (
  `id` varchar(32) NOT NULL COMMENT '题目ID',
  `question` text NOT NULL COMMENT '题目内容',
  `type` varchar(2) NOT NULL COMMENT '题目类型 E/I, S/N, T/F, J/P',
  `question_type` varchar(20) DEFAULT 'standard' COMMENT '题目类型 standard/premium',
  `order_num` int(11) NOT NULL COMMENT '题目顺序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_question_type` (`question_type`),
  KEY `idx_order_num` (`order_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MBTI题目表';
```

### 4. 题目选项表 (question_options)

```sql
CREATE TABLE `question_options` (
  `id` varchar(32) NOT NULL COMMENT '选项ID',
  `question_id` varchar(32) NOT NULL COMMENT '题目ID',
  `option_key` varchar(10) NOT NULL COMMENT '选项键 A/B',
  `option_text` varchar(500) NOT NULL COMMENT '选项文本',
  `score` int(11) NOT NULL DEFAULT 1 COMMENT '分值',
  `order_num` int(11) NOT NULL COMMENT '选项顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_option_key` (`option_key`),
  CONSTRAINT `fk_question_options_question_id` FOREIGN KEY (`question_id`) REFERENCES `mbti_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目选项表';
```

### 5. 测试记录表 (test_records)

```sql
CREATE TABLE `test_records` (
  `id` varchar(32) NOT NULL COMMENT '测试ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `mbti_type` varchar(4) NOT NULL COMMENT 'MBTI类型',
  `e_score` int(11) NOT NULL DEFAULT 0 COMMENT '外向得分',
  `i_score` int(11) NOT NULL DEFAULT 0 COMMENT '内向得分',
  `s_score` int(11) NOT NULL DEFAULT 0 COMMENT '感觉得分',
  `n_score` int(11) NOT NULL DEFAULT 0 COMMENT '直觉得分',
  `t_score` int(11) NOT NULL DEFAULT 0 COMMENT '思考得分',
  `f_score` int(11) NOT NULL DEFAULT 0 COMMENT '情感得分',
  `j_score` int(11) NOT NULL DEFAULT 0 COMMENT '判断得分',
  `p_score` int(11) NOT NULL DEFAULT 0 COMMENT '知觉得分',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `duration` int(11) NOT NULL COMMENT '测试时长(秒)',
  `question_count` int(11) NOT NULL COMMENT '题目数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-完成 0-未完成',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_mbti_type` (`mbti_type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_test_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试记录表';
```

### 6. 测试答案表 (test_answers)

```sql
CREATE TABLE `test_answers` (
  `id` varchar(32) NOT NULL COMMENT '答案ID',
  `test_id` varchar(32) NOT NULL COMMENT '测试ID',
  `question_id` varchar(32) NOT NULL COMMENT '题目ID',
  `answer` varchar(10) NOT NULL COMMENT '答案选项',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_test_id` (`test_id`),
  KEY `idx_question_id` (`question_id`),
  CONSTRAINT `fk_test_answers_test_id` FOREIGN KEY (`test_id`) REFERENCES `test_records` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_test_answers_question_id` FOREIGN KEY (`question_id`) REFERENCES `mbti_questions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试答案表';
```

### 7. 报告模板表 (report_templates)

```sql
CREATE TABLE `report_templates` (
  `id` varchar(32) NOT NULL COMMENT '报告模板ID',
  `name` varchar(100) NOT NULL COMMENT '报告名称',
  `description` text COMMENT '报告描述',
  `type` varchar(20) NOT NULL COMMENT '报告类型 basic/premium/vip',
  `price` int(11) NOT NULL DEFAULT 0 COMMENT '价格(分)',
  `is_vip` tinyint(1) DEFAULT 0 COMMENT '是否VIP专享',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片',
  `estimated_time` int(11) DEFAULT 5 COMMENT '预计阅读时间(分钟)',
  `content_template` longtext COMMENT '内容模板(JSON)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_price` (`price`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报告模板表';
```

### 8. 用户报告表 (user_reports)

```sql
CREATE TABLE `user_reports` (
  `id` varchar(32) NOT NULL COMMENT '报告ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `template_id` varchar(32) NOT NULL COMMENT '报告模板ID',
  `mbti_type` varchar(4) NOT NULL COMMENT 'MBTI类型',
  `report_name` varchar(100) NOT NULL COMMENT '报告名称',
  `content` longtext COMMENT '报告内容(JSON)',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `read_time` int(11) DEFAULT 0 COMMENT '阅读时长(秒)',
  `last_read_time` datetime DEFAULT NULL COMMENT '最后阅读时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-正常 0-删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_mbti_type` (`mbti_type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_user_reports_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_reports_template_id` FOREIGN KEY (`template_id`) REFERENCES `report_templates` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户报告表';
```

### 9. 商品表 (products)

```sql
CREATE TABLE `products` (
  `id` varchar(32) NOT NULL COMMENT '商品ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `type` varchar(20) NOT NULL COMMENT '商品类型 vip/report',
  `price` int(11) NOT NULL COMMENT '原价(分)',
  `current_price` int(11) NOT NULL COMMENT '现价(分)',
  `duration` int(11) DEFAULT NULL COMMENT '有效期(天)',
  `features` text COMMENT '功能特性(JSON)',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门',
  `is_recommended` tinyint(1) DEFAULT 0 COMMENT '是否推荐',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-上架 0-下架',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
```

### 10. 订单表 (orders)

```sql
CREATE TABLE `orders` (
  `id` varchar(32) NOT NULL COMMENT '订单ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `product_id` varchar(32) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_type` varchar(20) NOT NULL COMMENT '商品类型',
  `amount` int(11) NOT NULL COMMENT '支付金额(分)',
  `discount_amount` int(11) DEFAULT 0 COMMENT '优惠金额(分)',
  `final_amount` int(11) NOT NULL COMMENT '最终金额(分)',
  `coupon_id` varchar(32) DEFAULT NULL COMMENT '优惠券ID',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态 pending/paid/cancelled/failed',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信交易号',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_orders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_orders_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

### 11. 优惠券表 (coupons)

```sql
CREATE TABLE `coupons` (
  `id` varchar(32) NOT NULL COMMENT '优惠券ID',
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `type` varchar(20) NOT NULL COMMENT '优惠券类型 discount/amount',
  `value` int(11) NOT NULL COMMENT '优惠值(分或折扣)',
  `min_amount` int(11) DEFAULT 0 COMMENT '最低消费金额(分)',
  `product_types` varchar(200) DEFAULT NULL COMMENT '适用商品类型(JSON)',
  `total_count` int(11) NOT NULL COMMENT '发放总数',
  `used_count` int(11) DEFAULT 0 COMMENT '已使用数量',
  `start_time` datetime NOT NULL COMMENT '生效时间',
  `end_time` datetime NOT NULL COMMENT '过期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';
```

### 12. 用户优惠券表 (user_coupons)

```sql
CREATE TABLE `user_coupons` (
  `id` varchar(32) NOT NULL COMMENT '用户优惠券ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `coupon_id` varchar(32) NOT NULL COMMENT '优惠券ID',
  `status` varchar(20) NOT NULL DEFAULT 'unused' COMMENT '状态 unused/used/expired',
  `use_time` datetime DEFAULT NULL COMMENT '使用时间',
  `order_id` varchar(32) DEFAULT NULL COMMENT '使用订单ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_user_coupons_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_coupons_coupon_id` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';
```

### 13. 系统配置表 (system_configs)

```sql
CREATE TABLE `system_configs` (
  `id` varchar(32) NOT NULL COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `type` varchar(20) DEFAULT 'string' COMMENT '配置类型 string/number/boolean/json',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

### 14. 意见反馈表 (feedbacks)

```sql
CREATE TABLE `feedbacks` (
  `id` varchar(32) NOT NULL COMMENT '反馈ID',
  `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '反馈类型 bug/feature/suggestion',
  `content` text NOT NULL COMMENT '反馈内容',
  `contact` varchar(100) DEFAULT NULL COMMENT '联系方式',
  `images` text COMMENT '图片URL列表(JSON)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态 pending/processing/resolved/closed',
  `reply` text COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_feedbacks_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='意见反馈表';
```

### 15. 操作日志表 (operation_logs)

```sql
CREATE TABLE `operation_logs` (
  `id` varchar(32) NOT NULL COMMENT '日志ID',
  `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID',
  `module` varchar(50) NOT NULL COMMENT '模块名称',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `request_data` text COMMENT '请求数据(JSON)',
  `response_data` text COMMENT '响应数据(JSON)',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-成功 0-失败',
  `error_message` text COMMENT '错误信息',
  `duration` int(11) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_operation_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

### 16. 人格轨迹表 (personality_trajectories)

```sql
CREATE TABLE `personality_trajectories` (
  `id` varchar(32) NOT NULL COMMENT '轨迹ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `test_id` varchar(32) NOT NULL COMMENT '测试ID',
  `mbti_type` varchar(4) NOT NULL COMMENT 'MBTI类型',
  `e_score` int(11) NOT NULL COMMENT '外向得分',
  `i_score` int(11) NOT NULL COMMENT '内向得分',
  `s_score` int(11) NOT NULL COMMENT '感觉得分',
  `n_score` int(11) NOT NULL COMMENT '直觉得分',
  `t_score` int(11) NOT NULL COMMENT '思考得分',
  `f_score` int(11) NOT NULL COMMENT '情感得分',
  `j_score` int(11) NOT NULL COMMENT '判断得分',
  `p_score` int(11) NOT NULL COMMENT '知觉得分',
  `confidence_level` decimal(3,2) DEFAULT 0.00 COMMENT '置信度',
  `test_date` date NOT NULL COMMENT '测试日期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_test_id` (`test_id`),
  KEY `idx_test_date` (`test_date`),
  CONSTRAINT `fk_personality_trajectories_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_personality_trajectories_test_id` FOREIGN KEY (`test_id`) REFERENCES `test_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人格轨迹表';
```

### 17. 分享图卡表 (share_cards)

```sql
CREATE TABLE `share_cards` (
  `id` varchar(32) NOT NULL COMMENT '图卡ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `report_id` varchar(32) DEFAULT NULL COMMENT '报告ID',
  `test_id` varchar(32) DEFAULT NULL COMMENT '测试ID',
  `share_code` varchar(32) NOT NULL COMMENT '分享码',
  `card_type` varchar(20) NOT NULL COMMENT '图卡类型 result/report',
  `card_data` longtext COMMENT '图卡数据(JSON)',
  `image_url` varchar(500) DEFAULT NULL COMMENT '图片URL',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `share_count` int(11) DEFAULT 0 COMMENT '分享次数',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-有效 0-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_code` (`share_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_report_id` (`report_id`),
  KEY `idx_test_id` (`test_id`),
  KEY `idx_expire_time` (`expire_time`),
  CONSTRAINT `fk_share_cards_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_share_cards_report_id` FOREIGN KEY (`report_id`) REFERENCES `user_reports` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_share_cards_test_id` FOREIGN KEY (`test_id`) REFERENCES `test_records` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享图卡表';
```

### 18. 匿名用户表 (anonymous_users)

```sql
CREATE TABLE `anonymous_users` (
  `id` varchar(32) NOT NULL COMMENT '匿名用户ID',
  `nick_name` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `device_id` varchar(64) DEFAULT NULL COMMENT '设备ID',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `is_bound` tinyint(1) DEFAULT 0 COMMENT '是否已绑定微信',
  `bound_user_id` varchar(32) DEFAULT NULL COMMENT '绑定的用户ID',
  `bound_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `last_active_time` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-正常 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_bound_user_id` (`bound_user_id`),
  KEY `idx_last_active_time` (`last_active_time`),
  CONSTRAINT `fk_anonymous_users_bound_user_id` FOREIGN KEY (`bound_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='匿名用户表';
```

### 19. 积分表 (user_points)

```sql
CREATE TABLE `user_points` (
  `id` varchar(32) NOT NULL COMMENT '积分记录ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `points` int(11) NOT NULL COMMENT '积分变动',
  `type` varchar(20) NOT NULL COMMENT '类型 earn/spend/expire',
  `source` varchar(50) NOT NULL COMMENT '来源 test/report/share/invite',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `balance` int(11) NOT NULL COMMENT '变动后余额',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-有效 0-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_user_points_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';
```

### 20. 兑换码表 (redeem_codes)

```sql
CREATE TABLE `redeem_codes` (
  `id` varchar(32) NOT NULL COMMENT '兑换码ID',
  `code` varchar(32) NOT NULL COMMENT '兑换码',
  `type` varchar(20) NOT NULL COMMENT '类型 points/vip/coupon',
  `value` int(11) NOT NULL COMMENT '兑换值',
  `max_uses` int(11) DEFAULT 1 COMMENT '最大使用次数',
  `used_count` int(11) DEFAULT 0 COMMENT '已使用次数',
  `start_time` datetime NOT NULL COMMENT '生效时间',
  `end_time` datetime NOT NULL COMMENT '过期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='兑换码表';
```

### 21. 兑换记录表 (redeem_records)

```sql
CREATE TABLE `redeem_records` (
  `id` varchar(32) NOT NULL COMMENT '兑换记录ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `code_id` varchar(32) NOT NULL COMMENT '兑换码ID',
  `code` varchar(32) NOT NULL COMMENT '兑换码',
  `type` varchar(20) NOT NULL COMMENT '类型 points/vip/coupon',
  `value` int(11) NOT NULL COMMENT '兑换值',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-成功 0-失败',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_code_id` (`code_id`),
  KEY `idx_code` (`code`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_redeem_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_redeem_records_code_id` FOREIGN KEY (`code_id`) REFERENCES `redeem_codes` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='兑换记录表';
```

### 22. 成就表 (achievements)

```sql
CREATE TABLE `achievements` (
  `id` varchar(32) NOT NULL COMMENT '成就ID',
  `name` varchar(100) NOT NULL COMMENT '成就名称',
  `description` varchar(500) DEFAULT NULL COMMENT '成就描述',
  `icon` varchar(500) DEFAULT NULL COMMENT '成就图标',
  `type` varchar(20) NOT NULL COMMENT '类型 test/report/share/invite',
  `condition_type` varchar(20) NOT NULL COMMENT '条件类型 count/score/time',
  `condition_value` int(11) NOT NULL COMMENT '条件值',
  `reward_points` int(11) DEFAULT 0 COMMENT '奖励积分',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成就表';
```

### 23. 用户成就表 (user_achievements)

```sql
CREATE TABLE `user_achievements` (
  `id` varchar(32) NOT NULL COMMENT '用户成就ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `achievement_id` varchar(32) NOT NULL COMMENT '成就ID',
  `progress` int(11) DEFAULT 0 COMMENT '当前进度',
  `is_completed` tinyint(1) DEFAULT 0 COMMENT '是否完成',
  `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
  `reward_claimed` tinyint(1) DEFAULT 0 COMMENT '是否已领取奖励',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_achievement` (`user_id`, `achievement_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_achievement_id` (`achievement_id`),
  KEY `idx_is_completed` (`is_completed`),
  CONSTRAINT `fk_user_achievements_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_achievements_achievement_id` FOREIGN KEY (`achievement_id`) REFERENCES `achievements` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户成就表';
```

### 24. 邀请记录表 (invite_records)

```sql
CREATE TABLE `invite_records` (
  `id` varchar(32) NOT NULL COMMENT '邀请记录ID',
  `inviter_id` varchar(32) NOT NULL COMMENT '邀请人ID',
  `invitee_id` varchar(32) DEFAULT NULL COMMENT '被邀请人ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `invitee_open_id` varchar(64) DEFAULT NULL COMMENT '被邀请人openId',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态 pending/accepted/expired',
  `reward_points` int(11) DEFAULT 0 COMMENT '奖励积分',
  `reward_claimed` tinyint(1) DEFAULT 0 COMMENT '是否已领取奖励',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_invitee_id` (`invitee_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_time` (`expire_time`),
  CONSTRAINT `fk_invite_records_inviter_id` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_invite_records_invitee_id` FOREIGN KEY (`invitee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请记录表';
```

---

## 🔗 表关系图

```
users (用户表)
├── user_tokens (用户Token表)
├── test_records (测试记录表)
│   └── test_answers (测试答案表)
├── user_reports (用户报告表)
├── orders (订单表)
├── user_coupons (用户优惠券表)
├── feedbacks (意见反馈表)
└── operation_logs (操作日志表)

mbti_questions (MBTI题目表)
├── question_options (题目选项表)
└── test_answers (测试答案表)

report_templates (报告模板表)
└── user_reports (用户报告表)

products (商品表)
└── orders (订单表)

coupons (优惠券表)
└── user_coupons (用户优惠券表)

system_configs (系统配置表) - 独立表
```

---

## 📊 索引设计

### 主要索引
1. **用户表**: open_id唯一索引，mbti_type普通索引
2. **Token表**: user_id索引，token索引，过期时间索引
3. **测试记录**: user_id索引，创建时间索引
4. **订单表**: user_id索引，状态索引，创建时间索引
5. **报告表**: user_id索引，模板ID索引

### 复合索引
```sql
-- 用户VIP状态查询
CREATE INDEX idx_users_vip_status ON users(is_vip, vip_expire_time);

-- 订单状态和时间查询
CREATE INDEX idx_orders_status_time ON orders(status, create_time);

-- 测试记录用户和时间查询
CREATE INDEX idx_test_records_user_time ON test_records(user_id, create_time);
```

---

## 🔒 数据安全

### 1. 数据加密
- 敏感字段使用AES加密
- Token使用JWT签名
- 密码使用bcrypt哈希

### 2. 数据备份
- 每日全量备份
- 实时增量备份
- 异地备份存储

### 3. 访问控制
- 数据库用户权限分离
- 只读用户用于查询
- 写用户用于更新

---

## 📈 性能优化

### 1. 查询优化
- 合理使用索引
- 避免全表扫描
- 使用分页查询

### 2. 存储优化
- 定期清理过期数据
- 压缩历史数据
- 分区表设计

### 3. 缓存策略
- Redis缓存热点数据
- 缓存用户信息
- 缓存测试题目

---

## 🚀 部署建议

### 1. 数据库配置
```ini
[mysqld]
# 字符集
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接数
max_connections = 1000
max_connect_errors = 10000

# 缓冲池
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
```

### 2. 监控指标
- 连接数监控
- 慢查询监控
- 磁盘空间监控
- 备份状态监控

---

这份数据库设计文档包含了MBTI小程序的所有数据表结构，你可以根据这个设计来创建数据库和表。设计考虑了数据完整性、性能优化和安全性，适合生产环境使用。 