# MBTI Go 后端 API 接口文档

## 📋 概述

本文档描述了 MBTI 性格测试后端服务的 API 接口规范。该服务基于 Golang + Gin + GORM + MySQL + Redis 构建，提供完整的 MBTI 测试功能、AI报告生成、充值系统、次数管理和分享功能。

**基础信息**:
- 基础URL: `http://localhost:8080`
- API版本: `v1`
- 数据格式: `JSON`
- 字符编码: `UTF-8`

## 🔐 认证方式

所有需要认证的接口都需要在请求头中携带 JWT Token：

```
Authorization: Bearer <token>
```

## 📊 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 🚀 API 接口

### 1. 健康检查

#### GET /health
系统健康检查

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": **********,
  "service": "mbti-backend"
}
```

---

### 2. 用户认证

#### POST /api/v1/auth/login
微信登录

**请求参数**:
```json
{
  "code": "微信授权码",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1,
    "country": "国家",
    "province": "省份",
    "city": "城市"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "nickname": "张三",
      "avatar": "https://example.com/avatar.jpg",
      "gender": 1,
      "email": "<EMAIL>",
      "phone": "13800138000",
      "city": "北京",
      "province": "北京",
      "country": "中国",
      "registerSource": "wechat",
      "analysisCredits": 5,
      "trackCredits": 2,
      "hasTakenTest": true,
      "createdAt": "2024-01-01T10:00:00Z",
      "updatedAt": "2024-01-01T10:00:00Z"
    }
  }
}
```

#### POST /api/v1/auth/logout
用户登出 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "登出成功"
  }
}
```

---

### 3. 用户信息

#### GET /api/v1/user/profile
获取用户信息 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "nickname": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1,
    "email": "<EMAIL>",
    "phone": "13800138000",
    "city": "北京",
    "province": "北京",
    "country": "中国",
    "registerSource": "wechat",
    "analysisCredits": 5,
    "trackCredits": 2,
    "hasTakenTest": true,
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T10:00:00Z"
  }
}
```

#### PUT /api/v1/user/profile
更新用户信息 **[需要认证]**

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatar": "新头像URL"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "nickname": "新昵称",
    "avatar": "新头像URL",
    "updatedAt": "2024-01-01T10:00:00Z"
  }
}
```

#### GET /api/v1/user/statistics
获取用户统计数据 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalTests": 10,
    "mostFrequentType": "INTJ",
    "testHistory": [
      {
        "date": "2024-01-01",
        "count": 2
      }
    ],
    "typeDistribution": [
      {
        "type": "INTJ",
        "count": 5
      }
    ]
  }
}
```

---

### 4. MBTI测试

#### GET /api/v1/mbti/questions
获取测试题目

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "question": "在社交场合中，你更倾向于：",
      "options": [
        {
          "id": 1,
          "text": "与很多人交谈",
          "dimension": "E",
          "score": 1
        },
        {
          "id": 2,
          "text": "与少数人深入交谈",
          "dimension": "I",
          "score": 1
        }
      ]
    }
  ]
}
```

#### POST /api/v1/mbti/submit
提交测试结果 **[需要认证]**

**请求参数**:
```json
{
  "answers": [
    {
      "questionId": 1,
      "optionId": 1
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "mbtiType": "INTJ",
    "description": "建筑师型人格",
    "score": {
      "E": 3,
      "I": 7,
      "N": 6,
      "S": 4,
      "T": 8,
      "F": 2,
      "J": 7,
      "P": 3
    }
  }
}
```

#### GET /api/v1/mbti/history
获取测试历史 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "mbtiType": "INTJ",
      "createdAt": "2024-01-01T10:00:00Z"
    }
  ]
}
```

---

### 5. 轨迹记录

#### GET /api/v1/trajectory/list
获取轨迹记录列表 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "mbtiType": "INTJ",
      "description": "轨迹描述",
      "createdAt": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### DELETE /api/v1/trajectory/delete/:id
删除轨迹记录 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "删除成功"
  }
}
```

---

### 6. AI报告

#### POST /api/v1/ai/report
生成AI报告 **[需要认证]**

**请求参数**:
```json
{
  "mbtiType": "INTJ",
  "type": "analysis"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "INTJ性格分析报告",
    "content": "报告内容...",
    "recommendations": {
      "career": ["职业建议"],
      "relationship": ["关系建议"]
    },
    "isPaid": false,
    "price": 19.90,
    "createdAt": "2024-01-01T10:00:00Z"
  }
}
```

#### GET /api/v1/ai/reports
获取AI报告列表 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "mbtiType": "INTJ",
      "type": "analysis",
      "title": "INTJ性格分析报告",
      "isPaid": false,
      "price": 19.90,
      "createdAt": "2024-01-01T10:00:00Z"
    }
  ]
}
```

---

### 7. 充值系统

#### GET /api/v1/products
获取商品列表

**查询参数**:
- `type` (可选): 商品类型 (analysis, track, credits)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "基础分析包",
      "description": "包含5次MBTI性格分析",
      "price": 19.90,
      "credit": 5,
      "type": "analysis"
    },
    {
      "id": 2,
      "name": "高级分析包",
      "description": "包含20次MBTI性格分析",
      "price": 69.90,
      "credit": 20,
      "type": "analysis"
    },
    {
      "id": 3,
      "name": "轨迹记录包",
      "description": "包含10次性格轨迹记录",
      "price": 29.90,
      "credit": 10,
      "type": "track"
    },
    {
      "id": 4,
      "name": "综合套餐",
      "description": "包含10次分析+5次轨迹",
      "price": 79.90,
      "credit": 0,
      "type": "credits"
    }
  ]
}
```

#### POST /api/v1/order/create
创建支付订单 **[需要认证]**

**请求参数**:
```json
{
  "productId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "ORDER_1234567890",
    "productId": 1,
    "productName": "基础分析包",
    "amount": 19.90,
    "status": "pending",
    "payType": "wechat",
    "createdAt": "2024-01-01T10:00:00Z"
  }
}
```

#### POST /api/v1/payment/callback
支付回调

**请求参数**:
```json
{
  "orderNo": "ORDER_1234567890",
  "transactionId": "WX123456789"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "支付成功"
  }
}
```

#### GET /api/v1/order/history
获取订单历史 **[需要认证]**

**查询参数**:
- `page` (可选): 页码，默认1
- `pageSize` (可选): 每页数量，默认10

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "ORDER_1234567890",
        "productId": 1,
        "productName": "基础分析包",
        "amount": 19.90,
        "status": "paid",
        "payType": "wechat",
        "transactionId": "WX123456789",
        "createdAt": "2024-01-01T10:00:00Z",
        "paidAt": "2024-01-01T10:05:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

### 8. 次数管理

#### GET /api/v1/credits
获取用户次数余额 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "analysisCredits": 5,
    "trackCredits": 2
  }
}
```

#### POST /api/v1/credits/use
使用次数 **[需要认证]**

**请求参数**:
```json
{
  "type": "analysis"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "消耗成功",
    "balance": {
      "analysisCredits": 4,
      "trackCredits": 2
    }
  }
}
```

---

### 9. 邀请码

#### POST /api/v1/invite/redeem
兑换邀请码 **[需要认证]**

**请求参数**:
```json
{
  "code": "INVITE123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "兑换成功"
  }
}
```

---

### 10. 分享功能

#### POST /api/v1/share/create
创建分享 **[需要认证]**

**请求参数**:
```json
{
  "type": "result",
  "content": "分享内容"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "shareId": "share_1234567890",
    "shareUrl": "https://example.com/share/share_1234567890"
  }
}
```

#### GET /api/v1/share/detail/:shareId
获取分享详情

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "shareId": "share_1234567890",
    "type": "result",
    "content": "分享内容",
    "imageUrl": "https://example.com/image.jpg",
    "shareUrl": "https://example.com/share/share_1234567890",
    "createdAt": "2024-01-01T10:00:00Z"
  }
}
```

#### GET /api/v1/share/list
获取分享列表 **[需要认证]**

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "shareId": "share_1234567890",
      "type": "result",
      "content": "分享内容",
      "shareUrl": "https://example.com/share/share_1234567890",
      "createdAt": "2024-01-01T10:00:00Z"
    }
  ]
}
```

---

## 📝 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔄 更新日志

### v1.2.0 (2024-01-01)
- ✨ 新增充值系统，支持商品购买和次数充值
- ✨ 新增次数管理，支持分析次数和轨迹次数
- ✨ 新增邀请码功能，支持免费次数兑换
- ✨ 新增注册来源字段，支持用户来源追踪
- 🗑️ 重构积分系统为次数系统
- 🗑️ 删除旧的积分相关接口

### v1.1.0 (2023-12-01)
- ✨ 新增AI报告生成功能
- ✨ 新增轨迹记录功能
- ✨ 新增分享功能

### v1.0.0 (2023-11-01)
- 🎉 初始版本发布
- ✨ 基础MBTI测试功能
- ✨ 用户认证系统
- ✨ 积分系统 