# 微信小程序项目 .gitignore

# 依赖目录
node_modules/
miniprogram_npm/
miniprogram/miniprogram_npm/

# 构建输出
dist/
build/
.temp/
.tmp/

# 微信开发者工具生成的文件
.idea/
.vscode/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage/
*.lcov

# nyc 测试覆盖率
.nyc_output

# 依赖锁定文件（可选，根据团队需求决定）
# package-lock.json
# yarn.lock

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存目录
.cache/
.parcel-cache/
.npm
.eslintcache

# TypeScript 编译输出
*.tsbuildinfo

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 图片和媒体文件（可选，根据项目需求决定）
# *.png
# *.jpg
# *.jpeg
# *.gif
# *.svg
# *.mp4
# *.mp3

# 微信小程序特定文件
project.private.config.json

# 测试相关
test-results/
coverage/
.nyc_output/

# 编辑器配置文件（可选）
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# 本地配置文件
config.local.js
config.local.json

# 文档生成
docs/_site/
docs/.vuepress/dist/

# 其他
*.tgz
*.tar 