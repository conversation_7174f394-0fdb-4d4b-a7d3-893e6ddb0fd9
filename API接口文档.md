# 心镜轨迹 API 接口文档

## 概述

本文档描述了心镜轨迹小程序的完整API接口，基于产品功能需求设计，支持MBTI性格测试、轨迹追踪、AI分析报告等核心功能。

## 基础信息

- **基础URL**: `http://47.101.35.203：8080`
- **认证方式**: Bear<PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 0,
  "message": "请求成功",
  "data": {},
  "success": true
}
```

## 1. 用户相关接口

### 1.1 微信登录
- **接口**: `POST /auth/login`
- **描述**: 用户微信登录，获取用户信息
- **请求参数**:
  ```json
  {
    "code": "微信登录code",
    "userInfo": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "gender": 1,
      "country": "国家",
      "province": "省份",
      "city": "城市"
    }
  }
  ```
- **响应数据**:
  ```json
  {
    "token": "用户认证token",
    "userInfo": {
      "id": "用户ID",
      "openid": "微信openid",
      "nickname": "昵称",
      "avatar": "头像",
      "totalTests": 5,
      "mostFrequentType": "INTJ",
      "createTime": "2024-01-01T00:00:00Z",
      "updateTime": "2024-01-01T00:00:00Z"
    }
  }
  ```

### 1.2 获取用户信息
- **接口**: `GET /user/profile`
- **描述**: 获取当前用户详细信息
- **响应数据**: 同登录接口的userInfo

### 1.3 更新用户信息
- **接口**: `PUT /user/update`
- **描述**: 更新用户信息
- **请求参数**: 用户信息字段（可选）

### 1.4 获取用户统计数据
- **接口**: `GET /user/statistics`
- **描述**: 获取用户测试统计数据
- **响应数据**:
  ```json
  {
    "totalTests": 10,
    "mostFrequentType": "INTJ",
    "testHistory": [
      {
        "date": "2024-01-01",
        "count": 2
      }
    ],
    "typeDistribution": [
      {
        "type": "INTJ",
        "count": 5
      }
    ]
  }
  ```

### 1.5 用户登出
- **接口**: `POST /auth/logout`
- **描述**: 用户登出，清除token

## 2. MBTI测试相关接口

### 2.1 获取测试题目
- **接口**: `GET /mbti/questions`
- **描述**: 获取40-60题MBTI标准题库
- **响应数据**:
  ```json
  [
    {
      "id": "q1",
      "question": "在社交场合中，你通常：",
      "dimension": "IE",
      "options": {
        "A": "主动与他人交谈",
        "B": "等待他人主动接近"
      }
    }
  ]
  ```

### 2.2 提交测试答案
- **接口**: `POST /mbti/submit`
- **描述**: 提交测试答案，计算MBTI类型
- **请求参数**:
  ```json
  {
    "answers": [
      {
        "questionId": "q1",
        "answer": "A"
      }
    ]
  }
  ```
- **响应数据**:
  ```json
  {
    "testId": "test_123"
  }
  ```

### 2.3 获取测试结果
- **接口**: `GET /mbti/result/{testId}`
- **描述**: 获取详细的测试结果
- **响应数据**:
  ```json
  {
    "id": "test_123",
    "mbtiType": "INTJ",
    "dimensions": {
      "IE": {
        "score": 65,
        "preference": "I"
      },
      "NS": {
        "score": 70,
        "preference": "N"
      },
      "TF": {
        "score": 60,
        "preference": "T"
      },
      "PJ": {
        "score": 55,
        "preference": "J"
      }
    },
    "keywords": ["战略家", "分析型", "独立"],
    "description": "INTJ型人格描述...",
    "createTime": "2024-01-01T00:00:00Z"
  }
  ```

### 2.4 获取测试历史
- **接口**: `GET /mbti/history`
- **描述**: 获取用户测试历史记录
- **请求参数**: `page`, `size`
- **响应数据**:
  ```json
  {
    "list": [测试结果数组],
    "total": 10,
    "page": 1,
    "size": 10
  }
  ```

## 3. 轨迹相关接口

### 3.1 获取轨迹列表
- **接口**: `GET /trajectory/list`
- **描述**: 获取节点流形式的测试轨迹
- **响应数据**:
  ```json
  [
    {
      "id": "traj_1",
      "mbtiType": "INTJ",
      "testDate": "2024-01-01",
      "summary": "最近一次测试结果",
      "isDeleted": false
    }
  ]
  ```

### 3.2 获取轨迹详情
- **接口**: `GET /trajectory/detail/{id}`
- **描述**: 获取特定轨迹的详细信息
- **响应数据**: 同测试结果格式

### 3.3 删除轨迹记录
- **接口**: `DELETE /trajectory/delete/{id}`
- **描述**: 删除不想保留的测试记录

### 3.4 获取轨迹摘要
- **接口**: `GET /trajectory/summary`
- **描述**: 获取首页展示的轨迹摘要
- **响应数据**:
  ```json
  {
    "recentType": "INTJ",
    "totalTests": 10,
    "lastTestDate": "2024-01-01"
  }
  ```

## 4. 报告相关接口

### 4.1 获取报告列表
- **接口**: `GET /report/list`
- **描述**: 获取AI分析报告列表
- **请求参数**: `type` (career/relationship/personal)
- **响应数据**:
  ```json
  [
    {
      "id": "report_1",
      "mbtiType": "INTJ",
      "type": "career",
      "title": "INTJ职业发展建议",
      "content": "报告内容...",
      "recommendations": ["建议1", "建议2"],
      "createTime": "2024-01-01T00:00:00Z"
    }
  ]
  ```

### 4.2 获取报告详情
- **接口**: `GET /report/detail/{reportId}`
- **描述**: 获取报告详细信息
- **响应数据**: 同报告列表格式

### 4.3 生成新报告
- **接口**: `POST /report/generate`
- **描述**: 基于MBTI类型生成新的AI分析报告
- **请求参数**:
  ```json
  {
    "mbtiType": "INTJ",
    "type": "career"
  }
  ```
- **响应数据**: 同报告格式

### 4.4 搜索报告
- **接口**: `GET /report/search`
- **描述**: 搜索报告内容
- **请求参数**: `keyword`
- **响应数据**: 报告数组

### 4.5 获取报告分类统计
- **接口**: `GET /report/categories`
- **描述**: 获取各类型报告数量
- **响应数据**:
  ```json
  {
    "career": 5,
    "relationship": 3,
    "personal": 2
  }
  ```

## 5. AI分析相关接口

### 5.1 生成职业建议
- **接口**: `POST /ai/career`
- **描述**: 生成基于MBTI的职业发展建议
- **请求参数**: `mbtiType`
- **响应数据**: AIReport格式

### 5.2 生成情感建议
- **接口**: `POST /ai/relationship`
- **描述**: 生成基于MBTI的情感关系建议
- **请求参数**: `mbtiType`
- **响应数据**: AIReport格式

### 5.3 生成个人分析
- **接口**: `POST /ai/personal`
- **描述**: 生成基于MBTI的个人成长分析
- **请求参数**: `mbtiType`
- **响应数据**: AIReport格式

## 6. 支付相关接口

### 6.1 创建订单
- **接口**: `POST /payment/create`
- **描述**: 创建支付订单
- **请求参数**:
  ```json
  {
    "productId": "product_1",
    "amount": 9900
  }
  ```
- **响应数据**:
  ```json
  {
    "id": "order_123",
    "productId": "product_1",
    "amount": 9900,
    "status": "pending",
    "createTime": "2024-01-01T00:00:00Z"
  }
  ```

### 6.2 验证支付
- **接口**: `POST /payment/verify`
- **描述**: 验证支付结果
- **请求参数**: `orderId`, `paymentResult`
- **响应数据**: `{ "success": true }`

### 6.3 获取支付历史
- **接口**: `GET /payment/history`
- **描述**: 获取用户支付历史
- **响应数据**: 订单数组

### 6.4 获取产品列表
- **接口**: `GET /payment/products`
- **描述**: 获取可购买的产品列表
- **响应数据**:
  ```json
  [
    {
      "id": "product_1",
      "name": "深度分析报告",
      "description": "包含职业和情感建议的详细报告",
      "price": 9900,
      "type": "report"
    }
  ]
  ```

## 7. 积分相关接口

### 7.1 获取积分余额
- **接口**: `GET /points/balance`
- **描述**: 获取用户积分余额
- **响应数据**: `{ "balance": 100 }`

### 7.2 获取积分历史
- **接口**: `GET /points/history`
- **描述**: 获取积分变动历史
- **响应数据**:
  ```json
  [
    {
      "id": "point_1",
      "type": "earn",
      "amount": 10,
      "description": "完成测试",
      "createTime": "2024-01-01T00:00:00Z"
    }
  ]
  ```

### 7.3 赚取积分
- **接口**: `POST /points/earn`
- **描述**: 通过完成动作赚取积分
- **请求参数**:
  ```json
  {
    "action": "complete_test",
    "amount": 10
  }
  ```
- **响应数据**: `{ "newBalance": 110 }`

### 7.4 消费积分
- **接口**: `POST /points/spend`
- **描述**: 消费积分购买服务
- **请求参数**:
  ```json
  {
    "action": "buy_report",
    "amount": 50
  }
  ```
- **响应数据**: `{ "newBalance": 60 }`

## 8. 分享相关接口

### 8.1 创建分享
- **接口**: `POST /share/create`
- **描述**: 创建分享内容
- **请求参数**:
  ```json
  {
    "type": "result",
    "content": "分享内容",
    "imageUrl": "图片URL"
  }
  ```
- **响应数据**:
  ```json
  {
    "shareId": "share_123",
    "shareUrl": "分享链接"
  }
  ```

### 8.2 获取分享详情
- **接口**: `GET /share/detail/{shareId}`
- **描述**: 获取分享内容详情
- **响应数据**:
  ```json
  {
    "id": "share_123",
    "type": "result",
    "content": "分享内容",
    "imageUrl": "图片URL",
    "createTime": "2024-01-01T00:00:00Z"
  }
  ```

### 8.3 获取分享列表
- **接口**: `GET /share/list`
- **描述**: 获取用户分享历史
- **响应数据**:
  ```json
  [
    {
      "id": "share_123",
      "type": "result",
      "title": "分享标题",
      "createTime": "2024-01-01T00:00:00Z"
    }
  ]
  ```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 用户未登录 |
| 1003 | 权限不足 |
| 2001 | 测试题目不存在 |
| 2002 | 测试结果不存在 |
| 3001 | 报告生成失败 |
| 4001 | 支付失败 |
| 5001 | 积分不足 |
| 9999 | 系统错误 |

## 注意事项

1. 所有请求需要在header中携带Authorization token
2. 时间格式统一使用ISO 8601格式
3. 金额单位为分（如9900表示99元）
4. 分页参数page从1开始
5. 文件上传需要先获取上传凭证
6. 支付相关接口需要微信支付商户认证

## 开发优先级

### P0 核心接口（必须实现）
- 用户登录认证
- MBTI测试流程
- 轨迹记录管理
- 基础报告功能

### 非P0 接口（后续上线）
- AI深度分析
- 积分系统
- 分享功能
- 支付功能 