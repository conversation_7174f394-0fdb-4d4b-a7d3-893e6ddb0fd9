---
description: 
globs: 
alwaysApply: false
---
# MBTI 微信小程序项目规则

## 产品概述

**产品名称**：心镜轨迹  
**定位**：一款基于 MBTI 性格测试的轻心理工具型小程序，培养自我认知和性格成长  
**目标用户**：
- 18-35 岁，关注自我认知与心理成长
- 有 MBTI/深入性格测试经历者
- 熟悉小程序体验，对统计抽象有需求

**核心价值**：通过专业的 MBTI 性格测试，帮助用户了解自我，促进性格成长

## 技术栈
- **框架**: 微信小程序原生框架
- **语言**: TypeScript
- **样式**: SCSS
- **依赖包**: 
  - `dayjs` - 日期处理
  - `wx-promise-pro` - 微信API Promise化
  - `miniprogram-api-typings` - TypeScript类型支持
- **后端**: Node.js + Supabase/MySQL
- **支付**: 微信支付商户认证
- **主题色**: `#006C68` 和 `#F5FDFC`

## 项目结构
```
miniprogram/
├── pages/                    # 页面目录
│   ├── home/                # 首页Tab（探索）
│   │   ├── home.ts
│   │   ├── home.wxml
│   │   ├── home.scss
│   │   └── home.json
│   ├── track/               # 轨迹页Tab
│   │   ├── track.ts
│   │   ├── track.wxml
│   │   ├── track.scss
│   │   └── track.json
│   ├── reports/             # 报告Tab
│   │   ├── reports.ts
│   │   ├── reports.wxml
│   │   ├── reports.scss
│   │   └── reports.json
│   ├── me/                  # 我的Tab
│   │   ├── me.ts
│   │   ├── me.wxml
│   │   ├── me.scss
│   │   └── me.json
│   ├── test/                # 测试相关页面
│   │   ├── test.ts          # 测试主页面
│   │   ├── test.wxml
│   │   ├── test.scss
│   │   ├── test.json
│   │   ├── start/           # 测试开始页面
│   │   │   ├── test-start.ts
│   │   │   ├── test-start.wxml
│   │   │   ├── test-start.scss
│   │   │   └── test-start.json
│   │   └── result/          # 测试结果页面
│   │       ├── test-result.ts
│   │       ├── test-result.wxml
│   │       ├── test-result.scss
│   │       └── test-result.json
│   ├── report/              # 单个报告页面
│   │   ├── report.ts
│   │   ├── report.wxml
│   │   ├── report.scss
│   │   └── report.json
│   ├── pay/                 # 支付相关页面
│   │   ├── pay.ts           # 支付主页面
│   │   ├── pay.wxml
│   │   ├── pay.scss
│   │   ├── pay.json
│   │   └── confirm/         # 支付确认页面
│   │       ├── pay-confirm.ts
│   │       ├── pay-confirm.wxml
│   │       ├── pay-confirm.scss
│   │       └── pay-confirm.json
│   ├── points/              # 积分页面
│   │   ├── points.ts
│   │   ├── points.wxml
│   │   ├── points.scss
│   │   └── points.json
│   ├── share/               # 分享页面
│   │   ├── share.ts
│   │   ├── share.wxml
│   │   ├── share.scss
│   │   └── share.json
│   ├── history/             # 历史记录页面
│   │   ├── history.ts
│   │   ├── history.wxml
│   │   ├── history.scss
│   │   └── history.json
│   ├── settings/            # 设置页面
│   │   ├── settings.ts
│   │   ├── settings.wxml
│   │   ├── settings.scss
│   │   └── settings.json
│   └── about/               # 关于页面
│       ├── about.ts
│       ├── about.wxml
│       ├── about.scss
│       └── about.json
├── components/              # 自定义组件
│   └── navigation-bar/      # 导航栏组件
├── utils/                   # 工具函数
│   ├── api.ts              # API接口封装
│   ├── request.ts          # 请求工具
│   ├── store.ts            # 状态管理
│   ├── anonymous.ts        # 匿名用户处理
│   └── util.ts             # 通用工具函数
├── styles/                  # 全局样式
├── images/                  # 图片资源
├── app.ts                   # 应用入口
├── app.json                 # 应用配置
└── app.scss                 # 全局样式
```

## TabBar 结构
根据 `app.json` 配置，应用包含4个主要Tab页面：
1. **首页** (`pages/home/<USER>
2. **轨迹** (`pages/track/track`) - 测试历史轨迹
3. **报告** (`pages/reports/reports`) - 测试报告列表
4. **我的** (`pages/me/me`) - 个人中心

## 核心功能模块

### 1️⃣ 探索（首页）
- **功能**：显示欢迎语 + 推荐描述推荐测试
- **特性**：如有历史测试，显示摘要回顾
- **优先级**：推荐测试为 **非P0** 功能，后续上线

### 2️⃣ 轨迹（纪录页）
- **功能**：节点流类图表，按时间排列测试结果
- **特性**：
  - 支持点击查看细节
  - 支持删除不想保留的测试
  - 各次结果保存性格类型 + 测试时间
- **优先级**：P0 核心功能

### 3️⃣ 报告（生成页）
- **功能**：接入 AI 分析，生成职业/情感建议
- **特性**：
  - 图文结合展示结果
  - 支持搜索或类别筛选
  - 长图生成功能为 **非P0**，后续上线
- **优先级**：AI 分析为 P0，长图生成为非P0

### 4️⃣ 我的
- **功能**：用户信息展示和个人数据
- **特性**：
  - 优先使用微信显示头像、昵称
  - 个人数据：总测试次数、最多出现性格
  - 成就系统为 **非P0**，后续上线
  - 设置区：使用说明/意见反馈/隐私协议

## 性格测试结构

### 测试配置
- **题目数**：40-60 题 MBTI 标准题库
- **分析维度**：IE / NS / TF / PJ 四组对绝值
- **输出结果**：16 类性格 + 关键词 + 图表 + 分析

### 报告模块结构
- **简要概览**：性格类型概述
- **四维特性分析**：IE/NS/TF/PJ 详细分析
- **AI 职业/情感建议**：基于性格类型的个性化建议
- **书单推荐**：选填功能

## 页面功能说明

### Tab页面
- **首页 (home)**: 展示欢迎语、推荐测试、历史测试摘要回顾
- **轨迹页 (track)**: 节点流图表展示测试历史，支持查看详情和删除
- **报告页 (reports)**: AI 分析报告列表，支持搜索和筛选
- **我的页面 (me)**: 用户信息、测试统计、设置入口

### 功能页面
- **测试主页面 (test)**: 测试介绍和入口
- **测试开始页 (test/start)**: 40-60题 MBTI 标准题库答题界面
- **测试结果页 (test/result)**: 16类性格结果、四维分析、AI建议展示
- **单个报告页 (report)**: 详细的测试报告内容，包含职业/情感建议
- **支付页面 (pay)**: 付费服务介绍
- **支付确认页 (pay/confirm)**: 付费报告购买流程
- **积分页面 (points)**: 积分系统相关功能
- **分享页面 (share)**: 测试结果分享功能
- **历史记录页 (history)**: 详细的测试历史记录
- **设置页面 (settings)**: 使用说明、意见反馈、隐私协议
- **关于页面 (about)**: 应用信息和帮助

## 开发优先级

### P0 核心功能（必须实现）
1. ✅ 基础测试流程（40-60题 MBTI 题库）
2. ✅ 测试结果展示（16类性格 + 四维分析）
3. ✅ 轨迹页面（历史记录节点流）
4. ✅ 基础报告功能
5. ✅ 用户信息展示
6. ✅ 基础设置功能

### 非P0 功能（后续上线）
1. 🔄 推荐测试功能
2. 🔄 AI 职业/情感建议
3. 🔄 长图生成功能
4. 🔄 成就系统
5. 🔄 社交扩展功能
6. 🔄 性格主页自定义

## TypeScript 规范

### 一般准则
- 使用严格的 TypeScript 配置
- 始终使用显式类型而不是 `any`
- 使用接口定义对象形状，使用类型定义联合/原始类型
- 优先使用 `const` 而不是 `let`，避免使用 `var`
- 使用早期返回减少嵌套
- 实现适当的错误处理与 try-catch 块

### 命名约定
- 使用 PascalCase 命名接口、类型和类: `UserInfo`, `ApiResponse`
- 使用 camelCase 命名变量、函数和属性: `userName`, `handleClick`
- 使用 UPPER_SNAKE_CASE 命名常量: `API_BASE_URL`, `MAX_RETRY_COUNT`
- 事件处理函数以 "handle" 开头: `handleTap`, `handleInputChange`

### 代码风格
```typescript
// 好的示例
interface UserInfo {
  id: string;
  name: string;
  avatar?: string;
}

const handleUserTap = (user: UserInfo): void => {
  if (!user.id) return;
  // 处理点击逻辑
};

// 不好的示例
interface userinfo {
  id: any;
  name: any;
}

function userTap(user: any) {
  if (user.id) {
    // 处理点击逻辑
  }
}
```

## 使用 Day.js 处理日期

### 导入和使用
```typescript
import dayjs from 'dayjs';

// 始终使用 dayjs 进行日期操作
const formatDate = (date: string | Date): string => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

const getRelativeTime = (date: string | Date): string => {
  return dayjs(date).fromNow();
};

const addDays = (date: string | Date, days: number): string => {
  return dayjs(date).add(days, 'day').format('YYYY-MM-DD');
};
```

### 日期常量
```typescript
// 常用日期格式
const DATE_FORMATS = {
  FULL: 'YYYY-MM-DD HH:mm:ss',
  DATE_ONLY: 'YYYY-MM-DD',
  TIME_ONLY: 'HH:mm:ss',
  MONTH_DAY: 'MM-DD',
  YEAR_MONTH: 'YYYY-MM'
} as const;
```

## 使用 wx-promise-pro 的微信 API

### 导入和设置
```typescript
import { wx } from 'wx-promise-pro';

// 始终使用 wx-promise-pro 获得更清晰的 async/await 代码
const handleApiRequest = async (): Promise<void> => {
  try {
    const result = await wx.request({
      url: 'https://api.example.com/data',
      method: 'GET'
    });
    
    this.setData({
      data: result.data
    });
  } catch (error) {
    console.error('API 请求失败:', error);
    wx.showToast({
      title: '请求失败',
      icon: 'error'
    });
  }
};
```

### 常用 API 模式
```typescript
// 文件操作
const handleImageUpload = async (): Promise<string> => {
  try {
    const chooseResult = await wx.chooseImage({ count: 1 });
    const tempFilePath = chooseResult.tempFilePaths[0];
    
    const uploadResult = await wx.uploadFile({
      url: 'https://upload.example.com',
      filePath: tempFilePath,
      name: 'file'
    });
    
    return uploadResult.data.url;
  } catch (error) {
    console.error('上传失败:', error);
    throw error;
  }
};

// 导航
const navigateToPage = async (url: string): Promise<void> => {
  try {
    await wx.navigateTo({ url });
  } catch (error) {
    console.error('导航失败:', error);
    // 回退到重定向
    await wx.redirectTo({ url });
  }
};
```

## SCSS 规范

### 文件组织
- 所有样式文件使用 `.scss` 扩展名
- 遵循 BEM 方法论的类命名
- 使用变量管理颜色、间距和排版
- 创建可复用的 mixins

### 命名约定
- 使用 kebab-case 命名类: `.user-card`, `.navigation-bar`
- 使用 BEM 方法论: `.block__element--modifier`
- 工具类前缀使用 `u-`: `.u-text-center`, `.u-margin-top`

### 主题色配置
```scss
// 主题色变量
$primary-color: #006C68;      // 主色调
$secondary-color: #F5FDFC;    // 辅助色
$text-primary: #333333;       // 主要文字
$text-secondary: #666666;     // 次要文字
$border-color: #E5E5E5;       // 边框色
$background-color: #F8F9FA;   // 背景色

// 组件样式
.user-card {
  padding: 20rpx;
  border-radius: 8rpx;
  background-color: #fff;
  border: 1rpx solid $border-color;
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16rpx;
  }
  
  &__title {
    font-size: 28rpx;
    color: $primary-color;
  }
}
```

## 微信小程序特定规则

### 页面结构
- 每个页面应该有: `.ts`, `.wxml`, `.scss`, 和 `.json` 文件
- 使用 Component() 构造函数进行页面注册
- 实现适当的生命周期方法

### 组件结构
- 使用 Component() 构造函数创建自定义组件
- 明确定义属性、数据和方法
- 使用适当的事件处理与 `triggerEvent`

### API 使用
- 所有微信小程序 API 使用 `wx-promise-pro`
- 适当处理 API 响应和错误
- 为 API 响应使用适当的 TypeScript 接口

### 页面结构示例
```typescript
// pages/example/example.ts
import { wx } from 'wx-promise-pro';
import dayjs from 'dayjs';

interface IPageData {
  title: string;
  list: Array<{ id: string; name: string; createTime: string }>;
  loading: boolean;
  currentDate: string;
}

Component<IPageData>({
  data: {
    title: '',
    list: [],
    loading: false,
    currentDate: ''
  },

  lifetimes: {
    attached() {
      this.setData({
        currentDate: dayjs().format('YYYY-MM-DD')
      });
      this.handleLoadData();
    }
  },

  methods: {
    async handleLoadData() {
      this.setData({ loading: true });
      
      try {
        const result = await wx.request({
          url: 'https://api.example.com/data'
        });
        
        const formattedList = result.data.map((item: any) => ({
          ...item,
          createTime: dayjs(item.createTime).format('MM-DD HH:mm')
        }));
        
        this.setData({
          list: formattedList,
          loading: false
        });
      } catch (error) {
        this.setData({ loading: false });
        wx.showToast({ title: '加载失败', icon: 'error' });
      }
    },

    async handleItemTap(e: WechatMiniprogram.TouchEvent) {
      const { id } = e.currentTarget.dataset;
      await wx.navigateTo({ url: `/pages/detail/detail?id=${id}` });
    }
  }
});
```

## 组件开发

### 组件结构示例
```typescript
// components/example/example.ts
import { wx } from 'wx-promise-pro';
import dayjs from 'dayjs';

interface IComponentData {
  value: string;
  formattedDate: string;
}

interface IComponentProperties {
  title: string;
  required?: boolean;
  date?: string;
}

Component<IComponentData, IComponentProperties>({
  properties: {
    title: {
      type: String,
      value: ''
    },
    required: {
      type: Boolean,
      value: false
    },
    date: {
      type: String,
      value: ''
    }
  },

  data: {
    value: '',
    formattedDate: ''
  },

  observers: {
    'date': function(date: string) {
      if (date) {
        this.setData({
          formattedDate: dayjs(date).format('YYYY-MM-DD')
        });
      }
    }
  },

  methods: {
    async handleInput(e: WechatMiniprogram.Input) {
      const { value } = e.detail;
      this.setData({ value });
      this.triggerEvent('change', { value });
    },

    async handleSubmit() {
      try {
        await wx.showModal({
          title: '确认',
          content: '确定要提交吗？'
        });
        this.triggerEvent('submit', { value: this.data.value });
      } catch (error) {
        console.log('用户取消操作');
      }
    }
  }
});
```

## 最佳实践

### 性能
- 通过批处理更新最小化 setData 调用
- 使用 `wx:if` 而不是 `hidden` 进行条件渲染
- 通过适当的尺寸优化图片加载
- 使用带 `wx:key` 的 `wx:for` 进行列表渲染

### 可访问性
- 添加适当的 `aria-label` 属性
- 使用语义化 HTML 元素
- 确保适当的颜色对比度
- 支持屏幕阅读器

### 错误处理
- 始终使用 async/await 的 try-catch
- 显示用户友好的错误消息
- 在适当的地方实现重试机制
- 记录错误用于调试

### 代码组织
- 保持函数小而专注
- 使用有意义的变量名
- 为复杂逻辑添加注释
- 遵循 DRY 原则
- 所有日期操作使用 dayjs
- 所有微信 API 使用 wx-promise-pro

## 开发工作流
1. 在 `miniprogram/pages/` 中创建新页面
2. 在 `miniprogram/components/` 中创建可复用组件
3. 在 `miniprogram/utils/` 中添加工具函数
4. 根据需要更新 `typings/` 中的类型定义
5. 在微信开发者工具中彻底测试
6. 使用 dayjs 进行日期格式化和操作
7. 使用 wx-promise-pro 获得更清晰的异步代码

## API 使用规范
- 使用 `wx-promise-pro` 进行 API 调用
- 统一错误处理机制
- 实现请求重试逻辑
- 使用 loading 状态管理

## 状态管理
- 页面数据使用 `data` 属性管理
- 全局数据使用 `getApp()` 获取
- 复杂状态考虑使用本地存储
- 避免频繁的 `setData` 调用

## 性能优化
- 图片资源压缩和懒加载
- 合理使用 `wx:if` 和 `hidden`
- 避免在 `setData` 中传递大量数据
- 使用 `wx:key` 优化列表渲染

## 用户体验
- 统一的加载状态展示
- 友好的错误提示
- 流畅的页面切换动画
- 响应式的交互反馈

## 测试规范
- 每个页面都要测试不同屏幕尺寸
- 测试网络异常情况
- 验证用户输入边界条件
- 确保支付流程的完整性

## 部署和发布
- 代码提交前进行 TypeScript 编译检查
- 使用微信开发者工具进行真机测试
- 配置合适的版本号和更新日志
- 遵循微信小程序审核规范

## 常见问题解决
1. **TypeScript 编译错误**: 检查类型定义和导入
2. **样式不生效**: 确认 SCSS 编译和类名正确
3. **API 调用失败**: 检查网络权限和错误处理
4. **页面跳转问题**: 确认路径配置和参数传递

## 开发工具配置
- 使用微信开发者工具
- 配置 TypeScript 编译选项
- 启用 SCSS 编译
- 设置合适的调试配置

## 版本控制
- 使用语义化版本号
- 编写清晰的提交信息
- 定期创建功能分支
- 代码审查和合并规范

## 安全考虑
- 用户数据加密存储
- API 接口权限验证
- 支付信息安全处理
- 防止敏感信息泄露

## 维护和更新
- 定期更新依赖包
- 监控应用性能指标
- 收集用户反馈
- 持续优化用户体验

## 未来拓展设想
- **成就系统**: 记录用户体验路径（造成创作力和累计量观示）
- **社交扩展**: 性格配对、互触社区、测试共帐分享
- **性格主页自定义**: 给用户构建自己性格人身应用
