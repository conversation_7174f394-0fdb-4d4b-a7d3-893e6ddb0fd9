# 🧠 心镜轨迹 · 小程序产品文档

## 一、产品概述

**产品名称**：心镜轨迹
**定位**：一款基于 MBTI 性格测试的轻心理工具型小程序，培养自我认知和性格成长
**目标用户**：

* 18-35 岁，关注自我认知与心理成长
* 有 MBTI/深入性格测试经历者
* 熟悉小程序体验，对统计抽象有需求

## 二、核心功能模块

### 1️⃣ 探索（首页）

* 显示欢迎言 + 推荐描述推荐测试
* 如有历史测试，显示摘要回顾
* 推荐测试为 **非P0** 功能

### 2️⃣ 轨迹（纪录页）

* 节点流类图表，按时间排列测试结果
* 支持点击查看细节 / 删除查看不想保留的测试
* 各次结果保存性格类型 + 测试时间

### 3️⃣ 报告（生成页）

* 接入 AI 分析：生成职业 / 情感 建议
* 图文结合展示结果
* 支持搜索或类别筛选
* 长图生成功能为 **非P0**，后续上线

### 4️⃣ 我的

* 用户显示优先使用微信显示头像、昵称
* 个人数据：总测试次数、最多出现性格
* 成就系统为 **非P0**，后续上线
* 设置区：使用说明 / 意见反馈 / 隐私协议

## 三、性格测试结构

* 题目数：40-60 题 MBTI 标准题库
* 分析维度：IE / NS / TF / PJ 四组对绝值
* 输出结果：16 类性格 + 关键词 + 图表 + 分析
* 报告模块：

  * 简要概览
  * 四维特性分析
  * AI 职业 / 情感建议
  * 书单推荐（选填）

## 四、技术和实现

* 前端：微信小程序原生框架（自定义 UI 组件）
* 后端：Node.js + Supabase / MySQL 等轻量级服务
* 支付：添加微信支付商户认证 + 订单分类支付模型
* 统一主题色： `#006C68` 和 `#F5FDFC`

## 五、未来拓展设想

* 成就系统：记录用户体验路径（造成创作力和累计量观示）
* 社交扩展：性格配对、互触社区、测试共帐分享
* 性格主页自定义：给用户构建自己性格人身应用

---
