{"description": "MBTI人格测试小程序项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript", "sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "ignoreUploadUnusedFiles": true, "compileHotReLoad": true, "skylineRenderEnable": true, "urlCheck": false, "es6": true, "preloadBackgroundData": false, "newFeature": false, "nodeModules": true, "autoAudits": false, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": true, "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "condition": true, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "latest", "packOptions": {"ignore": [{"value": ".eslintrc.js", "type": "file"}, {"value": ".prettier<PERSON>", "type": "file"}, {"value": "tsconfig.json", "type": "file"}, {"value": "package.json", "type": "file"}, {"value": "package-lock.json", "type": "file"}, {"value": "yarn.lock", "type": "file"}, {"value": "node_modules", "type": "folder"}, {"value": ".git", "type": "folder"}, {"value": "docs", "type": "folder"}, {"value": "server", "type": "folder"}], "include": []}, "appid": "wx6e1983453efc929b", "projectname": "mbti"}