# .gitignore 文件说明

## 概述
这个 `.gitignore` 文件专门为微信小程序项目设计，确保不必要的文件不会被提交到 Git 仓库中。

## 主要忽略内容

### 1. 依赖目录
```
node_modules/
miniprogram_npm/
miniprogram/miniprogram_npm/
```
- **node_modules/**: npm 安装的依赖包
- **miniprogram_npm/**: 微信开发者工具构建的 npm 包

### 2. 构建输出
```
dist/
build/
.temp/
.tmp/
```
- 构建工具生成的输出文件
- 临时构建目录

### 3. 微信开发者工具生成的文件
```
.idea/
.vscode/
*.swp
*.swo
*~
```
- IDE 配置文件
- 临时编辑文件
- 备份文件

### 4. 操作系统生成的文件
```
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
```
- macOS 系统文件
- Windows 系统文件

### 5. 日志和调试文件
```
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
```
- 各种日志文件
- 调试输出文件

### 6. 测试和覆盖率
```
lib-cov
coverage/
*.lcov
.nyc_output
```
- 测试覆盖率报告
- 测试结果文件

### 7. 环境变量文件
```
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
```
- 包含敏感信息的环境变量文件

### 8. 缓存目录
```
.cache/
.parcel-cache/
.npm
.eslintcache
```
- 各种工具的缓存文件

### 9. TypeScript 相关
```
*.tsbuildinfo
```
- TypeScript 编译信息文件

### 10. 微信小程序特定文件
```
project.private.config.json
```
- 微信开发者工具的私有配置文件（包含个人设置）

## 可选配置

### 依赖锁定文件
```bash
# package-lock.json
# yarn.lock
```
- 根据团队需求决定是否忽略
- 建议保留以确保依赖版本一致性

### 图片和媒体文件
```bash
# *.png
# *.jpg
# *.jpeg
# *.gif
# *.svg
# *.mp4
# *.mp3
```
- 根据项目需求决定是否忽略
- 如果图片较大，可以考虑忽略

### 编辑器配置
```bash
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
```
- 个人编辑器配置
- 团队可以统一配置

## 使用建议

### 1. 团队协作
- 保留 `package-lock.json` 或 `yarn.lock`
- 统一编辑器配置
- 共享必要的配置文件

### 2. 安全性
- 确保 `.env` 文件被忽略
- 检查是否包含敏感信息
- 定期审查忽略规则

### 3. 性能优化
- 忽略大型媒体文件（如果适用）
- 忽略构建输出
- 忽略缓存文件

### 4. 维护
- 定期更新忽略规则
- 根据项目发展调整
- 参考官方文档

## 常见问题

### Q: 为什么忽略 miniprogram_npm？
A: 这是微信开发者工具自动生成的，每次构建都会重新生成，不需要版本控制。

### Q: 是否应该忽略图片文件？
A: 根据项目需求决定。如果图片是项目资源，应该保留；如果是用户上传的，可以忽略。

### Q: 如何处理环境变量？
A: 创建 `.env.example` 文件作为模板，实际的 `.env` 文件被忽略。

### Q: 团队如何统一配置？
A: 可以创建 `.vscode/settings.json` 等配置文件，并提交到仓库中。

## 相关链接
- [Git 官方文档](https://git-scm.com/docs/gitignore)
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [Node.js .gitignore 模板](https://github.com/github/gitignore/blob/main/Node.gitignore) 