/**app.wxss**/
@import './styles/variables.scss';
@import './styles/iconfont.scss';

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 
/* 全局样式重置 */
page,
view,
text,
image,
button,
video,
map,
scroll-view,
swiper,
input,
textarea,
navigator {
  position: relative;
  background-origin: border-box;
  isolation: isolate;
}
page {
  height: 100%;
}
/* ===== CSS变量系统 - 支持黑夜模式 ===== */

/* 亮色主题变量 */
:root {
  /* 主色调 */
  --primary-color: #006C68;
  --primary-color-light: #008A85;
  --primary-color-dark: #004D4A;
  --primary-color-sub: #F5FDFC;
  --primary-color-sub-light: #FFFFFF;
  --primary-color-sub-dark: #E8F5F4;
  
  /* 语义化颜色 */
  --success-color: #52c41a;
  --success-color-light: #73d13d;
  --success-color-dark: #389e0d;
  --success-color-sub: #f6ffed;
  --warning-color: #faad14;
  --warning-color-light: #ffc53d;
  --warning-color-dark: #d48806;
  --warning-color-sub: #fff7e6;
  --error-color: #ff4d4f;
  --error-color-light: #ff7875;
  --error-color-dark: #d9363e;
  --error-color-sub: #fff2f0;
  --info-color: #1890ff;
  --info-color-light: #40a9ff;
  --info-color-dark: #096dd9;
  
  /* 文字颜色 */
  --text-color-primary: #1a1a1a;
  --text-color-secondary: #666666;
  --text-color-tertiary: #999999;
  --text-color-disabled: #cccccc;
  --text-color-inverse: #ffffff;
  --text-color-link: #006C68;
  --text-color-link-hover: #008A85;
  
  /* 背景颜色 */
  --background-color-primary: #ffffff;
  --background-color-secondary: #f8f9fa;
  --background-color-tertiary: #f0f2f5;
  --background-color-quaternary: #e8e8e8;
  --background-color-elevated: #ffffff;
  --background-color-overlay: rgba(0, 0, 0, 0.45);
  
  /* 边框颜色 */
  --border-color-primary: #e8e8e8;
  --border-color-secondary: #f0f0f0;
  --border-color-tertiary: #d9d9d9;
  --border-color-focus: #006C68;
  
  /* 阴影 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
  
  /* 圆角 */
  --border-radius-sm: 8rpx;
  --border-radius-md: 16rpx;
  --border-radius-lg: 24rpx;
  --border-radius-xl: 32rpx;
  --border-radius-full: 50%;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-2xl: 40rpx;
  --font-size-3xl: 48rpx;
  --font-size-4xl: 56rpx;
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 40rpx;
  --spacing-2xl: 48rpx;
  --spacing-3xl: 64rpx;
  --spacing-4xl: 80rpx;
  
  /* 动画时长 */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 动画缓动 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  
  /* Z-index层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* 黑夜主题变量 */
[data-theme="dark"] {
  /* 主色调 - 黑夜模式下保持品牌色 */
  --primary-color: #00A8A3;
  --primary-color-light: #00C4BE;
  --primary-color-dark: #008A85;
  --primary-color-sub: #0A1A1A;
  --primary-color-sub-light: #0F2A2A;
  --primary-color-sub-dark: #051515;
  
  /* 语义化颜色 - 黑夜模式下调整亮度 */
  --success-color: #6abe39;
  --success-color-light: #7ccd4a;
  --success-color-dark: #5aa32e;
  --success-color-sub: #0A1A1A;
  --warning-color: #ffb84d;
  --warning-color-light: #ffc666;
  --warning-color-dark: #e6a64d;
  --warning-color-sub: #0A1A1A;
  --error-color: #ff6b6b;
  --error-color-light: #ff8585;
  --error-color-dark: #e65555;
  --error-color-sub: #0A1A1A;
  --info-color: #4dabf7;
  --info-color-light: #66b5f8;
  --info-color-dark: #3d8bd9;
  
  /* 文字颜色 - 黑夜模式反转 */
  --text-color-primary: #ffffff;
  --text-color-secondary: #b3b3b3;
  --text-color-tertiary: #808080;
  --text-color-disabled: #4d4d4d;
  --text-color-inverse: #1a1a1a;
  --text-color-link: #00A8A3;
  --text-color-link-hover: #00C4BE;
  
  /* 背景颜色 - 黑夜模式深色系 */
  --background-color-primary: #1a1a1a;
  --background-color-secondary: #262626;
  --background-color-tertiary: #333333;
  --background-color-quaternary: #404040;
  --background-color-elevated: #2a2a2a;
  --background-color-overlay: rgba(0, 0, 0, 0.65);
  
  /* 边框颜色 - 黑夜模式深色系 */
  --border-color-primary: #404040;
  --border-color-secondary: #333333;
  --border-color-tertiary: #4d4d4d;
  --border-color-focus: #00A8A3;
  
  /* 阴影 - 黑夜模式调整 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.6);
}

/* 通用工具类 */
.u-text-center {
  text-align: center;
}
.u-text-left {
  text-align: left;
}
.u-text-right {
  text-align: right;
}
.u-flex {
  display: flex;
}
.u-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.u-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.u-flex-column {
  display: flex;
  flex-direction: column;
}
.u-flex-wrap { flex-wrap: wrap; }
.u-flex-1 { flex: 1; }
/* Grid布局工具类 */
.u-grid {
  display: grid;
}
.u-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}
.u-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}
.u-grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}
.u-gap-xs {
  gap: var(--spacing-xs);
}
.u-gap-sm {
  gap: var(--spacing-sm);
}
.u-gap-md {
  gap: var(--spacing-md);
}
.u-gap-lg {
  gap: var(--spacing-lg);
}
.u-gap-xl {
  gap: var(--spacing-xl);
}
.u-margin-top {
  margin-top: var(--spacing-md);
}
.u-margin-bottom {
  margin-bottom: var(--spacing-md);
}
.u-padding {
  padding: var(--spacing-md);
}
.u-border-radius {
  border-radius: var(--border-radius-md);
}
.u-shadow {
  box-shadow: var(--shadow-md);
}
/* 动画工具类 */
.u-animation-pause {
  animation-play-state: paused;
}
.u-animation-play {
  animation-play-state: running;
}
.u-outline {
  outline: 2rpx solid var(--primary-color);
  outline-offset: 4rpx;
}
/* 按钮样式重置 */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
}
button::after {
  border: none;
}
/* 输入框样式重置 */
input,
textarea {
  border: none;
  background: none;
}
/* 图片样式重置 */
image {
  display: block;
}
/* 链接样式重置 */
navigator {
  text-decoration: none;
  color: inherit;
}
/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
} 
/* 全局动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}
/* 动画工具类 */
.u-fade-in {
  animation: fadeIn var(--transition-normal) var(--ease-out);
}
.u-slide-in-up {
  animation: slideInUp var(--transition-normal) var(--ease-out);
}
.u-slide-in-down {
  animation: slideInDown var(--transition-normal) var(--ease-out);
}
.u-slide-in-left {
  animation: slideInLeft var(--transition-normal) var(--ease-out);
}
.u-slide-in-right {
  animation: slideInRight var(--transition-normal) var(--ease-out);
}
.u-scale-in {
  animation: scaleIn var(--transition-normal) var(--ease-out);
}
.u-rotate {
  animation: rotate 1s linear infinite;
}
.u-pulse {
  animation: pulse 2s var(--ease-in-out) infinite;
}
.u-bounce {
  animation: bounce 1s var(--ease-in-out);
}
/* 响应式工具类 */
@media (max-width: 750rpx) {
  .u-grid-2,
  .u-grid-3,
  .u-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .u-gap-md {
    gap: var(--spacing-sm);
  }
  
  .u-gap-lg {
    gap: var(--spacing-md);
  }
  
  .u-gap-xl {
    gap: var(--spacing-lg);
  }
} 
