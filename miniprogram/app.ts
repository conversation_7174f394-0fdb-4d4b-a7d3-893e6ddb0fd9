// app.ts
import { promisifyAll } from 'wx-promise-pro';
import { request } from './utils/request';
import { userAPI } from './utils/api';
import dayjs from 'dayjs';
import { loginManager, LoginStatus } from './utils/login-manager';

// 在微信小程序环境中，直接使用dayjs的基础功能
// 如果需要相对时间，可以使用dayjs的内置方法或自定义函数

// 初始化 wx-promise-pro
promisifyAll();

// 用户信息接口
export interface IUserInfo {
  id: string;
  nickName: string;
  avatarUrl: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  mbtiType?: string;
  registerSource: string;
  analysisCredits: number;
  trackCredits: number;
  hasTakenTest: boolean;
  createTime: string;
  lastLoginTime: string;
}

// 全局状态接口
export interface IGlobalData {
  userInfo: IUserInfo | null;
  isLoggedIn: boolean;
  token: string | null;
  loading: boolean;
  // 登录相关状态
  loginStatus: LoginStatus;
  isAutoLoginEnabled: boolean;
  // 状态更新监听器
  listeners: Array<(data: IGlobalData) => void>;
}

// 状态更新回调类型
export type StateUpdateCallback = (data: IGlobalData) => void;

// 应用配置接口
interface IAppOption {
  globalData: IGlobalData;
  // 全局方法
  initGlobalState(): Promise<void>;
  login(): Promise<any>;
  logout(): Promise<void>;
  checkLoginStatus(): boolean;
  getCurrentUser(): IUserInfo | null;
  updateUserInfo(updates: Partial<IUserInfo>): void;
  setMbtiType(mbtiType: string): void;
  checkAnalysisCredits(): boolean;
  checkTrackCredits(): boolean;
  getUserProfile(): Promise<WechatMiniprogram.UserInfo | null>;
  // 状态管理方法
  subscribe(callback: StateUpdateCallback): () => void;
  unsubscribe(callback: StateUpdateCallback): void;
  setState(updates: Partial<IGlobalData>): void;
  validateToken(token: string): Promise<boolean>;
  fetchUserInfo(): Promise<void>;
  // 新增方法
  handleLoginStatusChange(status: LoginStatus, data?: any): void;
  enableAutoLogin(): void;
  disableAutoLogin(): void;
  performSilentLogin(): Promise<void>;
  checkAndRefreshLogin(): Promise<void>;
  // 工具
  request: typeof request;
}

App<IAppOption>({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    token: null,
    loading: false,
    loginStatus: LoginStatus.NOT_LOGGED_IN,
    isAutoLoginEnabled: true,
    listeners: []
  },

  onLaunch() {
    console.log('应用启动');
    
    // 初始化全局状态
    this.initGlobalState();
    
    // 订阅登录状态变化
    loginManager.subscribe(this.handleLoginStatusChange.bind(this));
    
    // 启动时尝试静默登录
    setTimeout(() => {
      this.performSilentLogin();
    }, 100);
  },

  onShow() {
    console.log('应用进入前台');
    // 应用从后台进入前台时，检查登录状态
    setTimeout(() => {
      this.checkAndRefreshLogin();
    }, 100);
  },

  onHide() {
    console.log('应用进入后台');
  },

  onError(error: string) {
    console.error('应用错误:', error);
    // 可以在这里上报错误到监控系统
  },

  // 状态管理方法
  subscribe(callback: StateUpdateCallback): () => void {
    this.globalData.listeners.push(callback);
    
    // 返回取消订阅的函数
    return () => {
      const index = this.globalData.listeners.indexOf(callback);
      if (index > -1) {
        this.globalData.listeners.splice(index, 1);
      }
    };
  },

  unsubscribe(callback: StateUpdateCallback) {
    const index = this.globalData.listeners.indexOf(callback);
    if (index > -1) {
      this.globalData.listeners.splice(index, 1);
    }
  },

  setState(updates: Partial<IGlobalData>) {
    // 合并更新
    Object.assign(this.globalData, updates);
    
    // 通知所有监听器
    this.globalData.listeners.forEach(callback => {
      try {
        callback(this.globalData);
      } catch (error) {
        console.error('状态更新回调错误:', error);
      }
    });
  },

  // 处理登录状态变化
  handleLoginStatusChange(status: LoginStatus, data?: any) {
    console.log('登录状态变化:', status, data);
    
    this.setState({
      loginStatus: status,
      loading: status === LoginStatus.LOGGING_IN
    });
    
    switch (status) {
      case LoginStatus.LOGGED_IN:
        if (data?.user && data?.token) {
          const formattedUserInfo: IUserInfo = {
            id: String(data.user.id),
            nickName: data.user.nickname || data.user.nickName,
            avatarUrl: data.user.avatar || data.user.avatarUrl,
            gender: data.user.gender,
            country: data.user.country,
            province: data.user.province,
            city: data.user.city,
            mbtiType: data.user.mbtiType,
            registerSource: data.user.registerSource || 'wechat',
            analysisCredits: data.user.analysisCredits || 5,
            trackCredits: data.user.trackCredits || 2,
            hasTakenTest: data.user.hasTakenTest || false,
            createTime: data.user.createdAt || data.user.createTime,
            lastLoginTime: data.user.updatedAt || data.user.lastLoginTime || dayjs().format()
          };
          
          this.setState({
            userInfo: formattedUserInfo,
            isLoggedIn: true,
            token: data.token
          });
        }
        break;
        
      case LoginStatus.NOT_LOGGED_IN:
      case LoginStatus.LOGIN_FAILED:
        this.setState({
          userInfo: null,
          isLoggedIn: false,
          token: null
        });
        break;
        
      case LoginStatus.SESSION_EXPIRED:
        console.log('登录过期，尝试重新登录...');
        // 这里可以显示一个提示或者直接尝试重新登录
        break;
    }
  },

  // 静默登录（优化版本）
  async performSilentLogin() {
    if (!this.globalData.isAutoLoginEnabled) {
      console.log('自动登录已禁用');
      return;
    }
    
    try {
      console.log('开始静默登录检查...');
      const result = await loginManager.silentLogin();
      
      if (result.success) {
        console.log('静默登录成功:', result.type);
        // 登录状态由loginManager.subscribe自动处理
      } else {
        console.log('静默登录失败:', result.error);
        if (result.error === '没有本地token') {
          console.log('用户首次使用，等待手动登录');
        } else if (result.error === 'token已失效') {
          console.log('token已过期，用户需要重新登录');
        }
      }
    } catch (error) {
      console.error('静默登录异常:', error);
    }
  },

  // 检查并刷新登录
  async checkAndRefreshLogin() {
    try {
      // 检查登录状态
      const isLoggedIn = await loginManager.checkLoginStatus();
      
      if (this.globalData.isLoggedIn && !isLoggedIn) {
        console.log('检测到登录状态不一致，重新验证...');
        // 重新执行静默登录检查
        await this.performSilentLogin();
      } else if (isLoggedIn && !this.globalData.isLoggedIn) {
        console.log('检测到有效token，但全局状态未更新');
        // 触发状态更新
        await loginManager.performLoginCheck();
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  },

  // 启用自动登录
  enableAutoLogin() {
    this.setState({ isAutoLoginEnabled: true });
    wx.setStorageSync('autoLoginEnabled', true);
  },

  // 禁用自动登录
  disableAutoLogin() {
    this.setState({ isAutoLoginEnabled: false });
    wx.setStorageSync('autoLoginEnabled', false);
  },

  async initGlobalState() {
    try {
      console.log('初始化全局状态...');
      
      // 恢复自动登录设置
      const autoLoginEnabled = wx.getStorageSync('autoLoginEnabled');
      if (autoLoginEnabled !== undefined) {
        this.setState({ isAutoLoginEnabled: autoLoginEnabled });
      }
      
      // 检查本地token
      const storedToken = wx.getStorageSync('userToken');
      if (storedToken) {
        this.setState({ token: storedToken });
        console.log('发现本地token，将进行验证');
      }
      
      console.log('全局状态初始化完成');
    } catch (error) {
      console.error('初始化全局状态失败:', error);
    }
  },

  // 验证token
  async validateToken(token: string): Promise<boolean> {
    try {
      // 调用后端API验证token - 通过获取用户信息来验证
      await userAPI.getProfile();
      return true;
    } catch (error) {
      console.error('Token验证失败:', error);
      return false;
    }
  },

  // 获取用户信息
  async fetchUserInfo() {
    if (!this.globalData.token) return;

    try {
      const userInfo = await userAPI.getProfile();
      
      const formattedUserInfo: IUserInfo = {
        id: String(userInfo.id),
        nickName: userInfo.nickname,
        avatarUrl: userInfo.avatar,
        registerSource: userInfo.registerSource || 'wechat',
        analysisCredits: userInfo.analysisCredits || 5,
        trackCredits: userInfo.trackCredits || 2,
        hasTakenTest: userInfo.hasTakenTest || false,
        createTime: userInfo.createdAt,
        lastLoginTime: userInfo.updatedAt
      };

      this.setState({
        userInfo: formattedUserInfo,
        isLoggedIn: true
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
      await this.logout();
    }
  },

  // 用户授权登录（保持向后兼容）
  async login() {
    try {
      console.log('开始用户授权登录...');
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        return this.globalData.userInfo;
      } else {
        throw new Error(result.error || '登录失败');
      }
    } catch (error) {
      console.error('用户授权登录失败:', error);
      throw error;
    }
  },

  // 获取用户信息（保持向后兼容）
  async getUserProfile(): Promise<WechatMiniprogram.UserInfo | null> {
    try {
      const res = await wx.pro.getUserProfile({
        desc: '用于完善用户资料'
      });
      
          console.log('获取用户信息成功:', res.userInfo);
      return res.userInfo;
    } catch (error) {
          console.error('获取用户信息失败:', error);
      await wx.pro.showToast({
            title: '需要授权才能登录',
            icon: 'none'
          });
      return null;
        }
  },

  // 用户登出
  async logout() {
    try {
      console.log('开始退出登录...');
      await loginManager.logout();
    } catch (error) {
      console.error('退出登录失败:', error);
      throw error;
    }
  },

  // 检查登录状态
  checkLoginStatus(): boolean {
    return this.globalData.isLoggedIn && this.globalData.loginStatus === LoginStatus.LOGGED_IN;
  },

  // 获取当前用户信息
  getCurrentUser(): IUserInfo | null {
    return this.globalData.userInfo;
  },

  // 更新用户信息
  updateUserInfo(updates: Partial<IUserInfo>) {
    if (this.globalData.userInfo) {
      const updatedUserInfo = { ...this.globalData.userInfo, ...updates };
      this.setState({ userInfo: updatedUserInfo });
    }
  },

  // 设置MBTI类型
  setMbtiType(mbtiType: string) {
    this.updateUserInfo({ mbtiType });
  },

  // 检查VIP状态
  // 检查用户是否有足够的分析次数
  checkAnalysisCredits(): boolean {
    const userInfo = this.globalData.userInfo;
    return userInfo?.analysisCredits ? userInfo.analysisCredits > 0 : false;
  },

  // 检查用户是否有足够的轨迹次数
  checkTrackCredits(): boolean {
    const userInfo = this.globalData.userInfo;
    return userInfo?.trackCredits ? userInfo.trackCredits > 0 : false;
  },

  // 网络请求工具
  request
});

// 导出应用实例类型，方便其他文件使用
export type AppInstance = IAppOption;

// 导出默认应用实例
export default getApp<IAppOption>();