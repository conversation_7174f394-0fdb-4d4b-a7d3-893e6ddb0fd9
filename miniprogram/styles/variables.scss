// ===== 全局 SCSS 变量定义 =====
// 主题色变量
$primary-color: #006C68;           // 主色调
$primary-color-light: #008A85;     // 主色调亮色
$primary-color-dark: #004D4A;      // 主色调暗色
$secondary-color: #F5FDFC;         // 辅助色
$secondary-color-light: #FFFFFF;   // 辅助色亮色
$secondary-color-dark: #E8F5F4;    // 辅助色暗色

// 文字颜色
$text-primary: #333333;            // 主要文字
$text-secondary: #666666;          // 次要文字
$text-tertiary: #999999;           // 第三级文字
$text-disabled: #cccccc;           // 禁用文字
$text-inverse: #ffffff;            // 反色文字
$text-link: #006C68;               // 链接文字
$text-link-hover: #008A85;         // 链接悬停文字

// 背景颜色
$background-color: #F8F9FA;        // 背景色
$background-color-secondary: #f0f2f5; // 次要背景色
$background-color-tertiary: #e8e8e8;  // 第三级背景色
$white: #FFFFFF;                   // 白色

// 边框颜色
$border-color: #E5E5E5;            // 边框色
$border-color-secondary: #f0f0f0;  // 次要边框色
$border-color-tertiary: #d9d9d9;   // 第三级边框色
$border-color-focus: #006C68;      // 聚焦边框色

// 语义化颜色
$success-color: #52C41A;           // 成功色
$success-color-light: #73d13d;     // 成功色亮色
$success-color-dark: #389e0d;      // 成功色暗色
$success-color-sub: #f6ffed;       // 成功色辅助色

$warning-color: #FAAD14;           // 警告色
$warning-color-light: #ffc53d;     // 警告色亮色
$warning-color-dark: #d48806;      // 警告色暗色
$warning-color-sub: #fff7e6;       // 警告色辅助色

$error-color: #FF4D4F;             // 错误色
$error-color-light: #ff7875;       // 错误色亮色
$error-color-dark: #d9363e;        // 错误色暗色
$error-color-sub: #fff2f0;         // 错误色辅助色

$info-color: #1890ff;              // 信息色
$info-color-light: #40a9ff;        // 信息色亮色
$info-color-dark: #096dd9;         // 信息色暗色

// 阴影
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
$shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);

// 圆角
$border-radius-sm: 8rpx;
$border-radius-md: 16rpx;
$border-radius-lg: 24rpx;
$border-radius-xl: 32rpx;
$border-radius-full: 50%;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-2xl: 40rpx;
$font-size-3xl: 48rpx;
$font-size-4xl: 56rpx;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 间距
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 40rpx;
$spacing-2xl: 48rpx;
$spacing-3xl: 64rpx;
$spacing-4xl: 80rpx;

// 动画时长
$transition-fast: 0.15s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

// 动画缓动
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in: cubic-bezier(0.4, 0, 1, 1);

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070; 