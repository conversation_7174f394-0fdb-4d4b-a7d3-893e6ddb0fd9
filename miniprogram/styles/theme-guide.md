# CSS变量系统使用指南

## 概述

本项目使用CSS变量系统来管理主题样式，支持亮色和黑夜模式的无缝切换。所有颜色、间距、字体等设计token都通过CSS变量定义，确保设计的一致性和可维护性。

## 变量分类

### 1. 主色调 (Primary Colors)
```css
--primary-color: #006C68;           /* 主色调 */
--primary-color-light: #008A85;     /* 主色调亮色 */
--primary-color-dark: #004D4A;      /* 主色调暗色 */
--primary-color-sub: #F5FDFC;       /* 主色调辅助色 */
--primary-color-sub-light: #FFFFFF; /* 主色调辅助色亮色 */
--primary-color-sub-dark: #E8F5F4;  /* 主色调辅助色暗色 */
```

### 2. 语义化颜色 (Semantic Colors)
```css
--success-color: #52c41a;           /* 成功色 */
--success-color-light: #73d13d;     /* 成功色亮色 */
--success-color-dark: #389e0d;      /* 成功色暗色 */
--warning-color: #faad14;           /* 警告色 */
--warning-color-light: #ffc53d;     /* 警告色亮色 */
--warning-color-dark: #d48806;      /* 警告色暗色 */
--error-color: #ff4d4f;             /* 错误色 */
--error-color-light: #ff7875;       /* 错误色亮色 */
--error-color-dark: #d9363e;        /* 错误色暗色 */
--info-color: #1890ff;              /* 信息色 */
--info-color-light: #40a9ff;        /* 信息色亮色 */
--info-color-dark: #096dd9;         /* 信息色暗色 */
```

### 3. 文字颜色 (Text Colors)
```css
--text-color-primary: #1a1a1a;      /* 主要文字 */
--text-color-secondary: #666666;    /* 次要文字 */
--text-color-tertiary: #999999;     /* 第三级文字 */
--text-color-disabled: #cccccc;     /* 禁用文字 */
--text-color-inverse: #ffffff;      /* 反色文字 */
--text-color-link: #006C68;         /* 链接文字 */
--text-color-link-hover: #008A85;   /* 链接悬停文字 */
```

### 4. 背景颜色 (Background Colors)
```css
--background-color-primary: #ffffff;    /* 主要背景 */
--background-color-secondary: #f8f9fa; /* 次要背景 */
--background-color-tertiary: #f0f2f5;  /* 第三级背景 */
--background-color-elevated: #ffffff;   /* 提升背景 */
--background-color-overlay: rgba(0, 0, 0, 0.45); /* 遮罩背景 */
```

### 5. 边框颜色 (Border Colors)
```css
--border-color-primary: #e8e8e8;    /* 主要边框 */
--border-color-secondary: #f0f0f0;  /* 次要边框 */
--border-color-tertiary: #d9d9d9;   /* 第三级边框 */
--border-color-focus: #006C68;      /* 焦点边框 */
```

### 6. 阴影 (Shadows)
```css
--shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);   /* 小阴影 */
--shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);  /* 中阴影 */
--shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);  /* 大阴影 */
--shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.16); /* 超大阴影 */
```

### 7. 圆角 (Border Radius)
```css
--border-radius-sm: 8rpx;   /* 小圆角 */
--border-radius-md: 16rpx;  /* 中圆角 */
--border-radius-lg: 24rpx;  /* 大圆角 */
--border-radius-xl: 32rpx;  /* 超大圆角 */
--border-radius-full: 50%;  /* 全圆角 */
```

### 8. 字体大小 (Font Sizes)
```css
--font-size-xs: 20rpx;    /* 超小字体 */
--font-size-sm: 24rpx;    /* 小字体 */
--font-size-base: 28rpx;  /* 基础字体 */
--font-size-lg: 32rpx;    /* 大字体 */
--font-size-xl: 36rpx;    /* 超大字体 */
--font-size-2xl: 40rpx;   /* 2倍大字体 */
--font-size-3xl: 48rpx;   /* 3倍大字体 */
--font-size-4xl: 56rpx;   /* 4倍大字体 */
```

### 9. 字体粗细 (Font Weights)
```css
--font-weight-light: 300;     /* 细体 */
--font-weight-normal: 400;    /* 常规 */
--font-weight-medium: 500;    /* 中等 */
--font-weight-semibold: 600;  /* 半粗体 */
--font-weight-bold: 700;      /* 粗体 */
```

### 10. 行高 (Line Heights)
```css
--line-height-tight: 1.25;    /* 紧凑行高 */
--line-height-normal: 1.5;    /* 常规行高 */
--line-height-relaxed: 1.75;  /* 宽松行高 */
```

### 11. 间距 (Spacing)
```css
--spacing-xs: 8rpx;    /* 超小间距 */
--spacing-sm: 16rpx;   /* 小间距 */
--spacing-md: 24rpx;   /* 中间距 */
--spacing-lg: 32rpx;   /* 大间距 */
--spacing-xl: 40rpx;   /* 超大间距 */
--spacing-2xl: 48rpx;  /* 2倍大间距 */
--spacing-3xl: 64rpx;  /* 3倍大间距 */
--spacing-4xl: 80rpx;  /* 4倍大间距 */
```

### 12. 动画 (Animations)
```css
--transition-fast: 0.15s;     /* 快速过渡 */
--transition-normal: 0.3s;    /* 常规过渡 */
--transition-slow: 0.5s;      /* 慢速过渡 */
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);  /* 缓入缓出 */
--ease-out: cubic-bezier(0, 0, 0.2, 1);       /* 缓出 */
--ease-in: cubic-bezier(0.4, 0, 1, 1);        /* 缓入 */
```

## 使用方式

### 1. 在SCSS文件中使用
```scss
.my-component {
  // 使用颜色变量
  color: var(--text-color-primary);
  background-color: var(--background-color-primary);
  border: 1rpx solid var(--border-color-primary);
  
  // 使用间距变量
  padding: var(--spacing-md);
  margin: var(--spacing-lg);
  
  // 使用字体变量
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  
  // 使用圆角变量
  border-radius: var(--border-radius-md);
  
  // 使用阴影变量
  box-shadow: var(--shadow-md);
  
  // 使用动画变量
  transition: all var(--transition-normal) var(--ease-in-out);
}
```

### 2. 使用工具类
项目提供了丰富的工具类，可以直接在WXML中使用：

#### 文字颜色工具类
```html
<text class="u-text-primary">主要文字</text>
<text class="u-text-secondary">次要文字</text>
<text class="u-text-tertiary">第三级文字</text>
<text class="u-text-disabled">禁用文字</text>
<text class="u-text-inverse">反色文字</text>
<text class="u-text-link">链接文字</text>
```

#### 背景颜色工具类
```html
<view class="u-bg-primary">主要背景</view>
<view class="u-bg-secondary">次要背景</view>
<view class="u-bg-tertiary">第三级背景</view>
<view class="u-bg-elevated">提升背景</view>
```

#### 字体大小工具类
```html
<text class="u-text-xs">超小字体</text>
<text class="u-text-sm">小字体</text>
<text class="u-text-base">基础字体</text>
<text class="u-text-lg">大字体</text>
<text class="u-text-xl">超大字体</text>
<text class="u-text-2xl">2倍大字体</text>
<text class="u-text-3xl">3倍大字体</text>
<text class="u-text-4xl">4倍大字体</text>
```

#### 字体粗细工具类
```html
<text class="u-font-light">细体</text>
<text class="u-font-normal">常规</text>
<text class="u-font-medium">中等</text>
<text class="u-font-semibold">半粗体</text>
<text class="u-font-bold">粗体</text>
```

#### 间距工具类
```html
<view class="u-padding-xs">超小内边距</view>
<view class="u-padding-sm">小内边距</view>
<view class="u-padding-md">中内边距</view>
<view class="u-padding-lg">大内边距</view>
<view class="u-padding-xl">超大内边距</view>

<view class="u-margin-xs">超小外边距</view>
<view class="u-margin-sm">小外边距</view>
<view class="u-margin-md">中外边距</view>
<view class="u-margin-lg">大外边距</view>
<view class="u-margin-xl">超大外边距</view>
```

#### 圆角工具类
```html
<view class="u-border-radius-sm">小圆角</view>
<view class="u-border-radius-md">中圆角</view>
<view class="u-border-radius-lg">大圆角</view>
<view class="u-border-radius-xl">超大圆角</view>
<view class="u-border-radius-full">全圆角</view>
```

#### 阴影工具类
```html
<view class="u-shadow-sm">小阴影</view>
<view class="u-shadow-md">中阴影</view>
<view class="u-shadow-lg">大阴影</view>
<view class="u-shadow-xl">超大阴影</view>
```

#### 动画工具类
```html
<view class="u-transition">常规过渡</view>
<view class="u-transition-fast">快速过渡</view>
<view class="u-transition-slow">慢速过渡</view>
```

## 黑夜模式

### 自动切换
当用户切换到黑夜模式时，所有CSS变量会自动更新为对应的暗色值。无需手动修改样式代码。

### 黑夜模式下的颜色变化
- 主色调：从 `#006C68` 变为 `#00A8A3`
- 背景色：从白色系变为深色系
- 文字色：从深色系变为浅色系
- 边框色：从浅色系变为深色系
- 阴影：调整透明度和颜色

### 特殊处理
如果某些组件需要特殊的黑夜模式样式，可以使用 `[data-theme="dark"]` 选择器：

```scss
.my-component {
  // 默认样式
  background-color: var(--background-color-primary);
  color: var(--text-color-primary);
}

[data-theme="dark"] .my-component {
  // 黑夜模式特殊样式
  background-color: var(--background-color-elevated);
  border: 1rpx solid var(--border-color-primary);
}
```

## 最佳实践

### 1. 优先使用CSS变量
```scss
// ✅ 推荐
.my-button {
  background-color: var(--primary-color);
  color: var(--text-color-inverse);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
}

// ❌ 不推荐
.my-button {
  background-color: #006C68;
  color: #ffffff;
  padding: 24rpx;
  border-radius: 16rpx;
}
```

### 2. 使用语义化的变量名
```scss
// ✅ 推荐
.error-message {
  color: var(--error-color);
  background-color: var(--error-color-light);
}

// ❌ 不推荐
.error-message {
  color: #ff4d4f;
  background-color: #ff7875;
}
```

### 3. 组合使用工具类
```html
<!-- ✅ 推荐 -->
<view class="u-bg-elevated u-padding-md u-border-radius-lg u-shadow-md">
  <text class="u-text-primary u-font-semibold">标题</text>
  <text class="u-text-secondary u-text-sm">描述文字</text>
</view>
```

### 4. 响应式设计
```scss
.my-component {
  padding: var(--spacing-md);
  
  @media (max-width: 750rpx) {
    padding: var(--spacing-sm);
  }
}
```

## 注意事项

1. **不要硬编码颜色值**：所有颜色都应该使用CSS变量
2. **保持一致性**：使用统一的变量名和值
3. **测试黑夜模式**：确保所有组件在黑夜模式下都正常显示
4. **性能考虑**：CSS变量在现代浏览器中性能良好，无需担心性能问题
5. **兼容性**：CSS变量在微信小程序中完全支持

通过遵循这些指南，可以确保项目的样式系统的一致性和可维护性，同时为用户提供良好的黑夜模式体验。 