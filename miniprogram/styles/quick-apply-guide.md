# CSS变量系统快速应用指南

## 快速应用步骤

### 1. 页面样式更新模板

对于每个需要更新的页面，按照以下模板进行修改：

```scss
/* 页面名称样式 - 使用CSS变量系统 */

.page-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.page-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 内容区域
.content-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .content-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .page-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .content-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .page-container {
    background-color: var(--background-color-secondary);
  }
  
  .page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .content-card {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
}
```

### 2. 常见替换对照表

| 原值 | CSS变量 | 说明 |
|------|---------|------|
| `#006C68` | `var(--primary-color)` | 主色调 |
| `#F5FDFC` | `var(--primary-color-sub)` | 主色调浅色 |
| `#333333` | `var(--text-color-primary)` | 主要文字 |
| `#666666` | `var(--text-color-secondary)` | 次要文字 |
| `#999999` | `var(--text-color-tertiary)` | 第三级文字 |
| `#FFFFFF` | `var(--text-color-inverse)` | 反色文字 |
| `#F8F9FA` | `var(--background-color-secondary)` | 次要背景 |
| `#FFFFFF` | `var(--background-color-elevated)` | 卡片背景 |
| `#F5F5F5` | `var(--background-color-tertiary)` | 第三级背景 |
| `#E5E5E5` | `var(--border-color-primary)` | 主要边框 |
| `#F0F0F0` | `var(--border-color-secondary)` | 次要边框 |
| `20rpx` | `var(--spacing-xs)` | 超小间距 |
| `24rpx` | `var(--spacing-sm)` | 小间距 |
| `32rpx` | `var(--spacing-md)` | 中等间距 |
| `48rpx` | `var(--spacing-lg)` | 大间距 |
| `64rpx` | `var(--spacing-xl)` | 超大间距 |
| `16rpx` | `var(--border-radius-sm)` | 小圆角 |
| `24rpx` | `var(--border-radius-md)` | 中等圆角 |
| `32rpx` | `var(--border-radius-lg)` | 大圆角 |
| `28rpx` | `var(--font-size-base)` | 基础字体 |
| `32rpx` | `var(--font-size-lg)` | 大字体 |
| `36rpx` | `var(--font-size-xl)` | 超大字体 |
| `600` | `var(--font-weight-semibold)` | 半粗体 |
| `700` | `var(--font-weight-bold)` | 粗体 |

### 3. 快速替换命令

使用以下正则表达式进行批量替换：

```bash
# 颜色替换
s/#006C68/var(--primary-color)/g
s/#F5FDFC/var(--primary-color-sub)/g
s/#333333/var(--text-color-primary)/g
s/#666666/var(--text-color-secondary)/g
s/#999999/var(--text-color-tertiary)/g
s/#FFFFFF/var(--text-color-inverse)/g
s/#F8F9FA/var(--background-color-secondary)/g
s/#E5E5E5/var(--border-color-primary)/g

# 间距替换
s/20rpx/var(--spacing-xs)/g
s/24rpx/var(--spacing-sm)/g
s/32rpx/var(--spacing-md)/g
s/48rpx/var(--spacing-lg)/g
s/64rpx/var(--spacing-xl)/g

# 字体替换
s/28rpx/var(--font-size-base)/g
s/32rpx/var(--font-size-lg)/g
s/36rpx/var(--font-size-xl)/g

# 字重替换
s/font-weight: 600/font-weight: var(--font-weight-semibold)/g
s/font-weight: 700/font-weight: var(--font-weight-bold)/g
```

### 4. 待更新页面列表

#### 优先级高（核心功能）
1. `pages/pay/pay.scss` - 支付页面
2. `pages/report/report.scss` - 单个报告页面
3. `pages/history/history.scss` - 历史记录页面

#### 优先级中（功能页面）
4. `pages/pay/confirm/pay-confirm.scss` - 支付确认页面
5. `pages/share/share.scss` - 分享页面
6. `pages/about/about.scss` - 关于页面

#### 优先级低（辅助页面）
7. `pages/credits/credits.scss` - 积分页面
8. `components/navigation-bar/navigation-bar.wxss` - 导航栏组件

### 5. 检查清单

更新每个页面后，请检查：

- [ ] 所有硬编码颜色已替换为CSS变量
- [ ] 所有硬编码间距已替换为CSS变量
- [ ] 所有硬编码字体大小已替换为CSS变量
- [ ] 添加了黑夜模式特殊样式 `[data-theme="dark"]`
- [ ] 添加了响应式设计 `@media (max-width: 750rpx)`
- [ ] 测试了亮色和暗色主题效果
- [ ] 测试了移动端显示效果

### 6. 常见问题解决

#### 问题1：黑夜模式下颜色不协调
**解决**：检查是否在 `[data-theme="dark"]` 选择器中正确设置了背景色和边框色

#### 问题2：间距不一致
**解决**：确保使用统一的间距变量，避免混用不同的间距值

#### 问题3：字体大小不统一
**解决**：使用字体大小变量，避免直接使用数字

#### 问题4：主题切换后样式未更新
**解决**：确保页面在主题切换时重新渲染，或在 `attached` 生命周期中初始化主题

### 7. 测试建议

1. **功能测试**：确保页面功能正常
2. **视觉测试**：检查亮色和暗色主题的视觉效果
3. **响应式测试**：在不同屏幕尺寸下测试
4. **性能测试**：确保主题切换流畅

### 8. 完成标准

一个页面被认为完成CSS变量系统应用，需要满足：

- ✅ 所有样式使用CSS变量
- ✅ 支持黑夜模式
- ✅ 响应式设计
- ✅ 视觉一致性
- ✅ 功能完整性

按照这个指南，您可以快速完成剩余页面的CSS变量系统应用工作。 