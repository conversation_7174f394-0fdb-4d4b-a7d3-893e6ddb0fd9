interface IPageData {
  appInfo: {
    name: string;
    version: string;
    description: string;
    developer: string;
    contact: string;
  };
  shareInfo: {
    title: string;
    desc: string;
    path: string;
    imageUrl: string;
  };
  features: Array<{
    id: string;
    title: string;
    desc: string;
    icon: string;
  }>;
  contacts: Array<{
    id: string;
    title: string;
    desc: string;
    icon: string;
  }>;
}

interface ICustomMethods {
  handleContact(): Promise<void>;
  handleShare(): Promise<void>;
  handleRate(): Promise<void>;
  handleFeedback(): Promise<void>;
  onShareAppMessage(): any;
}

Page<IPageData, ICustomMethods>({
  data: {
    appInfo: {
      name: 'MBTI人格测试',
      version: '1.0.0',
      description: '专业的MBTI人格测试工具，帮助你了解自己的性格类型。',
      developer: 'MBTI团队',
      contact: '<EMAIL>'
    },
    shareInfo: {
      title: 'MBTI人格测试',
      desc: '专业的性格测试工具，发现真实的自己',
      path: '/pages/home/<USER>',
      imageUrl: 'icon-tianxie'
    },
    features: [
      {
        id: '1',
        title: 'MBTI性格测试',
        desc: '科学的性格分析工具',
        icon: 'icon-tianxie'
      },
      {
        id: '2',
        title: '个性化报告',
        desc: '详细的性格分析报告',
        icon: 'icon-tupian'
      },
      {
        id: '3',
        title: '性格轨迹',
        desc: '追踪性格变化趋势',
        icon: 'icon-dingwei'
      }
    ],
    contacts: [
      {
        id: '1',
        title: '邮箱反馈',
        desc: '<EMAIL>',
        icon: 'icon-xinfeng'
      },
      {
        id: '2',
        title: '微信客服',
        desc: '扫码添加客服微信',
        icon: 'icon-kefu'
      }
    ]
  },

  onLoad() {
    // 页面加载完成
  },

  async handleContact() {
    try {
      await wx.setClipboardData({
        data: this.data.appInfo.contact
      });
      
      wx.showToast({
        title: '邮箱已复制',
        icon: 'success'
      });
    } catch (error) {
      console.error('复制邮箱失败:', error);
    }
  },

  async handleShare() {
    try {
      await wx.pro.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      
      wx.pro.showToast({
        title: '请点击右上角分享',
        icon: 'none'
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  },

  onShareAppMessage() {
    return {
      title: this.data.shareInfo.title,
      path: this.data.shareInfo.path,
      imageUrl: this.data.shareInfo.imageUrl
    };
  },

  async handleRate() {
    try {
      // 引导用户去评分
      wx.showToast({
        title: '感谢您的支持！',
        icon: 'success'
      });
    } catch (error) {
      console.error('评分失败:', error);
    }
  },

  async handleFeedback() {
    try {
      // 引导用户反馈
      await wx.pro.showModal({
        title: '反馈建议',
        content: '请通过邮箱或微信客服联系我们',
        showCancel: false,
        confirmText: '我知道了'
      });
    } catch (error) {
      console.error('反馈失败:', error);
    }
  }
}); 