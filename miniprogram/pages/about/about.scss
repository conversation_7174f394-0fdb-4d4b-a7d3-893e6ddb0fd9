/* 关于页面样式 - 使用CSS变量系统 */

.about-page {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: var(--spacing-xl) var(--spacing-lg);
}

.app-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-3xl) 0;
  text-align: center;
  
  .app-logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    background: var(--background-color-elevated);
    border: 1rpx solid var(--border-color-primary);
  }
  
  .app-name {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  .app-version {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
  }
}

.app-description {
  background-color: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
  
  .description-text {
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    line-height: var(--line-height-relaxed);
  }
}

.developer-info {
  background-color: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) 0;
    border-bottom: 1rpx solid var(--border-color-secondary);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background: var(--background-color-tertiary);
      margin: 0 calc(-1 * var(--spacing-lg));
      padding: var(--spacing-lg);
      border-radius: var(--border-radius-sm);
    }
    
    .info-label {
      font-size: var(--font-size-base);
      color: var(--text-color-secondary);
      font-weight: var(--font-weight-medium);
    }
    
    .info-value {
      font-size: var(--font-size-base);
      color: var(--text-color-primary);
      font-weight: var(--font-weight-medium);
      
      &.contact-email {
        color: var(--primary-color);
        text-decoration: underline;
      }
    }
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-md);
  
  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-color-elevated);
    border: 2rpx solid var(--border-color-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal) var(--ease-in-out);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-md);
    }
    
    .btn-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: var(--spacing-sm);
      color: var(--text-color-tertiary);
    }
    
    .btn-text {
      font-size: var(--font-size-base);
      color: var(--text-color-primary);
      font-weight: var(--font-weight-medium);
    }
    
    &.share-btn {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      
      .btn-text {
        color: var(--text-color-inverse);
      }
      
      .btn-icon {
        color: var(--text-color-inverse);
      }
    }
    
    &.rate-btn {
      border-color: var(--success-color);
      background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
      
      .btn-text {
        color: var(--text-color-inverse);
      }
      
      .btn-icon {
        color: var(--text-color-inverse);
      }
    }
    
    &.feedback-btn {
      border-color: var(--warning-color);
      background: linear-gradient(135deg, var(--warning-color), var(--warning-color-light));
      
      .btn-text {
        color: var(--text-color-inverse);
      }
      
      .btn-icon {
        color: var(--text-color-inverse);
      }
    }
  }
}

.copyright {
  text-align: center;
  padding: var(--spacing-xl) 0;
  
  .copyright-text {
    font-size: var(--font-size-xs);
    color: var(--text-color-tertiary);
    line-height: var(--line-height-normal);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .about-page {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .app-info {
    padding: var(--spacing-2xl) 0;
    
    .app-logo {
      width: 100rpx;
      height: 100rpx;
    }
    
    .app-name {
      font-size: var(--font-size-xl);
    }
  }
  
  .app-description,
  .developer-info {
    padding: var(--spacing-md);
  }
  
  .info-item {
    padding: var(--spacing-md) 0;
    
    &:active {
      margin: 0 calc(-1 * var(--spacing-md));
      padding: var(--spacing-md);
    }
  }
  
  .action-btn {
    padding: var(--spacing-md);
    
    .btn-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: var(--spacing-xs);
    }
    
    .btn-text {
      font-size: var(--font-size-sm);
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .about-page {
    background-color: var(--background-color-secondary);
  }
  
  .app-logo {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .app-description,
  .developer-info {
    background-color: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .info-item {
    border-bottom-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-tertiary);
    }
  }
  
  .action-btn {
    background-color: var(--background-color-elevated);
    border-color: var(--border-color-primary);
    
    .btn-icon {
      color: var(--text-color-tertiary);
    }
    
    &.share-btn {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      
      .btn-text,
      .btn-icon {
        color: var(--text-color-inverse);
      }
    }
    
    &.rate-btn {
      border-color: var(--success-color);
      background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
      
      .btn-text,
      .btn-icon {
        color: var(--text-color-inverse);
      }
    }
    
    &.feedback-btn {
      border-color: var(--warning-color);
      background: linear-gradient(135deg, var(--warning-color), var(--warning-color-light));
      
      .btn-text,
      .btn-icon {
        color: var(--text-color-inverse);
      }
    }
  }
} 