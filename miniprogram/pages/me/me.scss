@import '../../styles/variables.scss';

.me-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: 0;
}

// 用户信息区域
.user-section {
  background: linear-gradient(135deg, $primary-color, lighten($primary-color, 8%));
  padding: 40rpx 20rpx;
  border-radius: 0 0 32rpx 32rpx;
  margin-bottom: 20rpx;
  
  // 已登录状态
  .user-info {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      margin-right: 24rpx;
      position: relative;
      
      .avatar-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
      }
      
      .avatar-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 48rpx;
        color: $white;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
      }
    }
    
    .user-details {
      flex: 1;
      
      .nickname {
        font-size: 36rpx;
        font-weight: 600;
        color: $white;
        margin-bottom: 8rpx;
      }
      
      .login-info {
        display: flex;
        align-items: center;
        gap: 8rpx;
        
        .iconfont {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
        
        .login-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
    
    .user-actions {
      .action-btn {
        background: rgba(255, 255, 255, 0.2);
        color: $white;
        border: 1rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 20rpx;
        padding: 16rpx 24rpx;
        font-size: 24rpx;
        font-weight: 500;
        
        &:active {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  // 未登录状态
  .login-prompt {
    display: flex;
    align-items: center;
    
    .login-avatar {
      width: 120rpx;
      height: 120rpx;
      margin-right: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      
      .iconfont {
        font-size: 48rpx;
        color: $white;
      }
    }
    
    .login-info {
      flex: 1;
      
      .login-title {
        font-size: 32rpx;
        font-weight: 600;
        color: $white;
        margin-bottom: 8rpx;
      }
      
      .login-desc {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.4;
      }
    }
    
    .login-btn {
      background: $white;
      color: $primary-color;
      border: none;
      border-radius: 24rpx;
      padding: 20rpx 32rpx;
      font-size: 28rpx;
      font-weight: 600;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      
      &:active {
        opacity: 0.9;
        transform: scale(0.98);
      }
    }
  }
}

// 数据统计
.stats-section {
  background: $white;
  border-radius: 16rpx;
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid $border-color;
  
  .stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    .stats-number {
      font-size: 48rpx;
      font-weight: 700;
      color: $primary-color;
      margin-bottom: 8rpx;
      line-height: 1;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

// 功能菜单
.menu-section {
  padding: 0 20rpx 20rpx;
  
  .menu-list {
    background: $white;
    border-radius: 16rpx;
    overflow: hidden;
    border: 1rpx solid $border-color;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    border-bottom: 1rpx solid $border-color;
    transition: background-color 0.2s ease;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background-color: lighten($secondary-color, 2%);
    }
    
    .menu-icon {
      width: 80rpx;
      height: 80rpx;
      background: $secondary-color;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      border: 1rpx solid $border-color;
      
      .iconfont {
        font-size: 36rpx;
        color: $primary-color;
      }
    }
    
    .menu-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .menu-title {
        font-size: 30rpx;
        font-weight: 500;
        color: $text-primary;
      }
      
      .menu-arrow {
        .iconfont {
          font-size: 24rpx;
          color: $text-secondary;
        }
      }
    }
  }
}

// 调试面板样式
.debug-section {
  margin: 0 20rpx 20rpx;
  
  .debug-toggle-btn {
    width: 100%;
    background: $text-secondary;
    color: $white;
    border: none;
    border-radius: 16rpx;
    padding: 20rpx;
    font-size: 24rpx;
    margin-bottom: 16rpx;
    
    &:active {
      opacity: 0.8;
    }
  }
  
  .debug-panel {
    background: $white;
    border-radius: 16rpx;
    padding: 24rpx;
    border: 1rpx solid $border-color;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    
    .debug-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 20rpx;
      text-align: center;
    }
    
    .debug-info {
      margin-bottom: 24rpx;
      
      .debug-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 0;
        border-bottom: 1rpx solid $border-color;
        
        &:last-child {
          border-bottom: none;
        }
        
        .debug-label {
          font-size: 24rpx;
          color: $text-secondary;
          font-weight: 500;
        }
        
        .debug-value {
          font-size: 24rpx;
          font-weight: 500;
          
          &.success {
            color: $success-color;
          }
          
          &.error {
            color: $error-color;
          }
        }
      }
    }
    
    .debug-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      
      .debug-btn {
        flex: 1;
        min-width: 200rpx;
        background: $primary-color;
        color: $white;
        border: none;
        border-radius: 12rpx;
        padding: 16rpx 20rpx;
        font-size: 22rpx;
        
        &.danger {
          background: $error-color;
        }
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .user-section {
    padding: 30rpx 16rpx;
  }
  
  .stats-section,
  .menu-section,
  .debug-section {
    margin-left: 16rpx;
    margin-right: 16rpx;
  }
  
  .menu-item {
    padding: 24rpx 20rpx;
  }
} 