<view class="me-container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <!-- 已登录状态 -->
    <view wx:if="{{isLoggedIn}}" class="user-info">
      <view class="avatar">
        <image wx:if="{{userInfo.avatarUrl}}" class="avatar-image" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
        <text wx:if="{{!userInfo.avatarUrl}}" class="iconfont icon-tongxunlu avatar-icon"></text>
      </view>
      <view class="user-details">
        <view class="nickname">{{userInfo.nickName || '未登录'}}</view>
        <view class="login-info">
          <text class="iconfont icon-chenggong"></text>
          <text class="login-text">已登录</text>
        </view>
      </view>
      <view class="user-actions">
        <button class="action-btn" bindtap="handleEditProfile">编辑资料</button>
      </view>
    </view>
    
    <!-- 未登录状态 -->
    <view wx:else class="login-prompt">
      <view class="login-avatar">
        <text class="iconfont icon-mimadenglu"></text>
      </view>
      <view class="login-info">
        <view class="login-title">登录心镜轨迹</view>
        <view class="login-desc">体验完整的MBTI性格测试功能</view>
      </view>
      <button class="login-btn" bindtap="handleLogin">立即登录</button>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="stats-item">
      <text class="stats-number">{{testCount || 0}}</text>
      <text class="stats-label">测试次数</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{favoriteCount || 0}}</text>
      <text class="stats-label">收藏报告</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{shareCount || 0}}</text>
      <text class="stats-label">分享次数</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-list">
      <view class="menu-item" wx:for="{{menuList}}" wx:key="id" bindtap="handleMenuTap" data-path="{{item.path}}">
        <view class="menu-icon">
          <text class="iconfont {{item.icon}}"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">{{item.title}}</view>
          <view class="menu-arrow">
            <text class="iconfont icon-gengduo"></text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 调试面板 -->
  <view class="debug-section">
    <button class="debug-toggle-btn" bindtap="toggleDebug">
      {{showDebug ? '隐藏调试' : '显示调试'}}
    </button>
    
    <view class="debug-panel" wx:if="{{showDebug}}">
      <view class="debug-title">登录管理器调试信息</view>
      
      <view class="debug-info">
        <view class="debug-item">
          <text class="debug-label">登录状态:</text>
          <text class="debug-value {{loginStatus === 'LOGGED_IN' ? 'success' : 'error'}}">{{loginStatus}}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">本地Token:</text>
          <text class="debug-value {{debugInfo.hasToken ? 'success' : 'error'}}">{{debugInfo.hasToken ? '存在' : '不存在'}}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">Token有效:</text>
          <text class="debug-value {{debugInfo.tokenValidated ? 'success' : 'error'}}">{{debugInfo.tokenValidated ? '有效' : '无效'}}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">Session有效:</text>
          <text class="debug-value {{debugInfo.sessionValid ? 'success' : 'error'}}">{{debugInfo.sessionValid ? '有效' : '无效'}}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">最后检查:</text>
          <text class="debug-value">{{debugInfo.lastLoginCheck}}</text>
        </view>
        <view class="debug-item" wx:if="{{debugInfo.errorInfo}}">
          <text class="debug-label">错误信息:</text>
          <text class="debug-value error">{{debugInfo.errorInfo}}</text>
        </view>
      </view>
      
      <view class="debug-actions">
        <button class="debug-btn" bindtap="testSilentLogin">测试静默登录</button>
        <button class="debug-btn" bindtap="testLoginCheck">测试登录检查</button>
        <button class="debug-btn" bindtap="testGetProfile">测试获取用户信息</button>
        <button class="debug-btn danger" bindtap="clearToken">清除Token</button>
      </view>
  </view>
  </view>
</view> 