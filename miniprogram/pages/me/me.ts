import type { IUserInfo } from '../../app';
import { loginManager, LoginStatus, LoginType } from '../../utils/login-manager';
import { userAPI } from '../../utils/api';
import { tokenDebug } from '../../utils/token-debug';

interface IPageData {
  userInfo: IUserInfo | null;
  isLoggedIn: boolean;
  loginStatus: LoginStatus;
  isLoading: boolean;
  // 调试信息
  debugInfo: {
    hasToken: boolean;
    tokenValidated: boolean;
    sessionValid: boolean;
    lastLoginCheck: string;
    errorInfo: string;
  };
  showDebug: boolean;
  menuList: Array<{
    id: string;
    title: string;
    desc: string;
    icon: string;
    path: string;
  }>;
  testCount: number;
  favoriteCount: number;
  shareCount: number;
}

interface ICustomMethods {
  handleLogin(): Promise<void>;
  handleMenuTap(e: WechatMiniprogram.TouchEvent): Promise<void>;
  handleEditProfile(): Promise<void>;
  handleUpgradeCredits(): Promise<void>;
  updatePageData(): void;
  checkAutoLogin(): void;
  loadUserStats(): void;
  // 调试方法
  toggleDebug(): void;
  testSilentLogin(): Promise<void>;
  testLoginCheck(): Promise<void>;
  clearToken(): void;
  testGetProfile(): Promise<void>;
  updateDebugInfo(): Promise<void>;
  showTokenReport(): Promise<void>;
}

Page<IPageData, ICustomMethods>({
  data: {
    userInfo: null,
    isLoggedIn: false,
    loginStatus: LoginStatus.NOT_LOGGED_IN,
    isLoading: false,
    // 调试信息
    debugInfo: {
      hasToken: false,
      tokenValidated: false,
      sessionValid: false,
      lastLoginCheck: '',
      errorInfo: ''
    },
    showDebug: false,
    menuList: [
      {
        id: 'reports',
        title: '我的报告',
        desc: '查看所有测试报告',
        icon: 'icon-tupian',
        path: '/pages/reports/reports'
      },
      {
        id: 'history', 
        title: '测试历史',
        desc: '查看历史测试记录',
        icon: 'icon-shijian',
        path: '/pages/history/history'
      },
      {
        id: 'credits',
        title: '次数管理',  
        desc: '查看分析次数和轨迹次数',
        icon: 'icon-liwu',
        path: '/pages/credits/credits'
      },
      {
        id: 'settings',
        title: '设置',
        desc: '账号设置和隐私管理',
        icon: 'icon-shezhi',
        path: '/pages/settings/settings'
      },
      {
        id: 'about',
        title: '关于我们',
        desc: '了解更多信息',
        icon: 'icon-yiwen',
        path: '/pages/about/about'
      }
    ],
    testCount: 0,
    favoriteCount: 0,
    shareCount: 0
  },

  onLoad() {
    console.log('我的页面加载');
    this.updatePageData();
    
    // 订阅登录状态变化
    const unsubscribe = loginManager.subscribe((status: LoginStatus, data?: any) => {
      console.log('登录状态变化:', status);
      this.setData({ 
        loginStatus: status,
        isLoading: status === LoginStatus.LOGGING_IN
      });
      
      if (status === LoginStatus.LOGGED_IN && data?.user) {
        this.updatePageData();
      } else if (status === LoginStatus.NOT_LOGGED_IN) {
        this.setData({
          userInfo: null,
          isLoggedIn: false
        });
      }
    });
    
    // 保存取消订阅函数，在页面销毁时调用
    (this as any).unsubscribeLoginStatus = unsubscribe;
  },

  onShow() {
    console.log('我的页面显示');
    this.updatePageData();
    this.updateDebugInfo();
    this.loadUserStats();
    
    // 检查是否需要自动登录提示
    this.checkAutoLogin();
  },

  onUnload() {
    // 取消订阅
    if ((this as any).unsubscribeLoginStatus) {
      (this as any).unsubscribeLoginStatus();
    }
  },

  // 更新页面数据
  updatePageData() {
    const app = getApp();
    const currentStatus = loginManager.getStatus();
    
    this.setData({
      userInfo: app.getCurrentUser(),
      isLoggedIn: app.checkLoginStatus(),
      loginStatus: currentStatus
    });
  },

  // 检查自动登录
  checkAutoLogin() {
    const app = getApp();
    const isLoggedIn = app.checkLoginStatus();
    const currentStatus = loginManager.getStatus();
    
    // 如果未登录且不在登录中，提示用户登录
    if (!isLoggedIn && currentStatus === LoginStatus.NOT_LOGGED_IN) {
      setTimeout(() => {
        wx.pro.showModal({
          title: '提示',
          content: '登录后可以保存测试记录，获得更完整的体验，是否立即登录？',
          confirmText: '立即登录',
          cancelText: '暂不登录'
        }).then((res) => {
          if (res.confirm) {
            this.handleLogin();
          }
        }).catch(() => {
          // 用户取消
        });
      }, 1000);
    }
  },

  // 用户登录
  async handleLogin() {
    try {
      this.setData({ isLoading: true });
      
      console.log('开始用户授权登录...');
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        console.log('登录成功:', result.type);
        this.updatePageData();
      } else {
        console.error('登录失败:', result.error);
        await wx.pro.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);
      await wx.pro.showToast({
        title: error?.message || '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 调试方法 - 切换调试面板
  toggleDebug() {
    const showDebug = !this.data.showDebug;
    this.setData({ showDebug });
    if (showDebug) {
      this.updateDebugInfo();
    }
  },

  // 调试方法 - 测试静默登录
  async testSilentLogin() {
    try {
      console.log('测试静默登录...');
      this.setData({ 
        isLoading: true,
        'debugInfo.errorInfo': ''
      });
      
      const result = await loginManager.silentLogin();
      
      console.log('静默登录结果:', result);
      await wx.pro.showModal({
        title: '静默登录结果',
        content: `成功: ${result.success}\n类型: ${result.type}\n错误: ${result.error || '无'}`,
        showCancel: false
      });
      
      this.updateDebugInfo();
    } catch (error: any) {
      console.error('测试静默登录失败:', error);
      this.setData({
        'debugInfo.errorInfo': error.message || '测试失败'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 调试方法 - 测试登录检查
  async testLoginCheck() {
    try {
      console.log('测试登录检查...');
      this.setData({ 
        isLoading: true,
        'debugInfo.errorInfo': ''
      });
      
      const isLoggedIn = await loginManager.checkLoginStatus();
      
      console.log('登录检查结果:', isLoggedIn);
      await wx.pro.showModal({
        title: '登录检查结果',
        content: `是否已登录: ${isLoggedIn ? '是' : '否'}`,
        showCancel: false
      });
      
      this.updateDebugInfo();
    } catch (error: any) {
      console.error('测试登录检查失败:', error);
      this.setData({
        'debugInfo.errorInfo': error.message || '检查失败'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 调试方法 - 清除token
  clearToken() {
    wx.removeStorageSync('userToken');
    console.log('已清除本地token');
    wx.pro.showToast({
      title: '已清除token',
      icon: 'success'
    });
    this.updateDebugInfo();
  },

  // 调试方法 - 测试获取用户信息
  async testGetProfile() {
    try {
      console.log('测试获取用户信息...');
      this.setData({ 
        isLoading: true,
        'debugInfo.errorInfo': ''
      });
      
      const userInfo = await userAPI.getProfile();
      
      console.log('获取用户信息成功:', userInfo);
      await wx.pro.showModal({
        title: '用户信息',
        content: `昵称: ${userInfo.nickname}\nID: ${userInfo.id}\n分析次数: ${userInfo.analysisCredits}\n轨迹次数: ${userInfo.trackCredits}`,
        showCancel: false
      });
      
      this.updateDebugInfo();
    } catch (error: any) {
      console.error('测试获取用户信息失败:', error);
      await wx.pro.showModal({
        title: '获取用户信息失败',
        content: error?.message || '请求失败',
        showCancel: false
      });
      this.setData({
        'debugInfo.errorInfo': error?.message || '获取用户信息失败'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 更新调试信息
  async updateDebugInfo() {
    try {
      const token = wx.getStorageSync('userToken');
      const hasToken = !!token;
      
      let tokenValidated = false;
      let sessionValid = false;
      let errorInfo = '';
      
      if (hasToken) {
        try {
          // 检查token有效性
          tokenValidated = await loginManager.checkLoginStatus();
        } catch (error: any) {
          errorInfo = error.message || '验证失败';
        }
        
        try {
          // 检查session有效性  
          sessionValid = await loginManager.checkSessionKey();
        } catch (error: any) {
          console.warn('检查session失败:', error);
        }
      }
      
      this.setData({
        debugInfo: {
          hasToken,
          tokenValidated,
          sessionValid,
          lastLoginCheck: new Date().toLocaleTimeString(),
          errorInfo
        }
      });
    } catch (error: any) {
      console.error('更新调试信息失败:', error);
      this.setData({
        'debugInfo.errorInfo': error.message || '更新失败'
      });
    }
  },

  // 显示Token调试报告
  async showTokenReport() {
    try {
      const report = tokenDebug.generateDebugReport();
      
      await wx.pro.showModal({
        title: 'Token调试报告',
        content: report,
        showCancel: false,
        confirmText: '知道了'
      });
    } catch (error: any) {
      console.error('显示Token报告失败:', error);
      await wx.pro.showToast({
        title: '获取报告失败',
        icon: 'none'
      });
    }
  },

  // 编辑资料
  async handleEditProfile() {
    try {
      await wx.pro.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    } catch (error) {
      console.error('编辑资料失败:', error);
    }
  },

  // 升级次数
  async handleUpgradeCredits() {
    try {
      await wx.pro.navigateTo({ url: '/pages/pay/pay' });
    } catch (error) {
      console.error('跳转充值页面失败:', error);
      await wx.pro.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  },

  // 加载用户统计数据
  loadUserStats() {
    // 模拟加载用户统计数据
    if (this.data.isLoggedIn) {
      // 实际项目中这里应该从API获取数据
      this.setData({
        testCount: 5,
        favoriteCount: 12,
        shareCount: 8
      });
    } else {
      this.setData({
        testCount: 0,
        favoriteCount: 0,
        shareCount: 0
      });
    }
  },

  // 菜单点击处理
  async handleMenuTap(e: WechatMiniprogram.TouchEvent) {
    const { path } = e.currentTarget.dataset;
    if (!path) return;

    try {
      const app = getApp();
      
      // 某些页面需要登录才能访问
      const needLoginPages = ['/pages/reports/reports', '/pages/history/history', '/pages/credits/credits'];
      
      if (needLoginPages.includes(path) && !app.checkLoginStatus()) {
        const res = await wx.pro.showModal({
          title: '请先登录',
          content: '此功能需要登录后才能使用',
          confirmText: '立即登录',
          cancelText: '取消'
        });
        
        if (res.confirm) {
          await this.handleLogin();
          
                     // 登录成功后再次尝试跳转
           if (app.checkLoginStatus()) {
             // TabBar页面列表
             const tabBarPages = [
               '/pages/home/<USER>',
               '/pages/track/track', 
               '/pages/reports/reports',
               '/pages/me/me'
             ];
             
             if (tabBarPages.includes(path)) {
               await wx.pro.switchTab({ url: path });
             } else {
               await wx.pro.navigateTo({ url: path });
             }
           }
        }
        return;
      }

             // 直接跳转页面
       const tabBarPages = [
         '/pages/home/<USER>',
         '/pages/track/track', 
         '/pages/reports/reports',
         '/pages/me/me'
       ];
       
       if (tabBarPages.includes(path)) {
         await wx.pro.switchTab({ url: path });
       } else {
         await wx.pro.navigateTo({ url: path });
       }
     } catch (error: any) {
       console.error('菜单跳转失败:', error);
       await wx.pro.showToast({
         title: '跳转失败',
         icon: 'none'
       });
     }
  }
});

export {}; 