/* 积分页面样式 - 使用CSS变量系统 */

.points-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 积分头部
.points-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-sm);
    }
    
    .points-display {
      background: rgba(255, 255, 255, 0.1);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg);
      margin: var(--spacing-md) 0;
      
      .points-label {
        font-size: var(--font-size-sm);
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: var(--spacing-xs);
      }
      
      .points-value {
        font-size: var(--font-size-4xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-color-inverse);
        margin-bottom: var(--spacing-xs);
      }
      
      .points-unit {
        font-size: var(--font-size-base);
        color: rgba(255, 255, 255, 0.8);
      }
    }
    
    .points-actions {
      display: flex;
      justify-content: center;
      gap: var(--spacing-md);
      
      .action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1rpx solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-sm) var(--spacing-md);
        color: var(--text-color-inverse);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:active {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(0.95);
        }
      }
    }
  }
}

// 积分统计
.stats-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    
    .stat-card {
      background: var(--background-color-elevated);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-lg);
      text-align: center;
      box-shadow: var(--shadow-sm);
      border: 1rpx solid var(--border-color-primary);
      
      .stat-value {
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
        margin-bottom: var(--spacing-xs);
      }
      
      .stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-color-secondary);
        line-height: var(--line-height-normal);
      }
    }
  }
}

// 积分任务
.tasks-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    
    .title-icon {
      margin-right: var(--spacing-sm);
      color: var(--primary-color);
    }
  }
  
  .task-list {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
    
    .task-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-lg);
      border-bottom: 1rpx solid var(--border-color-secondary);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: var(--background-color-tertiary);
      }
      
      .task-icon {
        width: 48rpx;
        height: 48rpx;
        background: var(--primary-color-sub);
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
        flex-shrink: 0;
        
        .iconfont {
          font-size: var(--font-size-lg);
          color: var(--primary-color);
        }
      }
      
      .task-content {
        flex: 1;
        
        .task-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .task-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
          margin-bottom: var(--spacing-xs);
        }
        
        .task-progress {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .progress-bar {
            flex: 1;
            height: 8rpx;
            background: var(--background-color-tertiary);
            border-radius: var(--border-radius-full);
            margin-right: var(--spacing-sm);
            overflow: hidden;
            
            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
              border-radius: var(--border-radius-full);
              transition: width var(--transition-normal) var(--ease-in-out);
            }
          }
          
          .progress-text {
            font-size: var(--font-size-xs);
            color: var(--text-color-secondary);
            min-width: 60rpx;
            text-align: right;
          }
        }
      }
      
      .task-reward {
        text-align: right;
        margin-left: var(--spacing-md);
        
        .reward-points {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--success-color);
          margin-bottom: var(--spacing-xs);
        }
        
        .reward-btn {
          background: var(--primary-color);
          color: var(--text-color-inverse);
          border: none;
          border-radius: var(--border-radius-sm);
          padding: var(--spacing-xs) var(--spacing-sm);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          transition: all var(--transition-normal) var(--ease-in-out);
          
          &:active {
            transform: scale(0.95);
            background: var(--primary-color-dark);
          }
          
          &.completed {
            background: var(--success-color);
            
            &:active {
              background: var(--success-color-dark);
            }
          }
          
          &.disabled {
            background: var(--background-color-tertiary);
            color: var(--text-color-secondary);
            pointer-events: none;
          }
        }
      }
    }
  }
}

// 积分记录
.records-section {
  margin: 0 var(--spacing-md) var(--spacing-xl);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title-icon {
      margin-right: var(--spacing-sm);
      color: var(--primary-color);
    }
    
    .view-all {
      font-size: var(--font-size-sm);
      color: var(--primary-color);
      font-weight: var(--font-weight-medium);
    }
  }
  
  .record-list {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
    
    .record-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-lg);
      border-bottom: 1rpx solid var(--border-color-secondary);
      
      &:last-child {
        border-bottom: none;
      }
      
      .record-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
        flex-shrink: 0;
        
        &.income {
          background: var(--success-color-sub);
          
          .iconfont {
            color: var(--success-color);
          }
        }
        
        &.expense {
          background: var(--error-color-sub);
          
          .iconfont {
            color: var(--error-color);
          }
        }
        
        .iconfont {
          font-size: var(--font-size-base);
        }
      }
      
      .record-content {
        flex: 1;
        
        .record-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .record-time {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
        }
      }
      
      .record-amount {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        
        &.income {
          color: var(--success-color);
        }
        
        &.expense {
          color: var(--error-color);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .points-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
    
    .points-display {
      padding: var(--spacing-md);
      
      .points-value {
        font-size: var(--font-size-3xl);
      }
    }
    
    .points-actions {
      gap: var(--spacing-sm);
      
      .action-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
      }
    }
  }
  
  .stats-section,
  .tasks-section,
  .records-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .stats-grid {
    gap: var(--spacing-xs);
    
    .stat-card {
      padding: var(--spacing-md);
      
      .stat-value {
        font-size: var(--font-size-xl);
      }
    }
  }
  
  .task-item {
    padding: var(--spacing-md);
    
    .task-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: var(--spacing-sm);
      
      .iconfont {
        font-size: var(--font-size-base);
      }
    }
    
    .task-reward {
      margin-left: var(--spacing-sm);
      
      .reward-points {
        font-size: var(--font-size-base);
      }
    }
  }
  
  .record-item {
    padding: var(--spacing-md);
    
    .record-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: var(--spacing-sm);
      
      .iconfont {
        font-size: var(--font-size-sm);
      }
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .points-container {
    background-color: var(--background-color-secondary);
  }
  
  .points-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .stats-grid .stat-card,
  .task-list,
  .record-list {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .task-icon {
    background: var(--primary-color-sub);
    
    .iconfont {
      color: var(--primary-color);
    }
  }
  
  .task-item {
    border-bottom-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-tertiary);
    }
  }
  
  .progress-bar {
    background: var(--background-color-tertiary);
  }
  
  .record-item {
    border-bottom-color: var(--border-color-primary);
  }
  
  .record-icon {
    &.income {
      background: var(--success-color-sub);
      
      .iconfont {
        color: var(--success-color);
      }
    }
    
    &.expense {
      background: var(--error-color-sub);
      
      .iconfont {
        color: var(--error-color);
      }
    }
  }
} 