import { creditsAPI, inviteAPI } from '../../utils/api';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  credits: {
    analysisCredits: number;
    trackCredits: number;
  } | null;
  inviteCode: string;
  loading: boolean;
  isLoggedIn: boolean;
  loginStatus: LoginStatus;
}

interface ICustomMethods {
  loadCredits(): Promise<void>;
  handleRedeemCode(): Promise<void>;
  handleInviteCodeInput(e: WechatMiniprogram.Input): void;
  handleRecharge(): Promise<void>;
  checkLoginStatus(): Promise<boolean>;
  handleLogin(): Promise<void>;
  showLoginPrompt(): Promise<void>;
  checkAndLoadData(): Promise<void>;
}

Page<IPageData, ICustomMethods>({
  data: {
    credits: null,
    inviteCode: '',
    loading: false,
    isLoggedIn: false,
    loginStatus: LoginStatus.NOT_LOGGED_IN
  },

  onLoad() {
    this.checkAndLoadData();
  },

  onShow() {
    this.checkAndLoadData();
  },

  async checkAndLoadData() {
    const isLoggedIn = await this.checkLoginStatus();
    this.setData({ isLoggedIn });
    
    if (!isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }
    
    await this.loadCredits();
  },

  async loadCredits() {
    if (!this.data.isLoggedIn) return;

    this.setData({ loading: true });

    try {
      const credits = await creditsAPI.getCredits();
      this.setData({ credits });
    } catch (error) {
      console.error('加载次数信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  handleInviteCodeInput(e: WechatMiniprogram.Input) {
    this.setData({
      inviteCode: e.detail.value
    });
  },

  async handleRedeemCode() {
    if (!this.data.inviteCode.trim()) {
      wx.showToast({
        title: '请输入邀请码',
        icon: 'none'
      });
      return;
    }

    if (!this.data.isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }

    this.setData({ loading: true });

    try {
      await inviteAPI.redeemCode(this.data.inviteCode.trim());
      
      wx.showToast({
        title: '兑换成功',
        icon: 'success'
      });

      // 清空输入框
      this.setData({ inviteCode: '' });
      
      // 重新加载次数信息
      await this.loadCredits();
    } catch (error: any) {
      console.error('兑换邀请码失败:', error);
      wx.showToast({
        title: error?.message || '兑换失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  async handleRecharge() {
    if (!this.data.isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }

    try {
      await wx.navigateTo({
        url: '/pages/pay/pay'
      });
    } catch (error) {
      console.error('跳转充值页面失败:', error);
    }
  },

  async showLoginPrompt() {
    try {
      const result = await wx.showModal({
        title: '请先登录',
        content: '次数管理功能需要登录后才能使用，是否立即登录？',
        confirmText: '立即登录',
        cancelText: '取消'
      });

      if (result.confirm) {
        await this.handleLogin();
      } else {
        // 用户取消，跳转回首页
        await wx.switchTab({ url: '/pages/home/<USER>' });
      }
    } catch (error) {
      console.error('显示登录提示失败:', error);
    }
  },

  async checkLoginStatus(): Promise<boolean> {
    try {
      return await loginManager.checkLoginStatus();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  },

  async handleLogin(): Promise<void> {
    try {
      console.log('开始用户授权登录...');
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        console.log('登录成功:', result.type);
        this.setData({ isLoggedIn: true });
        // 登录成功后重新加载数据
        await this.loadCredits();
      } else {
        console.error('登录失败:', result.error);
        await wx.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);
      await wx.showToast({
        title: error?.message || '登录失败，请重试',
        icon: 'none'
      });
    }
  }
}); 