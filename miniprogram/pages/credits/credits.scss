/* 积分页面样式 - 使用CSS变量系统 */

.credits-container {
  min-height: 100vh;
  background: var(--background-color-secondary);
  padding-bottom: var(--spacing-xl);
}

// 页面头部
.credits-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-3xl) var(--spacing-xl) var(--spacing-xl);
  text-align: center;
  color: var(--text-color-inverse);
  
  .header-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-xs);
  }
  
  .header-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    line-height: var(--line-height-normal);
  }
}

.credits-content {
  padding: 0 var(--spacing-xl);
  margin-top: calc(-1 * var(--spacing-md));
}

// 次数信息卡片
.credits-card {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  border: 1rpx solid var(--border-color-primary);
  
  .credits-header {
    background: none;
    padding: 0;
    text-align: left;
    margin-bottom: var(--spacing-lg);
    
    .credits-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      display: block;
      margin-bottom: var(--spacing-xs);
    }
    
    .credits-subtitle {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      line-height: var(--line-height-normal);
    }
  }
  
  .credits-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    .credit-item {
      flex: 1;
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .credit-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: var(--border-radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-sm);
        
        &.analysis {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
        }
        
        &.track {
          background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
        }
        
        .iconfont {
          font-size: var(--font-size-2xl);
          color: var(--text-color-inverse);
        }
      }
      
      .credit-info {
        flex: 1;
        
        .credit-value {
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
          display: block;
          margin-bottom: var(--spacing-xs);
        }
        
        .credit-label {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
    }
  }
  
  .credits-actions {
    .recharge-btn {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      color: var(--text-color-inverse);
      border: none;
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
      box-shadow: var(--shadow-lg);
      transition: all var(--transition-normal) var(--ease-in-out);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.2), 
          transparent
        );
        transition: left var(--transition-slow) var(--ease-in-out);
      }
      
      &:hover::before {
        left: 100%;
      }
      
      .iconfont {
        font-size: var(--font-size-base);
      }
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-xl);
      }
    }
  }
}

// 邀请码兑换卡片
.invite-card {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-color-primary);
  
  .card-header {
    margin-bottom: var(--spacing-lg);
    
    .card-title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      display: block;
      margin-bottom: var(--spacing-xs);
    }
    
    .card-subtitle {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      line-height: var(--line-height-normal);
    }
  }
  
  .invite-form {
    display: flex;
    gap: var(--spacing-sm);
    
    .input-wrapper {
      flex: 1;
      
      .invite-input {
        width: 100%;
        height: 80rpx;
        background: var(--background-color-tertiary);
        border: 2rpx solid var(--border-color-primary);
        border-radius: var(--border-radius-lg);
        padding: 0 var(--spacing-lg);
        font-size: var(--font-size-base);
        color: var(--text-color-primary);
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:focus {
          border-color: var(--primary-color);
          background: var(--background-color-elevated);
          box-shadow: var(--shadow-sm);
        }
        
        &::placeholder {
          color: var(--text-color-tertiary);
        }
      }
    }
    
    .redeem-btn {
      width: 160rpx;
      height: 80rpx;
      background: var(--primary-color);
      color: var(--text-color-inverse);
      border: none;
      border-radius: var(--border-radius-lg);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:active {
        transform: scale(0.95);
        background: var(--primary-color-dark);
      }
      
      &:disabled {
        background: var(--background-color-tertiary);
        color: var(--text-color-secondary);
        pointer-events: none;
      }
    }
  }
}

// 积分记录卡片
.records-card {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-color-primary);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    .card-title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
    }
    
    .view-all {
      font-size: var(--font-size-sm);
      color: var(--primary-color);
      font-weight: var(--font-weight-medium);
    }
  }
  
  .record-list {
    .record-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-md) 0;
      border-bottom: 1rpx solid var(--border-color-secondary);
      
      &:last-child {
        border-bottom: none;
      }
      
      .record-icon {
        width: 48rpx;
        height: 48rpx;
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
        flex-shrink: 0;
        
        &.income {
          background: var(--success-color-sub);
          
          .iconfont {
            color: var(--success-color);
          }
        }
        
        &.expense {
          background: var(--error-color-sub);
          
          .iconfont {
            color: var(--error-color);
          }
        }
        
        .iconfont {
          font-size: var(--font-size-base);
        }
      }
      
      .record-content {
        flex: 1;
        
        .record-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .record-time {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
        }
      }
      
      .record-amount {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        
        &.income {
          color: var(--success-color);
        }
        
        &.expense {
          color: var(--error-color);
        }
      }
    }
  }
}

// 使用说明卡片
.usage-card {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  
  .card-header {
    margin-bottom: var(--spacing-lg);
    
    .card-title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
    }
  }
  
  .usage-list {
    .usage-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-md);
      padding: var(--spacing-md) 0;
      border-bottom: 1rpx solid var(--border-color-primary);
      
      &:last-child {
        border-bottom: none;
      }
      
      .usage-icon {
        font-size: var(--font-size-2xl);
        margin-top: var(--spacing-xs);
      }
      
      .usage-content {
        flex: 1;
        
        .usage-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
          display: block;
          margin-bottom: var(--spacing-xs);
        }
        
        .usage-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
    }
  }
}

// 登录提示
.login-prompt {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-xl);
  
  .iconfont {
    font-size: var(--font-size-3xl);
    color: var(--primary-color-light);
    margin-bottom: var(--spacing-xl);
  }
  
  .prompt-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .prompt-desc {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-xl);
  }
  
  .login-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-color-inverse);
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-xl);
    }
  }
}

// 加载状态
.loading-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid var(--border-color-primary);
    border-top: 4rpx solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-xl);
  }
  
  .loading-text {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 750rpx) {
  .credits-header {
    padding: var(--spacing-2xl) var(--spacing-md) var(--spacing-md);
    
    .header-title {
      font-size: var(--font-size-xl);
    }
  }
  
  .credits-content {
    padding: 0 var(--spacing-md);
  }
  
  .credits-card {
    padding: var(--spacing-md);
    
    .credits-stats {
      flex-direction: column;
      gap: var(--spacing-md);
      
      .credit-item {
        .credit-icon {
          width: 64rpx;
          height: 64rpx;
          
          .iconfont {
            font-size: var(--font-size-xl);
          }
        }
        
        .credit-info .credit-value {
          font-size: var(--font-size-xl);
        }
      }
    }
  }
  
  .invite-card,
  .records-card {
    padding: var(--spacing-md);
  }
  
  .invite-form {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .redeem-btn {
      width: 100%;
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .credits-container {
    background: var(--background-color-secondary);
  }
  
  .credits-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .credits-card,
  .invite-card,
  .records-card {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .credit-icon {
    &.analysis {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    }
    
    &.track {
      background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
    }
  }
  
  .invite-input {
    background: var(--background-color-tertiary);
    border-color: var(--border-color-primary);
    color: var(--text-color-primary);
    
    &:focus {
      background: var(--background-color-elevated);
      border-color: var(--primary-color);
    }
    
    &::placeholder {
      color: var(--text-color-tertiary);
    }
  }
  
  .record-item {
    border-bottom-color: var(--border-color-primary);
  }
  
  .record-icon {
    &.income {
      background: var(--success-color-sub);
      
      .iconfont {
        color: var(--success-color);
      }
    }
    
    &.expense {
      background: var(--error-color-sub);
      
      .iconfont {
        color: var(--error-color);
      }
    }
  }
} 