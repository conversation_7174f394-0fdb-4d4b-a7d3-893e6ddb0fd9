<view class="credits-container">
  <!-- 页面头部 -->
  <view class="credits-header">
    <view class="header-title">次数管理</view>
    <view class="header-subtitle">管理您的分析次数和轨迹次数</view>
  </view>

  <view class="credits-content">
    <!-- 次数信息卡片 -->
    <view class="credits-card" wx:if="{{isLoggedIn && credits}}">
      <view class="credits-header">
        <text class="credits-title">我的次数</text>
        <text class="credits-subtitle">当前可用次数</text>
      </view>
      
      <view class="credits-stats">
        <view class="credit-item">
          <view class="credit-icon analysis">
            <text class="iconfont icon-fenxi"></text>
          </view>
          <view class="credit-info">
            <text class="credit-value">{{credits.analysisCredits}}</text>
            <text class="credit-label">分析次数</text>
          </view>
        </view>
        
        <view class="credit-item">
          <view class="credit-icon track">
            <text class="iconfont icon-shijian"></text>
          </view>
          <view class="credit-info">
            <text class="credit-value">{{credits.trackCredits}}</text>
            <text class="credit-label">轨迹次数</text>
          </view>
        </view>
      </view>
      
      <view class="credits-actions">
        <button class="recharge-btn" bindtap="handleRecharge">
          <text class="iconfont icon-chongzhi"></text>
          立即充值
        </button>
      </view>
    </view>

    <!-- 邀请码兑换 -->
    <view class="invite-card">
      <view class="card-header">
        <text class="card-title">邀请码兑换</text>
        <text class="card-subtitle">输入邀请码获得免费次数</text>
      </view>
      
      <view class="invite-form">
        <view class="input-wrapper">
          <input class="invite-input" 
                 placeholder="请输入邀请码" 
                 value="{{inviteCode}}"
                 bindinput="handleInviteCodeInput"
                 maxlength="20" />
        </view>
        <button class="redeem-btn" 
                bindtap="handleRedeemCode"
                loading="{{loading}}">
          兑换
        </button>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="usage-card">
      <view class="card-header">
        <text class="card-title">使用说明</text>
      </view>
      
      <view class="usage-list">
        <view class="usage-item">
          <text class="usage-icon">📊</text>
          <view class="usage-content">
            <text class="usage-title">分析次数</text>
            <text class="usage-desc">用于生成详细的MBTI性格分析报告</text>
          </view>
        </view>
        
        <view class="usage-item">
          <text class="usage-icon">📈</text>
          <view class="usage-content">
            <text class="usage-title">轨迹次数</text>
            <text class="usage-desc">用于记录和追踪性格变化轨迹</text>
          </view>
        </view>
        
        <view class="usage-item">
          <text class="usage-icon">🎁</text>
          <view class="usage-content">
            <text class="usage-title">邀请码</text>
            <text class="usage-desc">通过邀请码可以获得免费次数</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="login-prompt" wx:if="{{!isLoggedIn}}">
      <text class="iconfont icon-denglu"></text>
      <view class="prompt-title">请先登录</view>
      <view class="prompt-desc">登录后可以查看和管理您的次数</view>
      <button class="login-btn" bindtap="handleLogin">立即登录</button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 