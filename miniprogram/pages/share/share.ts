import dayjs from 'dayjs';
import { shareAPI } from '../../utils/api';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  shareData: {
    mbtiType: string;
    nickName: string;
    avatarUrl: string;
    testDate: string;
    shareImage: string;
    shareCode?: string;
  } | null;
  loading: boolean;
}

interface IPageMethods {
  loadShareData(shareCode: string): Promise<void>;
  handleShare(): Promise<void>;
  handleSaveImage(): Promise<void>;
}

Page<IPageData, IPageMethods>({
  data: {
    shareData: null,
    loading: true
  },

  async onLoad(options: any) {
    const { code } = options;
    
    // 分享页面可以无需登录查看，但登录用户可以获得更好的体验
    const isLoggedIn = await loginManager.checkLoginStatus();
    if (!isLoggedIn) {
      console.log('用户未登录，使用基础分享功能');
    }
    
    this.loadShareData(code);
  },

  async loadShareData(shareCode: string) {
    try {
      // 由于后端接口文档中没有获取分享详情API，使用模拟数据
      const mockShareDetail = {
        id: shareCode,
        type: 'result',
        content: '我的MBTI测试结果是INTJ型人格！',
        imageUrl: 'https://example.com/share-image.jpg',
        createTime: '2024-01-01T00:00:00Z'
      };
      
      this.setData({
        shareData: {
          mbtiType: mockShareDetail.content.includes('INTJ') ? 'INTJ' : 'ENFP', // 从内容中解析
          nickName: '用户昵称', // 需要从用户信息获取
          avatarUrl: 'https://example.com/avatar.jpg', // 需要从用户信息获取
          testDate: dayjs(mockShareDetail.createTime).format('YYYY-MM-DD'),
          shareImage: mockShareDetail.imageUrl || '',
          shareCode: shareCode
        },
        loading: false
      });
    } catch (error) {
      console.error('加载分享数据失败:', error);
      this.setData({ loading: false });
    }
  },

  async handleShare() {
    try {
      // 使用微信小程序的分享功能
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  },

  async handleSaveImage() {
    try {
      await wx.saveImageToPhotosAlbum({
        filePath: this.data.shareData?.shareImage || ''
      });
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('保存图片失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  }
}); 