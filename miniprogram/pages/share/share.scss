/* 分享页面样式 - 使用CSS变量系统 */

.share-page {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: var(--spacing-md);
}

.share-container {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
}

.share-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  
  .share-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .share-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    line-height: var(--line-height-normal);
  }
}

.share-content {
  margin-bottom: var(--spacing-xl);
  
  .share-preview {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-color-inverse);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    
    .preview-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      margin-bottom: var(--spacing-sm);
    }
    
    .preview-desc {
      font-size: var(--font-size-sm);
      opacity: 0.9;
      line-height: var(--line-height-normal);
    }
  }
}

.share-options {
  margin-bottom: var(--spacing-xl);
  
  .option-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }
  
  .option-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    
    .option-item {
      flex: 1;
      min-width: 200rpx;
      background: var(--background-color-tertiary);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-lg);
      text-align: center;
      border: 1rpx solid var(--border-color-secondary);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:active {
        transform: scale(0.98);
        background: var(--background-color-quaternary);
        box-shadow: var(--shadow-sm);
      }
      
      .option-icon {
        width: 60rpx;
        height: 60rpx;
        margin: 0 auto var(--spacing-sm);
        background: var(--primary-color-sub);
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        
        .iconfont {
          font-size: var(--font-size-xl);
          color: var(--primary-color);
        }
      }
      
      .option-text {
        font-size: var(--font-size-sm);
        color: var(--text-color-primary);
        font-weight: var(--font-weight-medium);
        line-height: var(--line-height-normal);
      }
    }
  }
}

.share-actions {
  display: flex;
  gap: var(--spacing-md);
  
  .action-btn {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-color-inverse);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    text-align: center;
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal) var(--ease-in-out);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-md);
    }
    
    &.secondary-btn {
      background: var(--background-color-elevated);
      color: var(--primary-color);
      border: 2rpx solid var(--primary-color);
      
      &:active {
        background: var(--primary-color-sub);
      }
    }
  }
}

.share-tips {
  background: var(--warning-color-sub);
  border: 2rpx solid var(--warning-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-xl);
  
  .tips-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--warning-color-dark);
    margin-bottom: var(--spacing-xs);
  }
  
  .tips-content {
    font-size: var(--font-size-xs);
    color: var(--warning-color-dark);
    line-height: var(--line-height-normal);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .share-page {
    padding: var(--spacing-sm);
  }
  
  .share-container {
    padding: var(--spacing-lg);
  }
  
  .share-header .share-title {
    font-size: var(--font-size-xl);
  }
  
  .share-preview {
    padding: var(--spacing-lg);
    
    .preview-title {
      font-size: var(--font-size-base);
    }
  }
  
  .option-list {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .option-item {
      min-width: auto;
      padding: var(--spacing-md);
      
      .option-icon {
        width: 48rpx;
        height: 48rpx;
        
        .iconfont {
          font-size: var(--font-size-lg);
        }
      }
    }
  }
  
  .share-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .action-btn {
      padding: var(--spacing-md);
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .share-page {
    background-color: var(--background-color-secondary);
  }
  
  .share-container {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .share-preview {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .option-item {
    background: var(--background-color-tertiary);
    border-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-quaternary);
    }
    
    .option-icon {
      background: var(--primary-color-sub);
      
      .iconfont {
        color: var(--primary-color);
      }
    }
  }
  
  .action-btn {
    &.secondary-btn {
      background: var(--background-color-elevated);
      color: var(--primary-color);
      border-color: var(--primary-color);
      
      &:active {
        background: var(--primary-color-sub);
      }
    }
  }
  
  .share-tips {
    background: var(--warning-color-sub);
    border-color: var(--warning-color);
    
    .tips-title,
    .tips-content {
      color: var(--warning-color-dark);
    }
  }
} 