<view class="share-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 分享内容 -->
  <view wx:elif="{{shareData}}" class="share-content">
    <!-- 分享卡片 -->
    <view class="share-card">
      <view class="card-header">
        <image class="user-avatar" src="{{shareData.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{shareData.nickName}}</text>
          <text class="test-date">{{shareData.testDate}}</text>
        </view>
      </view>
      
      <view class="mbti-result">
        <text class="result-title">我的MBTI人格类型</text>
        <text class="mbti-type">{{shareData.mbtiType}}</text>
        <text class="result-subtitle">基于专业的MBTI测试结果</text>
      </view>
      
      <view class="share-image">
        <image class="result-image" src="{{shareData.shareImage}}" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn share-btn" bindtap="handleShare">
        <text class="iconfont icon-fenxiang"></text>
        <text class="btn-text">分享给朋友</text>
      </button>
      
      <button class="action-btn save-btn" bindtap="handleSaveImage">
        <text class="iconfont icon-dakai"></text>
        <text class="btn-text">保存图片</text>
      </button>
    </view>

    <!-- 邀请测试 -->
    <view class="invite-section">
      <view class="invite-content">
        <text class="invite-title">邀请朋友一起测试</text>
        <text class="invite-desc">了解自己的人格类型，发现更好的自己</text>
        <button class="invite-btn" bindtap="handleShare">
          <text class="invite-text">立即邀请</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:else class="error-state">
    <text class="iconfont icon-shibai error-icon"></text>
    <text class="error-text">分享内容不存在</text>
    <text class="error-desc">该分享链接可能已过期或不存在</text>
    <button class="back-btn" bindtap="handleBack">
      <text class="back-text">返回首页</text>
    </button>
  </view>
</view> 