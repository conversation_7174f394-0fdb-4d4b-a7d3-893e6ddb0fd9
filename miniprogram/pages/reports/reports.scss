@import '../../styles/variables.scss';

.reports-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: 0;
}

// 报告页头部
.reports-header {
  background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
  padding: 40rpx 20rpx;
  border-radius: 0 0 32rpx 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
  
  .header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: $white;
    margin-bottom: 8rpx;
  }
  
  .header-subtitle {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

// 报告内容区域
.reports-content {
  padding: 0 20rpx 20rpx;
}

// 筛选选项
.filter-section {
  margin-bottom: 20rpx;
  
  .filter-tabs {
    background: $white;
    border-radius: 16rpx;
    padding: 8rpx;
    display: flex;
    gap: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid $border-color;
    
    .filter-tab {
      flex: 1;
      text-align: center;
      padding: 20rpx 0;
      font-size: 28rpx;
      color: $text-secondary;
      font-weight: 500;
      border-radius: 12rpx;
      transition: all 0.3s ease;
      
      &.active {
        background: $primary-color;
        color: $white;
        font-weight: 600;
      }
      
      &:active:not(.active) {
        background: lighten($secondary-color, 2%);
      }
    }
  }
}

// 报告列表
.reports-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.report-item {
  background: $white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid $border-color;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  }
  
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .report-type {
      background: $secondary-color;
      color: $primary-color;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      font-weight: 600;
      border: 1rpx solid $border-color;
    }
    
    .report-date {
      font-size: 24rpx;
      color: $text-secondary;
    }
  }
  
  .report-content {
    margin-bottom: 20rpx;
    
    .report-name {
      font-size: 30rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 8rpx;
    }
    
    .report-description {
      font-size: 26rpx;
      color: $text-secondary;
      line-height: 1.5;
    }
  }
  
  .report-actions {
    display: flex;
    justify-content: flex-end;
    gap: 24rpx;
    
    .action-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $secondary-color;
      border-radius: 16rpx;
      border: 1rpx solid $border-color;
      transition: all 0.2s ease;
      
      .iconfont {
        font-size: 28rpx;
        color: $primary-color;
      }
      
      &:active {
        background: darken($secondary-color, 5%);
        transform: scale(0.9);
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  
  .iconfont {
    font-size: 120rpx;
    color: lighten($primary-color, 30%);
    margin-bottom: 40rpx;
  }
  
  .empty-title {
    font-size: 32rpx;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 16rpx;
  }
  
  .empty-description {
    font-size: 26rpx;
    color: $text-secondary;
    line-height: 1.5;
    margin-bottom: 40rpx;
  }
  
  .empty-btn {
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
    color: $white;
    border: none;
    border-radius: 24rpx;
    padding: 24rpx 48rpx;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 8rpx 24rpx rgba(0, 108, 104, 0.3);
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(0, 108, 104, 0.4);
    }
  }
}

// 加载状态
.loading-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid $border-color;
    border-top: 4rpx solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: $text-secondary;
  }
}

// 付费标识
.report-type {
  .paid-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
    color: $white;
    padding: 2rpx 8rpx;
    border-radius: 8rpx;
    font-size: 18rpx;
    font-weight: 600;
    margin-left: 8rpx;
    vertical-align: middle;
    
    .iconfont {
      font-size: 18rpx;
      margin-right: 2rpx;
    }
  }
}

// 搜索框样式
.search-section {
  margin-bottom: 20rpx;
  
  .search-wrapper {
    background: $white;
    border-radius: 16rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid $border-color;
    
    .search-icon {
      .iconfont {
        font-size: 32rpx;
        color: $text-secondary;
      }
    }
    
    .search-input {
      flex: 1;
      margin-left: 16rpx;
      font-size: 28rpx;
      
      &::placeholder {
        color: lighten($text-secondary, 20%);
      }
    }
    
    .clear-icon {
      .iconfont {
        font-size: 28rpx;
        color: $text-secondary;
      }
      
      &:active {
        opacity: 0.6;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 750rpx) {
  .reports-header {
    padding: 30rpx 16rpx;
  }
  
  .reports-content {
    padding: 0 16rpx 20rpx;
  }
  
  .report-item {
    padding: 20rpx;
  }
  
  .empty-state {
    padding: 100rpx 30rpx;
  }
  
  .filter-tab {
    font-size: 26rpx;
    padding: 16rpx 0;
  }
} 