<view class="reports-container">
  <!-- 页面头部 -->
  <view class="reports-header">
    <view class="header-title">我的报告</view>
    <view class="header-subtitle">查看你的所有MBTI测试报告</view>
  </view>

  <view class="reports-content">
    <!-- 搜索框 -->
    <view class="search-section" wx:if="{{reports.length > 0 || searchQuery}}">
      <view class="search-wrapper">
        <view class="search-icon">
          <text class="iconfont icon-sousuo"></text>
        </view>
        <input class="search-input" 
               placeholder="搜索报告" 
               value="{{searchQuery}}"
               bindinput="handleSearch" />
        <view class="clear-icon" wx:if="{{searchQuery}}" bindtap="clearSearch">
          <text class="iconfont icon-guanbi"></text>
        </view>
      </view>
    </view>

    <!-- 筛选选项 -->
    <view class="filter-section" wx:if="{{reports.length > 0}}">
      <view class="filter-tabs">
        <view class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}" 
              bindtap="handleFilterChange" data-type="all">
          全部
        </view>
        <view class="filter-tab {{currentFilter === 'recent' ? 'active' : ''}}" 
              bindtap="handleFilterChange" data-type="recent">
          最近
        </view>
        <view class="filter-tab {{currentFilter === 'favorite' ? 'active' : ''}}" 
              bindtap="handleFilterChange" data-type="favorite">
          收藏
        </view>
      </view>
    </view>

    <!-- 报告列表 -->
    <view class="reports-list" wx:if="{{reports.length > 0}}">
      <view class="report-item" 
            wx:for="{{reports}}" 
            wx:key="id" 
            bindtap="handleReportTap" 
            data-id="{{item.id}}"
            data-report-id="{{item.id}}"
            data-is-paid="{{item.isPaid}}">
        <view class="report-header">
          <view class="report-type">
            {{item.mbtiType}}
            <view class="paid-badge" wx:if="{{item.isPaid}}">
              <text class="iconfont icon-liwu"></text>
              付费
            </view>
          </view>
          <view class="report-date">{{item.createTime}}</view>
        </view>
        <view class="report-content">
          <view class="report-name">{{item.title}}</view>
          <view class="report-description">
            {{item.type === 'career' ? '职业发展建议' : item.type === 'relationship' ? '人际关系分析' : '个人成长指南'}}
          </view>
        </view>
        <view class="report-actions">
          <view class="action-btn" catchtap="handleShare" data-id="{{item.id}}">
            <text class="iconfont icon-fenxiang1"></text>
          </view>
          <view class="action-btn" catchtap="handleFavorite" data-id="{{item.id}}">
            <text class="iconfont {{item.isFavorite ? 'icon-shoucang1' : 'icon-shoucang1'}}"></text>
          </view>
          <view class="action-btn" catchtap="handleDelete" data-id="{{item.id}}">
            <text class="iconfont icon-shanchu"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <text class="iconfont icon-yiwen"></text>
      <view class="empty-title">暂无报告</view>
      <view class="empty-description">完成MBTI测试后，你的报告将显示在这里</view>
      <button class="empty-btn" bindtap="handleStartTest">开始测试</button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 