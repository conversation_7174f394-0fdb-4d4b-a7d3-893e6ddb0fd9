import dayjs from 'dayjs';
import { aiAPI, mbtiAPI, type MBTIResult } from '../../utils/api';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  reports: Array<{
    id: string;
    title: string;
    mbtiType: string;
    createTime: string;
    readStatus: boolean;
    isFavorite?: boolean;
    type: 'career' | 'relationship' | 'personal';
    isPaid: boolean;
    price: number;
  }>;
  loading: boolean;
  page: number;
  hasMore: boolean;
  searchQuery: string;
  selectedType: string;
  currentFilter: string;
  typeList: Array<{
    value: string;
    label: string;
  }>;
}

interface ICustomMethods {
  loadReports(): Promise<void>;
  handleReportTap(e: WechatMiniprogram.TouchEvent): Promise<void>;
  handleStartTest(): Promise<void>;
  checkCredits(): boolean;
  showCreditsUpgrade(): Promise<void>;
  handleSearch(e: WechatMiniprogram.Input): void;
  handleTypeChange(e: WechatMiniprogram.CustomEvent): void;
  handleFilterChange(e: WechatMiniprogram.TouchEvent): void;
  clearSearch(): void;
  refreshReports(): Promise<void>;
  checkLoginStatus(): Promise<boolean>;
  handleLogin(): Promise<void>;
  checkAndLoadReports(): Promise<void>;
  showLoginPrompt(): Promise<void>;
  handleShare(e: WechatMiniprogram.TouchEvent): Promise<void>;
  handleFavorite(e: WechatMiniprogram.TouchEvent): Promise<void>;
  handleDelete(e: WechatMiniprogram.TouchEvent): Promise<void>;
}

interface IPageInstance {
  searchTimeout?: number;
}

Page<IPageData, ICustomMethods & IPageInstance>({
  data: {
    reports: [],
    loading: false,
    page: 1,
    hasMore: true,
    searchQuery: '',
    selectedType: '',
    currentFilter: 'all',
    typeList: [
      { value: '', label: '全部' },
      { value: 'career', label: '职业' },
      { value: 'relationship', label: '关系' },
      { value: 'personal', label: '个人' }
    ]
  },

  onLoad() {
    this.checkAndLoadReports();
  },

  async checkAndLoadReports() {
    const isLoggedIn = await this.checkLoginStatus();
    if (!isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }
    this.loadReports();
  },

  async showLoginPrompt() {
    try {
      const result = await wx.pro.showModal({
        title: '请先登录',
        content: '此功能需要登录后才能使用，是否立即登录？',
        confirmText: '立即登录',
        cancelText: '取消'
      });

      if (result.confirm) {
        await this.handleLogin();
      } else {
        // 用户取消，跳转回首页
        await wx.pro.switchTab({ url: '/pages/home/<USER>' });
      }
    } catch (error) {
      console.error('显示登录提示失败:', error);
    }
  },

  async loadReports() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      // 首先获取MBTI测试历史
      const historyData: MBTIResult[] = await mbtiAPI.getHistory();
      
      // 将MBTI测试结果转换为报告格式
      const reportsFromHistory = historyData.map((item) => ({
        id: String(item.id),
        title: `${item.mbtiType} 性格分析报告`,
        mbtiType: item.mbtiType,
        createTime: dayjs(item.createdAt).format('YYYY-MM-DD HH:mm'),
        readStatus: false,
        type: 'personal' as const,
        isPaid: false,
        price: 0
      }));

      // 尝试获取AI生成的报告
      let aiReports: Array<{
        id: string;
        title: string;
        mbtiType: string;
        createTime: string;
        readStatus: boolean;
        type: 'career' | 'relationship' | 'personal';
        isPaid: boolean;
        price: number;
      }> = [];
      try {
        const aiReportsData = await aiAPI.getReports();
        aiReports = aiReportsData.map((item) => ({
          id: `ai_${item.id}`,
          title: item.title,
          mbtiType: item.mbtiType,
          createTime: dayjs(item.createdAt).format('YYYY-MM-DD HH:mm'),
          readStatus: false,
          type: item.type,
          isPaid: item.isPaid,
          price: item.price
        }));
      } catch (aiError) {
        console.warn('AI报告加载失败，仅显示基础报告:', aiError);
      }

      // 合并所有报告并按时间排序
      const allReports = [...reportsFromHistory, ...aiReports].sort((a, b) => 
        dayjs(b.createTime, 'YYYY-MM-DD HH:mm').valueOf() - dayjs(a.createTime, 'YYYY-MM-DD HH:mm').valueOf()
      );

      // 根据搜索条件和类型过滤
      let filteredReports = allReports;
      
      if (this.data.searchQuery) {
        filteredReports = filteredReports.filter(report => 
          report.title.toLowerCase().includes(this.data.searchQuery.toLowerCase()) ||
          report.mbtiType.toLowerCase().includes(this.data.searchQuery.toLowerCase())
        );
      }
      
      if (this.data.selectedType) {
        filteredReports = filteredReports.filter(report => 
          report.type === this.data.selectedType
        );
      }

      // 分页处理
      const pageSize = 10;
      const startIndex = (this.data.page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageReports = filteredReports.slice(startIndex, endIndex);

      this.setData({
        reports: this.data.page === 1 ? pageReports : [...this.data.reports, ...pageReports],
        hasMore: endIndex < filteredReports.length,
        loading: false
      });

    } catch (error) {
      console.error('加载报告列表失败:', error);
      this.setData({ loading: false });
      
      // 使用模拟数据作为fallback
              const mockReports = [
        {
          id: '1',
          title: 'MBTI性格分析报告',
          mbtiType: 'INTJ',
          createTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm'),
          readStatus: false,
          type: 'career' as const,
          isPaid: false,
          price: 0
        },
        {
          id: '2',
          title: '深度性格解读',
          mbtiType: 'INTJ',
          createTime: dayjs().subtract(3, 'day').format('YYYY-MM-DD HH:mm'),
          readStatus: true,
          type: 'relationship' as const,
          isPaid: true,
          price: 9900
        }
      ];

      this.setData({
        reports: this.data.page === 1 ? mockReports : [...this.data.reports, ...mockReports],
        page: this.data.page + 1,
        hasMore: false,
        loading: false
      });

      wx.showToast({
        title: '使用离线数据',
        icon: 'none'
      });
    }
  },

  async handleReportTap(e: WechatMiniprogram.TouchEvent) {
    const { reportId, isPaid } = e.currentTarget.dataset;
    
    if (isPaid && !this.checkCredits()) {
      await this.showCreditsUpgrade();
      return;
    }

    try {
      await wx.navigateTo({ url: `/pages/report/report?reportId=${reportId}` });
    } catch (error) {
      console.error('查看报告失败:', error);
      wx.pro.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  },

  async handleStartTest() {
    try {
      await wx.pro.switchTab({ url: '/pages/home/<USER>' });
    } catch (error) {
      console.error('跳转到首页失败:', error);
      wx.pro.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  },

  checkCredits(): boolean {
    const app = getApp();
    return app.checkAnalysisCredits();
  },

  async showCreditsUpgrade() {
    try {
      const result = await wx.pro.showModal({
        title: '次数不足',
        content: '您的分析次数不足，是否立即充值？',
        confirmText: '立即充值',
        cancelText: '稍后再说'
      });

      if (result.confirm) {
        await wx.navigateTo({ url: '/pages/pay/pay' });
      }
    } catch (error) {
      console.error('显示充值弹窗失败:', error);
    }
  },

  handleSearch(e: WechatMiniprogram.Input) {
    const searchQuery = e.detail.value;
    this.setData({ 
      searchQuery,
      page: 1,
      hasMore: true,
      reports: []
    });
    
    // 防抖处理
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      this.loadReports();
    }, 500);
  },

  handleTypeChange(e: WechatMiniprogram.CustomEvent) {
    const selectedType = e.detail.value;
    this.setData({ 
      selectedType,
      page: 1,
      hasMore: true,
      reports: []
    });
    this.loadReports();
  },

  clearSearch() {
    this.setData({ 
      searchQuery: '',
      page: 1,
      hasMore: true,
      reports: []
    });
    this.loadReports();
  },

  async refreshReports() {
    this.setData({
      reports: [],
      page: 1,
      hasMore: true
    });
    await this.loadReports();
  },

  onReachBottom() {
    if (!this.data.loading && this.data.hasMore) {
      this.setData({ page: this.data.page + 1 });
      this.loadReports();
    }
  },

  async onPullDownRefresh() {
    await this.refreshReports();
    wx.stopPullDownRefresh();
  },

  // 检查登录状态
  async checkLoginStatus(): Promise<boolean> {
    try {
      return await loginManager.checkLoginStatus();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  },

  // 处理登录
  async handleLogin(): Promise<void> {
    try {
      console.log('开始用户授权登录...');
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        console.log('登录成功:', result.type);
        // 登录成功后重新加载数据
        await this.loadReports();
      } else {
        console.error('登录失败:', result.error);
        await wx.pro.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);
      await wx.pro.showToast({
        title: error?.message || '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 处理筛选切换
  handleFilterChange(e: WechatMiniprogram.TouchEvent) {
    const { type } = e.currentTarget.dataset;
    this.setData({ 
      currentFilter: type,
      page: 1,
      hasMore: true,
      reports: []
    });
    this.loadReports();
  },

  // 分享报告
  async handleShare(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    
    try {
      await wx.pro.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    } catch (error) {
      console.error('分享失败:', error);
      await wx.pro.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  },

  // 收藏/取消收藏
  async handleFavorite(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    
    const reportIndex = this.data.reports.findIndex(r => r.id === id);
    if (reportIndex !== -1) {
      const report = this.data.reports[reportIndex];
      const newFavoriteState = !report.isFavorite;
      
      this.setData({
        [`reports[${reportIndex}].isFavorite`]: newFavoriteState
      });
      
      await wx.pro.showToast({
        title: newFavoriteState ? '已收藏' : '已取消收藏',
        icon: 'success',
        duration: 1500
      });
    }
  },

  // 删除报告
  async handleDelete(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    
    try {
      const result = await wx.pro.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除这份报告吗？',
        confirmText: '删除',
        confirmColor: '#FF4D4F'
      });
      
      if (result.confirm) {
        // 从列表中移除
        const reports = this.data.reports.filter(r => r.id !== id);
        this.setData({ reports });
        
        await wx.pro.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('删除失败:', error);
      await wx.pro.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  }
}); 