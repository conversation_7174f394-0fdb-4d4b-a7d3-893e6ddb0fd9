/* 设置页面样式 - 使用CSS变量系统 */

.settings-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.settings-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 设置分组
.settings-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .section-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
    padding-left: var(--spacing-sm);
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4rpx;
      height: 16rpx;
      background: var(--primary-color);
      border-radius: 2rpx;
    }
  }
  
  .settings-list {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1rpx solid var(--border-color-primary);
    box-shadow: var(--shadow-sm);
    
    .settings-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-lg);
      border-bottom: 1rpx solid var(--border-color-secondary);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: var(--background-color-tertiary);
      }
      
      .item-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-color-sub);
        border-radius: var(--border-radius-sm);
        
        .iconfont {
          font-size: var(--font-size-lg);
          color: var(--primary-color);
        }
      }
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .item-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
      
      .item-action {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .item-value {
          font-size: var(--font-size-sm);
          color: var(--text-color-tertiary);
        }
        
        .item-arrow {
          font-size: var(--font-size-lg);
          color: var(--text-color-tertiary);
        }
      }
    }
  }
}

// 主题设置
.theme-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .theme-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1rpx solid var(--border-color-primary);
    box-shadow: var(--shadow-sm);
    
    .theme-header {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-md);
      
      .theme-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-color-sub);
        border-radius: var(--border-radius-sm);
        
        .iconfont {
          font-size: var(--font-size-lg);
          color: var(--primary-color);
        }
      }
      
      .theme-info {
        flex: 1;
        
        .theme-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .theme-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
    }
    
    .theme-options {
      .theme-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md) 0;
        border-bottom: 1rpx solid var(--border-color-secondary);
        
        &:last-child {
          border-bottom: none;
        }
        
        .option-info {
          .option-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-color-primary);
            margin-bottom: var(--spacing-xs);
          }
          
          .option-desc {
            font-size: var(--font-size-sm);
            color: var(--text-color-secondary);
            line-height: var(--line-height-normal);
          }
        }
        
        .option-control {
          .switch {
            position: relative;
            width: 80rpx;
            height: 40rpx;
            background: var(--border-color-primary);
            border-radius: var(--border-radius-full);
            transition: all var(--transition-normal) var(--ease-in-out);
            
            &.active {
              background: var(--primary-color);
            }
            
            .switch-thumb {
              position: absolute;
              top: 2rpx;
              left: 2rpx;
              width: 36rpx;
              height: 36rpx;
              background: var(--text-color-inverse);
              border-radius: var(--border-radius-full);
              transition: all var(--transition-normal) var(--ease-in-out);
              box-shadow: var(--shadow-sm);
            }
            
            &.active .switch-thumb {
              transform: translateX(40rpx);
            }
          }
        }
      }
    }
  }
}

// 关于信息
.about-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .about-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1rpx solid var(--border-color-primary);
    box-shadow: var(--shadow-sm);
    
    .app-logo {
      width: 120rpx;
      height: 120rpx;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      border-radius: var(--border-radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--spacing-md);
      box-shadow: var(--shadow-md);
      
      .iconfont {
        font-size: var(--font-size-4xl);
        color: var(--text-color-inverse);
      }
    }
    
    .app-name {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-xs);
    }
    
    .app-version {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      margin-bottom: var(--spacing-md);
    }
    
    .app-desc {
      font-size: var(--font-size-base);
      color: var(--text-color-secondary);
      line-height: var(--line-height-normal);
      margin-bottom: var(--spacing-lg);
    }
    
    .app-links {
      display: flex;
      justify-content: center;
      gap: var(--spacing-md);
      
      .link-btn {
        flex: 1;
        background: var(--background-color-tertiary);
        color: var(--primary-color);
        border: 1rpx solid var(--primary-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:active {
          background: var(--primary-color);
          color: var(--text-color-inverse);
        }
      }
    }
  }
}

// 调试信息
.debug-section {
  margin: var(--spacing-lg) var(--spacing-md);
  
  .debug-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1rpx solid var(--border-color-primary);
    box-shadow: var(--shadow-sm);
    
    .debug-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);
      
      .debug-title {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-primary);
      }
      
      .debug-toggle {
        font-size: var(--font-size-sm);
        color: var(--primary-color);
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--primary-color-sub);
        border-radius: var(--border-radius-sm);
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:active {
          background: var(--primary-color);
          color: var(--text-color-inverse);
        }
      }
    }
    
    .debug-content {
      .debug-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-sm) 0;
        border-bottom: 1rpx solid var(--border-color-secondary);
        
        &:last-child {
          border-bottom: none;
        }
        
        .debug-label {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
        }
        
        .debug-value {
          font-size: var(--font-size-sm);
          color: var(--text-color-primary);
          font-weight: var(--font-weight-medium);
          
          &.success {
            color: var(--success-color);
          }
          
          &.error {
            color: var(--error-color);
          }
          
          &.warning {
            color: var(--warning-color);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .settings-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .settings-section,
  .theme-section,
  .about-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .settings-item,
  .theme-card,
  .about-card,
  .debug-card {
    padding: var(--spacing-md);
  }
  
  .about-card .app-logo {
    width: 100rpx;
    height: 100rpx;
    
    .iconfont {
      font-size: var(--font-size-3xl);
    }
  }
  
  .app-name {
    font-size: var(--font-size-lg);
  }
  
  .app-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .settings-container {
    background-color: var(--background-color-secondary);
  }
  
  .settings-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .settings-list,
  .theme-card,
  .about-card,
  .debug-card {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .settings-item,
  .theme-option {
    border-bottom-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-tertiary);
    }
  }
  
  .item-icon,
  .theme-icon {
    background: var(--primary-color-sub);
    
    .iconfont {
      color: var(--primary-color);
    }
  }
  
  .app-logo {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .link-btn {
    background: var(--background-color-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
    
    &:active {
      background: var(--primary-color);
      color: var(--text-color-inverse);
    }
  }
  
  .debug-toggle {
    background: var(--primary-color-sub);
    color: var(--primary-color);
    
    &:active {
      background: var(--primary-color);
      color: var(--text-color-inverse);
    }
  }
  
  .debug-item {
    border-bottom-color: var(--border-color-primary);
  }
} 