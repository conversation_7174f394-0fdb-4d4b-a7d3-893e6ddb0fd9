<view class="settings-page u-bg-primary">
  <!-- 用户信息卡片 -->
  <view class="user-card u-bg-elevated u-border-radius-lg u-shadow-md" wx:if="{{userInfo}}">
    <view class="user-info">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name u-text-primary u-font-semibold">{{userInfo.nickName}}</text>
      </view>
    </view>
  </view>

  <!-- 主题设置 -->
  <view class="settings-section u-bg-elevated u-border-radius-lg u-shadow-md">
    <view class="section-title u-text-primary u-font-semibold">主题设置</view>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label u-text-primary">深色模式</text>
        <text class="setting-desc u-text-secondary">使用深色主题保护眼睛</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{isDarkMode}}"
        bindchange="handleThemeChange"
        color="var(--primary-color)"
      />
    </view>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label u-text-primary">跟随系统</text>
        <text class="setting-desc u-text-secondary">自动跟随系统主题设置</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{autoFollowSystem}}"
        bindchange="handleAutoFollowSystemChange"
        color="var(--primary-color)"
      />
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="settings-section u-bg-elevated u-border-radius-lg u-shadow-md">
    <view class="section-title u-text-primary u-font-semibold">应用设置</view>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label u-text-primary">消息通知</text>
        <text class="setting-desc u-text-secondary">接收测试结果和活动通知</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{settings.notification}}"
        data-key="notification"
        bindchange="handleSettingChange"
        color="var(--primary-color)"
      />
    </view>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label u-text-primary">自动保存</text>
        <text class="setting-desc u-text-secondary">自动保存测试进度</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{settings.autoSave}}"
        data-key="autoSave"
        bindchange="handleSettingChange"
        color="var(--primary-color)"
      />
    </view>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label u-text-primary">语言设置</text>
        <text class="setting-desc u-text-secondary">选择应用语言</text>
      </view>
      <picker 
        class="setting-picker" 
        range="{{['简体中文', 'English']}}"
        value="{{settings.language === 'zh-CN' ? 0 : 1}}"
        data-key="language"
        bindchange="handleSettingChange"
      >
        <text class="picker-text u-text-primary">{{settings.language === 'zh-CN' ? '简体中文' : 'English'}}</text>
        <text class="picker-arrow u-text-tertiary">></text>
      </picker>
    </view>
  </view>

  <!-- 功能选项 -->
  <view class="settings-section u-bg-elevated u-border-radius-lg u-shadow-md">
    <view class="section-title u-text-primary u-font-semibold">功能</view>
    
    <view class="setting-item" bindtap="handleClearCache">
      <view class="setting-info">
        <text class="setting-label u-text-primary">清除缓存</text>
        <text class="setting-desc u-text-secondary">清除本地缓存数据</text>
      </view>
      <text class="setting-arrow u-text-tertiary">></text>
    </view>
    
    <view class="setting-item" bindtap="handleAbout">
      <view class="setting-info">
        <text class="setting-label u-text-primary">关于我们</text>
        <text class="setting-desc u-text-secondary">应用版本和开发者信息</text>
      </view>
      <text class="setting-arrow u-text-tertiary">></text>
    </view>
    
    <view class="setting-item" bindtap="handlePrivacy">
      <view class="setting-info">
        <text class="setting-label u-text-primary">隐私政策</text>
        <text class="setting-desc u-text-secondary">了解我们如何保护您的隐私</text>
      </view>
      <text class="setting-arrow u-text-tertiary">></text>
    </view>
    
    <view class="setting-item" bindtap="handleFeedback">
      <view class="setting-info">
        <text class="setting-label u-text-primary">意见反馈</text>
        <text class="setting-desc u-text-secondary">告诉我们您的建议</text>
      </view>
      <text class="setting-arrow u-text-tertiary">></text>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="logout-btn u-bg-error-color u-text-inverse u-border-radius-lg" bindtap="handleLogout">
      <text class="logout-text">退出登录</text>
    </button>
  </view>
</view> 