import { loginManager } from '../../utils/login-manager';
import { themeManager, ThemeMode } from '../../utils/theme-manager';

interface IPageData {
  settings: {
    notification: boolean;
    autoSave: boolean;
    language: string;
  };
  userInfo: any;
  isDarkMode: boolean;
  autoFollowSystem: boolean;
  themeMode: ThemeMode;
}

interface IPageMethods {
  loadSettings(): Promise<void>;
  loadUserInfo(): Promise<void>;
  handleSettingChange(e: any): void;
  handleThemeChange(e: any): Promise<void>;
  handleAutoFollowSystemChange(e: any): Promise<void>;
  handleLogout(): Promise<void>;
  handleClearCache(): Promise<void>;
  handleAbout(): Promise<void>;
  handlePrivacy(): Promise<void>;
  handleFeedback(): Promise<void>;
  updateThemeState(): void;
  setupThemeListener(): void;
  cleanupThemeListener(): void;
}

interface IPageInstance {
  unsubscribeTheme?: () => void;
}

Page<IPageData, IPageMethods & IPageInstance>({
  data: {
    settings: {
      notification: true,
      autoSave: true,
      language: 'zh-CN'
    },
    userInfo: null,
    isDarkMode: false,
    autoFollowSystem: false,
    themeMode: ThemeMode.LIGHT
  },

  onLoad() {
    this.loadSettings();
    this.loadUserInfo();
    this.updateThemeState();
    this.setupThemeListener();
  },

  onUnload() {
    this.cleanupThemeListener();
  },

  async loadSettings() {
    try {
      const settings = wx.getStorageSync('userSettings') || this.data.settings;
      this.setData({ settings });
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  },

  async loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      this.setData({ userInfo });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  handleSettingChange(e: any) {
    const { key } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    const settings = { ...this.data.settings };
    (settings as any)[key] = value;
    
    this.setData({ settings });
    wx.setStorageSync('userSettings', settings);
  },

  async handleThemeChange(e: any) {
    const isDarkMode = e.detail.value;
    const newTheme = isDarkMode ? ThemeMode.DARK : ThemeMode.LIGHT;
    
    try {
      themeManager.setTheme(newTheme);
      this.setData({ 
        isDarkMode,
        themeMode: newTheme
      });
      
      wx.showToast({
        title: isDarkMode ? '已开启深色模式' : '已关闭深色模式',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      console.error('切换主题失败:', error);
      wx.showToast({
        title: '切换失败，请重试',
        icon: 'none'
      });
    }
  },

  async handleAutoFollowSystemChange(e: any) {
    const autoFollowSystem = e.detail.value;
    
    try {
      themeManager.setAutoFollowSystem(autoFollowSystem);
      this.setData({ autoFollowSystem });
      
      wx.showToast({
        title: autoFollowSystem ? '已开启跟随系统' : '已关闭跟随系统',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      console.error('设置跟随系统失败:', error);
      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      });
    }
  },

  async handleLogout() {
    try {
      const result = await wx.pro.showModal({
        title: '确认退出',
        content: '退出登录后，您的测试记录将无法同步，确定要退出吗？',
        confirmText: '确定退出',
        cancelText: '取消'
      });

      if (result.confirm) {
        console.log('开始退出登录...');
        await loginManager.logout();

        // 登录管理器会自动处理页面跳转和状态清理
        // 无需额外处理
      }
    } catch (error: any) {
      console.error('退出登录失败:', error);
      await wx.pro.showToast({
        title: error?.message || '退出失败，请重试',
        icon: 'none'
      });
    }
  },

  async handleClearCache() {
    try {
      const result = await wx.showModal({
        title: '清除缓存',
        content: '确定要清除所有缓存数据吗？',
        confirmText: '清除',
        cancelText: '取消'
      });

      if (result.confirm) {
        // 清除缓存
        wx.clearStorageSync();
        
        wx.showToast({
          title: '缓存已清除',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  },

  async handleAbout() {
    try {
      await wx.navigateTo({ url: '/pages/about/about' });
    } catch (error) {
      console.error('跳转关于页面失败:', error);
    }
  },

  async handlePrivacy() {
    try {
      await wx.navigateTo({ url: '/pages/privacy/privacy' });
    } catch (error) {
      console.error('跳转隐私政策失败:', error);
    }
  },

  async handleFeedback() {
    try {
      await wx.navigateTo({ url: '/pages/feedback/feedback' });
    } catch (error) {
      console.error('跳转意见反馈失败:', error);
    }
  },

  updateThemeState() {
    const themeConfig = themeManager.getThemeConfig();
    this.setData({
      isDarkMode: themeConfig.mode === ThemeMode.DARK,
      autoFollowSystem: themeConfig.autoFollowSystem,
      themeMode: themeConfig.mode
    });
  },

  setupThemeListener() {
    // 订阅主题变化
    this.unsubscribeTheme = themeManager.subscribe((theme: ThemeMode) => {
      this.setData({
        isDarkMode: theme === ThemeMode.DARK,
        themeMode: theme
      });
    });
  },

  cleanupThemeListener() {
    if (this.unsubscribeTheme) {
      this.unsubscribeTheme();
      this.unsubscribeTheme = undefined;
    }
  }
}); 