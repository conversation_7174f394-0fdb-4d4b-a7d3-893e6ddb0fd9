import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  isLoggedIn: boolean;
  loading: boolean;
}

interface ICustomMethods {
  handleStartTest(): Promise<void>;
  handleLogin(): Promise<void>;
  checkLoginStatus(): void;
  onLoginStatusChange(status: LoginStatus, data?: any): void;
  setupLoginListener(): void;
  cleanupLoginListener(): void;
}

interface IPageInstance {
  unsubscribe?: () => void;
}

Page<IPageData, ICustomMethods & IPageInstance>({
  data: {
    isLoggedIn: false,
    loading: false
  },

  onLoad() {
    this.checkLoginStatus();
    this.setupLoginListener();
  },

  onShow() {
    this.checkLoginStatus();
  },

  onUnload() {
    // 清理监听器
    this.cleanupLoginListener();
  },

  // 设置登录状态监听器
  setupLoginListener() {
    // 监听登录状态变化
    this.unsubscribe = loginManager.subscribe((status: LoginStatus, data?: any) => {
      this.onLoginStatusChange(status, data);
    });
  },

  // 清理登录状态监听器
  cleanupLoginListener() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = undefined;
    }
  },

  // 处理登录状态变化
  onLoginStatusChange(status: LoginStatus, data?: any) {
    console.log('首页收到登录状态变化:', status, data);
    
    const isLoggedIn = status === LoginStatus.LOGGED_IN;
    
    this.setData({ 
      isLoggedIn,
      loading: false 
    });

    // 如果登录成功，显示提示
    if (isLoggedIn) {
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      });
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp();
    const isLoggedIn = app.checkLoginStatus();
    this.setData({ isLoggedIn });
  },

  // 开始测试
  async handleStartTest() {
    try {
      // 跳转到测试开始页面
      await wx.navigateTo({ 
        url: '/pages/test/start/test-start' 
      });
    } catch (error) {
      console.error('跳转失败:', error);
      wx.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    }
  },

  // 处理登录
  async handleLogin() {
    try {
      this.setData({ loading: true });
      
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        // 登录成功后的处理由监听器完成
        console.log('登录请求成功，等待状态更新');
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  }
}); 