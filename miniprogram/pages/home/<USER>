/* 首页样式 - 使用CSS变量系统 */
@import '../../styles/variables.scss';
 
.home-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color-primary);
}

// 沉浸式背景
.immersive-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  
  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, 
      var(--primary-color-sub) 0%, 
      rgba(245, 253, 252, 0.8) 30%,
      rgba(245, 253, 252, 0.4) 70%,
      var(--background-color-primary) 100%
    );
  }
  
  .bg-pattern {
    position: absolute;
    top: -200rpx;
    right: -200rpx;
    width: 600rpx;
    height: 600rpx;
    border-radius: 50%;
    background: radial-gradient(circle, 
      rgba(0, 108, 104, 0.05) 0%, 
      transparent 70%
    );
  }
}

// 主内容区
.main-content {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-4xl) var(--spacing-xl) var(--spacing-xl);
}

// Logo 和标题
.header-section {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  
  .logo-container {
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    box-shadow: var(--shadow-lg);
    
    .logo-icon {
      font-size: var(--font-size-4xl);
      color: var(--text-color-inverse);
    }
  }
  
  .app-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    letter-spacing: 4rpx;
  }
  
  .app-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    font-weight: var(--font-weight-normal);
  }
}

// 引导文案
.guide-section {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: var(--spacing-3xl);
  
  .guide-card {
    text-align: center;
    flex: 1;
    
    .guide-number {
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-bold);
      color: var(--primary-color);
      line-height: 1;
      margin-bottom: var(--spacing-xs);
    }
    
    .guide-text {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      font-weight: var(--font-weight-medium);
    }
  }
}

// 介绍文字
.intro-section {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  width: 100%;
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-color-primary);
  
  .intro-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
  }
  
  .intro-text {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    line-height: var(--line-height-relaxed);
    text-align: justify;
  }
}

// 开始测试按钮
.action-section {
  width: 100%;
  margin-top: auto;
  
  .start-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-color-inverse);
    border: none;
    border-radius: var(--border-radius-xl);
    padding: 0;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-xl);
    }
    
    .btn-text {
      font-size: 32rpx;
      margin-right: 12rpx;
    }
    
    .btn-arrow {
      font-size: 28rpx;
      transition: transform 0.3s ease;
    }
    
    &:hover .btn-arrow {
      transform: translateX(4rpx);
    }
  }
  
  .user-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24rpx;
    font-size: 24rpx;
    
    .hint-text {
      color: var(--text-color-secondary);
      margin-right: 8rpx;
    }
    
         .hint-link {
       color: var(--primary-color);
       font-weight: 500;
       
       &:active {
         opacity: 0.8;
       }
       
       &.loading {
         color: var(--text-color-secondary);
         opacity: 0.6;
       }
     }
  }
}

// 登录提示
.login-prompt {
  margin-top: var(--spacing-lg);
  text-align: center;
  
  .login-text {
    font-size: var(--font-size-sm);
    color: var(--text-color-tertiary);
    margin-bottom: var(--spacing-sm);
  }
  
  .login-btn {
    background: var(--background-color-elevated);
    color: var(--primary-color);
    border: 1rpx solid var(--primary-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      background: var(--primary-color);
      color: var(--text-color-inverse);
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl);
  
  .loading-text {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    margin-left: var(--spacing-sm);
  }
}

// 底部装饰
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;
  overflow: hidden;
  
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    
    &.circle-1 {
      width: 300rpx;
      height: 300rpx;
      background: radial-gradient(circle, 
        rgba(0, 108, 104, 0.08) 0%, 
        transparent 70%
      );
      bottom: -150rpx;
      left: -50rpx;
    }
    
    &.circle-2 {
      width: 200rpx;
      height: 200rpx;
      background: radial-gradient(circle, 
        rgba(0, 108, 104, 0.06) 0%, 
        transparent 70%
      );
      bottom: -100rpx;
      right: 100rpx;
    }
    
    &.circle-3 {
      width: 150rpx;
      height: 150rpx;
      background: radial-gradient(circle, 
        rgba(0, 108, 104, 0.04) 0%, 
        transparent 70%
      );
      bottom: -75rpx;
      right: -20rpx;
    }
  }
}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .main-content {
    padding: var(--spacing-2xl) var(--spacing-md) var(--spacing-lg);
  }
  
  .header-section {
    margin-bottom: var(--spacing-2xl);
    
    .logo-container {
      width: 100rpx;
      height: 100rpx;
      
      .logo-icon {
        font-size: var(--font-size-3xl);
      }
    }
    
    .app-title {
      font-size: var(--font-size-2xl);
    }
  }
  
  .guide-section {
    margin-bottom: var(--spacing-2xl);
    
    .guide-card .guide-number {
      font-size: var(--font-size-3xl);
    }
  }
  
  .intro-section {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .home-container {
    background-color: var(--background-color-primary);
  }
  
  .immersive-bg .bg-gradient {
    background: linear-gradient(180deg, 
      var(--primary-color-sub) 0%, 
      rgba(10, 26, 26, 0.8) 30%,
      rgba(10, 26, 26, 0.4) 70%,
      var(--background-color-primary) 100%
    );
  }
  
  .intro-section {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .login-btn {
    background: var(--background-color-elevated);
    border-color: var(--primary-color);
    
    &:active {
      background: var(--primary-color);
    }
  }
}

// 适配 iPhone X 等有底部安全区的设备
@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  .action-section {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
} 