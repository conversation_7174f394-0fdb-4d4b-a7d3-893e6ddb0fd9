/* 单个报告页面样式 - 使用CSS变量系统 */

.report-page {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: var(--spacing-md);
}

.report-container {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
}

.report-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  
  .report-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .report-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    line-height: var(--line-height-normal);
  }
}

.report-content {
  margin-bottom: var(--spacing-xl);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    
    .title-icon {
      margin-right: var(--spacing-sm);
      color: var(--primary-color);
    }
  }
  
  .section-content {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
  }
  
  .mbti-result {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    color: var(--text-color-inverse);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    
    .mbti-type {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--spacing-sm);
    }
    
    .mbti-desc {
      font-size: var(--font-size-base);
      opacity: 0.9;
      line-height: var(--line-height-normal);
    }
  }
  
  .dimension-analysis {
    background: var(--background-color-tertiary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1rpx solid var(--border-color-secondary);
    
    .dimension-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-sm) 0;
      border-bottom: 1rpx solid var(--border-color-secondary);
      
      &:last-child {
        border-bottom: none;
      }
      
      .dimension-label {
        font-size: var(--font-size-base);
        color: var(--text-color-primary);
        font-weight: var(--font-weight-medium);
      }
      
      .dimension-value {
        font-size: var(--font-size-base);
        color: var(--primary-color);
        font-weight: var(--font-weight-semibold);
      }
    }
  }
  
  .analysis-text {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border-left: 4rpx solid var(--primary-color);
    
    .analysis-title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    .analysis-content {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      line-height: var(--line-height-relaxed);
    }
  }
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  
  .action-btn {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-color-inverse);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    text-align: center;
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal) var(--ease-in-out);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-md);
    }
    
    &.secondary-btn {
      background: var(--background-color-elevated);
      color: var(--primary-color);
      border: 2rpx solid var(--primary-color);
      
      &:active {
        background: var(--primary-color-sub);
      }
    }
    
    &.share-btn {
      background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
      
      &:active {
        background: var(--success-color-dark);
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .report-page {
    padding: var(--spacing-sm);
  }
  
  .report-container {
    padding: var(--spacing-lg);
  }
  
  .report-header .report-title {
    font-size: var(--font-size-xl);
  }
  
  .report-content {
    .section-title {
      font-size: var(--font-size-base);
    }
    
    .mbti-result {
      padding: var(--spacing-md);
      
      .mbti-type {
        font-size: var(--font-size-2xl);
      }
    }
    
    .dimension-analysis {
      padding: var(--spacing-md);
    }
    
    .analysis-text {
      padding: var(--spacing-md);
    }
  }
  
  .action-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .action-btn {
      padding: var(--spacing-md);
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .report-page {
    background-color: var(--background-color-secondary);
  }
  
  .report-container {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .mbti-result {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .dimension-analysis {
    background: var(--background-color-tertiary);
    border-color: var(--border-color-primary);
    
    .dimension-item {
      border-bottom-color: var(--border-color-primary);
    }
  }
  
  .analysis-text {
    background: var(--background-color-elevated);
    border-left-color: var(--primary-color);
  }
  
  .action-btn {
    &.secondary-btn {
      background: var(--background-color-elevated);
      color: var(--primary-color);
      border-color: var(--primary-color);
      
      &:active {
        background: var(--primary-color-sub);
      }
    }
  }
} 