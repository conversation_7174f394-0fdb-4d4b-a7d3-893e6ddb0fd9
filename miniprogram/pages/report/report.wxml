<view class="report-container">
  <!-- 最新报告 -->
  <view class="recent-report" wx:if="{{recentReport}}" bindtap="handleRecentReportTap">
    <view class="recent-header">
      <view class="recent-title">最新报告</view>
      <view class="recent-score">{{recentReport.score}}分</view>
    </view>
    <view class="recent-content">
      <image class="recent-image" src="{{recentReport.image}}" mode="aspectFill" />
      <view class="recent-info">
        <view class="recent-type">{{recentReport.type}}</view>
        <view class="recent-name">{{recentReport.title}}</view>
        <view class="recent-desc">{{recentReport.description}}</view>
        <view class="recent-date">{{recentReport.date}}</view>
      </view>
    </view>
  </view>

  <!-- 报告列表 -->
  <view class="report-section">
    <view class="section-header">
      <view class="section-title">所有报告</view>
      <view class="generate-button" bindtap="handleGenerateReport">
        <text class="generate-text">生成新报告</text>
      </view>
    </view>
    
    <view class="report-list">
      <view 
        class="report-item" 
        wx:for="{{reportList}}" 
        wx:key="id"
        bindtap="handleReportTap"
        data-id="{{item.id}}"
      >
        <view class="report-image">
          <image src="{{item.image}}" mode="aspectFill" />
          <view class="new-badge" wx:if="{{item.isNew}}">NEW</view>
        </view>
        <view class="report-info">
          <view class="report-type">{{item.type}}</view>
          <view class="report-title">{{item.title}}</view>
          <view class="report-desc">{{item.description}}</view>
          <view class="report-date">{{item.date}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 