import dayjs from 'dayjs';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IReport {
  id: string;
  title: string;
  type: string;
  date: string;
  description: string;
  image: string;
  isNew: boolean;
}

interface IRecentReport {
  id: string;
  title: string;
  type: string;
  date: string;
  description: string;
  image: string;
  score: number;
}

interface IPageData {
  reportList: IReport[];
  recentReport: IRecentReport | null;
  loading: boolean;
  reportInfo: {
    id: string;
    type: string;
    name: string;
    description: string;
    traits: string[];
  };
  reportTypes: {
    id: string;
    title: string;
    desc: string;
    icon: string;
    url?: string;
    action?: string;
  }[];
  selectedType: string;
}

interface ICustomMethods {
  handleLoadReports(): void;
  handleReportTap(e: WechatMiniprogram.TouchEvent): void;
  handleRecentReportTap(): void;
  handleGenerateReport(): void;
}

Page<IPageData, ICustomMethods>({
  data: {
    reportList: [],
    recentReport: null,
    loading: false,
    reportInfo: {
      id: '',
      type: '',
      name: '',
      description: '',
      traits: []
    },
    reportTypes: [
      {
        id: 'personality',
        title: '性格分析',
        desc: '深入了解你的性格特点',
        icon: 'icon-tupian',
        url: '/pages/report/detail/personality'
      },
      {
        id: 'career',
        title: '职业建议',
        desc: '基于性格的职业发展指导',
        icon: 'icon-dingdan',
        url: '/pages/report/detail/career'
      },
      {
        id: 'relationship',
        title: '人际关系',
        desc: '改善人际关系的建议',
        icon: 'icon-shequ',
        url: '/pages/report/detail/relationship'
      },
      {
        id: 'share',
        title: '分享报告',
        desc: '分享给朋友',
        icon: 'icon-fenxiang',
        action: 'share'
      },
      {
        id: 'save',
        title: '保存报告',
        desc: '保存到本地',
        icon: 'icon-shoucang',
        action: 'save'
      },
      {
        id: 'history',
        title: '历史报告',
        desc: '查看所有报告',
        icon: 'icon-shijian',
        action: 'history'
      }
    ],
    selectedType: 'personality'
  },

  async onLoad() {
    const isLoggedIn = await loginManager.checkLoginStatus();
    if (!isLoggedIn) {
      // 如果未登录，显示提示并跳转到登录
      try {
        const result = await wx.pro.showModal({
          title: '请先登录',
          content: '查看报告功能需要登录后才能使用，是否立即登录？',
          confirmText: '立即登录',
          cancelText: '取消'
        });

        if (result.confirm) {
          // 跳转到登录页面
          await wx.pro.switchTab({ url: '/pages/me/me' });
        } else {
          // 用户取消，跳转回首页
          await wx.pro.switchTab({ url: '/pages/home/<USER>' });
        }
      } catch (error) {
        console.error('显示登录提示失败:', error);
        await wx.pro.switchTab({ url: '/pages/home/<USER>' });
      }
      return;
    }
    
    this.handleLoadReports();
  },

  handleLoadReports() {
    this.setData({ loading: true });
    
    // 模拟数据
    const reportList = [
      {
        id: '1',
        title: 'INTJ建筑师性格分析',
        type: 'MBTI报告',
        date: dayjs().subtract(1, 'day').format('MM-DD'),
        description: '深入了解INTJ性格特点、优势和发展建议',
        image: '/images/report-intj.png',
        isNew: true
      },
      {
        id: '2',
        title: '职业发展建议',
        type: '职业报告',
        date: dayjs().subtract(3, 'day').format('MM-DD'),
        description: '基于MBTI结果的职业选择和规划建议',
        image: '/images/report-career.png',
        isNew: false
      },
      {
        id: '3',
        title: '人际关系分析',
        type: '关系报告',
        date: dayjs().subtract(5, 'day').format('MM-DD'),
        description: '分析你的社交风格和人际关系模式',
        image: '/images/report-relationship.png',
        isNew: false
      }
    ];
    
    const recentReport = {
      id: '1',
      title: 'INTJ建筑师性格分析',
      type: 'MBTI报告',
      date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      description: '深入了解INTJ性格特点、优势和发展建议',
      image: '/images/report-intj.png',
      score: 85
    };
    
    this.setData({
      reportList,
      recentReport,
      loading: false
    });
  },

  handleReportTap(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({ url: `/pages/report/detail/detail?id=${id}` });
  },

  handleRecentReportTap() {
    if (this.data.recentReport) {
      wx.navigateTo({ url: `/pages/report/detail/detail?id=${this.data.recentReport.id}` });
    }
  },

  handleGenerateReport() {
    wx.navigateTo({ url: '/pages/test/start/test-start' });
  }
}); 