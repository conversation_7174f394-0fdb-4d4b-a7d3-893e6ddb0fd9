import dayjs from 'dayjs';
import { mbtiAPI, aiAPI } from '../../utils/api';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  testHistory: Array<{
    id: string;
    mbtiType: string;
    createTime: string;
    score: number;
    duration: number;
  }>;
  reportHistory: Array<{
    id: string;
    title: string;
    createTime: string;
    readStatus: boolean;
  }>;
  currentTab: 'test' | 'report';
  loading: boolean;
  page: number;
  hasMore: boolean;
}

interface IPageMethods {
  loadTestHistory(): Promise<void>;
  loadReportHistory(): Promise<void>;
  handleTabChange(e: any): void;
  handleTestItemTap(e: any): Promise<void>;
  handleReportItemTap(e: any): Promise<void>;
  onReachBottom(): void;
  checkLoginStatus(): Promise<boolean>;
  handleLogin(): Promise<void>;
  checkAndLoadData(): Promise<void>;
  showLoginPrompt(): Promise<void>;
}

Page<IPageData, IPageMethods>({
  data: {
    testHistory: [],
    reportHistory: [],
    currentTab: 'test',
    loading: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.checkAndLoadData();
  },

  async loadTestHistory() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const result = await mbtiAPI.getHistory();
      
      const newHistory = result.map((item: any) => ({
        id: item.id,
        mbtiType: item.mbtiType,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm'),
        score: item.dimensions.IE.score + item.dimensions.NS.score + item.dimensions.TF.score + item.dimensions.PJ.score,
        duration: item.duration || 300
      }));

      this.setData({
        testHistory: [...this.data.testHistory, ...newHistory],
        page: this.data.page + 1,
        hasMore: newHistory.length === 10,
        loading: false
      });
    } catch (error) {
      console.error('加载测试历史失败:', error);
      this.setData({ loading: false });
    }
  },

  async loadReportHistory() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const reports = await aiAPI.getReports();
      
      const newHistory = reports.map((item: any) => ({
        id: item.id,
        title: item.title,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm'),
        readStatus: true // 假设已读
      }));

      this.setData({
        reportHistory: [...this.data.reportHistory, ...newHistory],
        page: this.data.page + 1,
        hasMore: newHistory.length === 10,
        loading: false
      });
    } catch (error) {
      console.error('加载报告历史失败:', error);
      this.setData({ loading: false });
    }
  },

  handleTabChange(e: any) {
    const tab = e.currentTarget.dataset.tab as 'test' | 'report';
    
    this.setData({
      currentTab: tab,
      page: 1,
      hasMore: true,
      testHistory: [],
      reportHistory: []
    });

    if (tab === 'test') {
      this.loadTestHistory();
    } else {
      this.loadReportHistory();
    }
  },

  async handleTestItemTap(e: any) {
    const { testId } = e.currentTarget.dataset;
    try {
      await wx.navigateTo({ url: `/pages/test/result/test-result?testId=${testId}` });
    } catch (error) {
      console.error('查看测试结果失败:', error);
    }
  },

  async handleReportItemTap(e: any) {
    const { reportId } = e.currentTarget.dataset;
    try {
      await wx.navigateTo({ url: `/pages/report/report?reportId=${reportId}` });
    } catch (error) {
      console.error('查看报告失败:', error);
    }
  },

  onReachBottom() {
    if (this.data.currentTab === 'test') {
      this.loadTestHistory();
    } else {
      this.loadReportHistory();
    }
  },

  // 检查登录状态并加载数据
  async checkAndLoadData() {
    const isLoggedIn = await this.checkLoginStatus();
    if (!isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }
    this.loadTestHistory();
  },

  // 显示登录提示
  async showLoginPrompt() {
    try {
      const result = await wx.pro.showModal({
        title: '请先登录',
        content: '历史记录功能需要登录后才能使用，是否立即登录？',
        confirmText: '立即登录',
        cancelText: '取消'
      });

      if (result.confirm) {
        await this.handleLogin();
      } else {
        // 用户取消，跳转回首页
        await wx.pro.switchTab({ url: '/pages/home/<USER>' });
      }
    } catch (error) {
      console.error('显示登录提示失败:', error);
    }
  },

  // 检查登录状态
  async checkLoginStatus(): Promise<boolean> {
    try {
      return await loginManager.checkLoginStatus();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  },

  // 处理登录
  async handleLogin(): Promise<void> {
    try {
      console.log('开始用户授权登录...');
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        console.log('登录成功:', result.type);
        // 登录成功后重新加载数据
        this.loadTestHistory();
      } else {
        console.error('登录失败:', result.error);
        await wx.pro.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);
      await wx.pro.showToast({
        title: error?.message || '登录失败，请重试',
        icon: 'none'
      });
    }
  }
}); 