/* 历史记录页面样式 - 使用CSS变量系统 */

.history-page {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
}

.page-header {
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  text-align: center;
  
  .page-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
  }
}

.tab-bar {
  display: flex;
  background-color: var(--background-color-elevated);
  margin: 0 var(--spacing-lg) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xs);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
  
  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &.active {
      background-color: var(--primary-color);
      box-shadow: var(--shadow-sm);
      
      .tab-text {
        color: var(--text-color-inverse);
        font-weight: var(--font-weight-medium);
      }
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  .tab-text {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.history-content {
  padding: 0 var(--spacing-lg) var(--spacing-xl);
  
  .history-item {
    background-color: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-md);
    }
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      
      .item-title {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--text-color-primary);
      }
      
      .item-time {
        font-size: var(--font-size-sm);
        color: var(--text-color-tertiary);
      }
    }
    
    .item-content {
      margin-bottom: var(--spacing-lg);
      
      .result-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
        
        .mbti-type {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
          color: var(--text-color-inverse);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--border-radius-full);
          box-shadow: var(--shadow-sm);
        }
        
        .score {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          font-weight: var(--font-weight-medium);
        }
      }
      
      .duration-info {
        display: flex;
        justify-content: flex-end;
        
        .duration {
          font-size: var(--font-size-xs);
          color: var(--text-color-tertiary);
        }
      }
    }
    
    .status-info {
      display: flex;
      justify-content: flex-end;
      margin-bottom: var(--spacing-lg);
      
      .status {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-md);
        font-weight: var(--font-weight-medium);
        
        &.read {
          background-color: var(--success-color-sub);
          color: var(--success-color);
        }
        
        &.unread {
          background-color: var(--warning-color-sub);
          color: var(--warning-color);
        }
      }
    }
    
    .item-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--spacing-lg);
      border-top: 1rpx solid var(--border-color-secondary);
      
      .action-text {
        font-size: var(--font-size-sm);
        color: var(--primary-color);
        font-weight: var(--font-weight-medium);
      }
      
      .action-arrow {
        font-size: var(--font-size-sm);
        color: var(--primary-color);
        font-weight: var(--font-weight-medium);
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl) 0;
  text-align: center;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: var(--spacing-lg);
    opacity: 0.6;
    color: var(--text-color-tertiary);
  }
  
  .empty-text {
    font-size: var(--font-size-lg);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
  }
  
  .empty-subtext {
    font-size: var(--font-size-sm);
    color: var(--text-color-tertiary);
    line-height: var(--line-height-normal);
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl) 0;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid var(--background-color-tertiary);
    border-top: 4rpx solid var(--primary-color);
    border-radius: var(--border-radius-full);
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
  }
  
  .loading-text {
    font-size: var(--font-size-sm);
    color: var(--text-color-tertiary);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-complete {
  text-align: center;
  padding: var(--spacing-xl) 0;
  
  .complete-text {
    font-size: var(--font-size-sm);
    color: var(--text-color-tertiary);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .history-page {
    padding: 0 var(--spacing-lg);
  }
  
  .page-header {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
    
    .page-title {
      font-size: var(--font-size-2xl);
    }
  }
  
  .tab-bar {
    margin: 0 var(--spacing-md) var(--spacing-md);
    
    .tab-item {
      padding: var(--spacing-md) var(--spacing-md);
    }
    
    .tab-text {
      font-size: var(--font-size-sm);
    }
  }
  
  .history-content {
    padding: 0 var(--spacing-md) var(--spacing-lg);
    
    .history-item {
      padding: var(--spacing-md);
      
      .item-header .item-title {
        font-size: var(--font-size-base);
      }
      
      .item-content .result-info .mbti-type {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-xs);
      }
    }
  }
  
  .empty-state {
    padding: var(--spacing-3xl) 0;
    
    .empty-icon {
      width: 160rpx;
      height: 160rpx;
    }
    
    .empty-text {
      font-size: var(--font-size-base);
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .history-page {
    background-color: var(--background-color-secondary);
  }
  
  .tab-bar {
    background-color: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .history-item {
    background-color: var(--background-color-elevated);
    border-color: var(--border-color-primary);
    
    .item-actions {
      border-top-color: var(--border-color-primary);
    }
  }
  
  .empty-icon {
    color: var(--text-color-tertiary);
  }
  
  .loading-spinner {
    border-color: var(--background-color-tertiary);
    border-top-color: var(--primary-color);
  }
} 