<view class="history-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">历史记录</text>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{currentTab === 'test' ? 'active' : ''}}" 
      data-tab="test"
      bindtap="handleTabChange"
    >
      <text class="tab-text">测试记录</text>
    </view>
    <view 
      class="tab-item {{currentTab === 'report' ? 'active' : ''}}" 
      data-tab="report"
      bindtap="handleTabChange"
    >
      <text class="tab-text">报告记录</text>
    </view>
  </view>

  <!-- 测试历史 -->
  <view class="history-content" wx:if="{{currentTab === 'test'}}">
    <block wx:if="{{testHistory.length > 0}}">
      <view 
        class="history-item test-item" 
        wx:for="{{testHistory}}" 
        wx:key="id"
        data-test-id="{{item.id}}"
        bindtap="handleTestItemTap"
      >
        <view class="item-header">
          <text class="item-title">MBTI测试</text>
          <text class="item-time">{{item.createTime}}</text>
        </view>
        
        <view class="item-content">
          <view class="result-info">
            <text class="mbti-type">{{item.mbtiType}}</text>
            <text class="score">得分: {{item.score}}</text>
          </view>
          <view class="duration-info">
            <text class="duration">用时: {{item.duration}}分钟</text>
          </view>
        </view>
        
        <view class="item-actions">
          <text class="action-text">查看详情</text>
          <text class="action-arrow">></text>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{testHistory.length === 0 && !loading}}" class="empty-state">
      <text class="iconfont icon-yiwen"></text>
      <view class="empty-title">暂无测试记录</view>
      <view class="empty-description">完成MBTI测试后，您的记录将在这里显示</view>
      <button class="empty-btn" bindtap="handleStartTest">开始测试</button>
    </view>
  </view>

  <!-- 报告历史 -->
  <view class="history-content" wx:if="{{currentTab === 'report'}}">
    <block wx:if="{{reportHistory.length > 0}}">
      <view 
        class="history-item report-item" 
        wx:for="{{reportHistory}}" 
        wx:key="id"
        data-report-id="{{item.id}}"
        bindtap="handleReportItemTap"
      >
        <view class="item-header">
          <text class="item-title">{{item.title}}</text>
          <text class="item-time">{{item.createTime}}</text>
        </view>
        
        <view class="item-content">
          <view class="status-info">
            <text class="status {{item.readStatus ? 'read' : 'unread'}}">
              {{item.readStatus ? '已读' : '未读'}}
            </text>
          </view>
        </view>
        
        <view class="item-actions">
          <text class="action-text">查看详情</text>
          <text class="action-arrow">></text>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{reportHistory.length === 0 && !loading}}" class="empty-state">
      <text class="iconfont icon-yiwen"></text>
      <view class="empty-title">暂无报告记录</view>
      <view class="empty-description">查看报告后，您的记录将在这里显示</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载完成 -->
  <view wx:if="{{!hasMore && (testHistory.length > 0 || reportHistory.length > 0)}}" class="load-complete">
    <text class="complete-text">已加载全部记录</text>
  </view>
</view> 