import dayjs from 'dayjs';
import { mbtiAPI, type MBTIQuestion, type MBTIAnswer } from '../../../utils/api';
import { loginManager } from '../../../utils/login-manager';

interface IPageData {
  currentQuestion: number;
  totalQuestions: number;
  questions: MBTIQuestion[];
  answers: string[];
  loading: boolean;
  testStarted: boolean;
  startTime: number;
  progress: number;
  showProgress: boolean;
}

interface ICustomMethods {
  handleLoadQuestions(): Promise<void>;
  handleStartTest(): void;
  handleAnswerSelect(e: WechatMiniprogram.TouchEvent): void;
  handleFinishTest(): Promise<void>;
  handlePrevQuestion(): void;
  handleRetryLoad(): void;
  updateProgress(): void;
  calculateMBTILocally(answers: MBTIAnswer[]): any;
}

Page<IPageData, ICustomMethods>({
  data: {
    currentQuestion: 0,
    totalQuestions: 0,
    questions: [],
    answers: [],
    loading: false,
    testStarted: false,
    startTime: 0,
    progress: 0,
    showProgress: false
  },

  onLoad() {
    this.handleLoadQuestions();
  },

  async handleLoadQuestions() {
    this.setData({ loading: true });
    
    try {
      // 检查登录状态
      const isLoggedIn = await loginManager.checkLoginStatus();
      if (!isLoggedIn) {
        const result = await wx.pro.showModal({
          title: '请先登录',
          content: '进行测试需要登录后才能保存结果，是否立即登录？',
          confirmText: '立即登录',
          cancelText: '游客测试'
        });

        if (result.confirm) {
          await wx.pro.switchTab({ url: '/pages/me/me' });
          return;
        }
        // 如果选择游客测试，继续执行
      }

      // 从API获取测试题目
      const questions = await mbtiAPI.getQuestions();
      
      if (!questions || questions.length === 0) {
        throw new Error('获取题目失败，题目数量为0');
      }

      // 按orderNum排序确保题目顺序正确
      questions.sort((a, b) => a.orderNum - b.orderNum);

      // 初始化答案数组
      const answers = new Array(questions.length).fill('');
    
    this.setData({
      questions,
      totalQuestions: questions.length,
        answers,
      loading: false
    });

      console.log(`成功加载 ${questions.length} 道题目`);
      
    } catch (error: any) {
      console.error('加载题目失败:', error);
      
      this.setData({ loading: false });
      
      const result = await wx.pro.showModal({
        title: '加载失败',
        content: error?.message || '获取测试题目失败，请检查网络连接',
        confirmText: '重试',
        cancelText: '返回'
      });

      if (result.confirm) {
        this.handleRetryLoad();
      } else {
        await wx.pro.navigateBack();
      }
    }
  },

  handleRetryLoad() {
    this.handleLoadQuestions();
  },

  handleStartTest() {
    this.setData({ 
      testStarted: true,
      currentQuestion: 0,
      startTime: Date.now(),
      showProgress: true
    });
    
    this.updateProgress();
  },

  handleAnswerSelect(e: WechatMiniprogram.TouchEvent) {
    const { option } = e.currentTarget.dataset; // 'A', 'B', 'C', 'D', 或 'E'
    const { currentQuestion, answers, totalQuestions } = this.data;
    
    // 记录答案
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = option;
    
    this.setData({ answers: newAnswers });
    
    // 延迟跳转到下一题，给用户反馈
    setTimeout(() => {
      if (currentQuestion < totalQuestions - 1) {
        this.setData({ currentQuestion: currentQuestion + 1 });
        this.updateProgress();
      } else {
        this.handleFinishTest();
      }
    }, 300);
  },

  async handleFinishTest() {
    const { answers, questions, startTime } = this.data;
    
    // 检查是否所有题目都已回答
    const unansweredCount = answers.filter(answer => !answer).length;
    if (unansweredCount > 0) {
      const result = await wx.pro.showModal({
        title: '还有题目未回答',
        content: `还有 ${unansweredCount} 道题目未回答，确定要提交吗？`,
        confirmText: '继续答题',
        cancelText: '直接提交'
      });

      if (result.confirm) {
        // 回到第一个未回答的题目
        const firstUnanswered = answers.findIndex(answer => !answer);
        this.setData({ currentQuestion: firstUnanswered });
        this.updateProgress();
        return;
      }
    }

    this.setData({ loading: true });

    try {
      // 计算测试时长（秒）
      const duration = Math.floor((Date.now() - startTime) / 1000);
      
      // 构造提交的答案格式
      const formattedAnswers: MBTIAnswer[] = questions.map((question, index) => ({
        questionId: question.id,
        answer: (answers[index] || 'A') as 'A' | 'B' | 'C' | 'D' | 'E'
      }));

      console.log('提交答案:', {
        answers: formattedAnswers,
        duration,
        answeredCount: answers.filter(a => a).length,
        totalQuestions: questions.length
      });

      // 检查是否登录，如果登录了就提交到后端
      const isLoggedIn = await loginManager.checkLoginStatus();
      
      let result = null;
      if (isLoggedIn) {
        try {
          // 提交到后端API
          result = await mbtiAPI.submitAnswers(formattedAnswers, duration);
          console.log('API提交成功:', result);
        } catch (apiError) {
          console.error('API提交失败，使用本地计算:', apiError);
          // API失败时回退到本地计算
          result = this.calculateMBTILocally(formattedAnswers);
        }
      } else {
        // 游客模式，本地计算
        result = this.calculateMBTILocally(formattedAnswers);
        console.log('游客模式，本地计算结果:', result);
      }

      this.setData({ loading: false });
    
    // 跳转到结果页面
      const queryParams = [
        `type=${result.mbtiType}`,
        `fromApi=${isLoggedIn ? 'true' : 'false'}`,
        `duration=${duration}`,
        `answeredCount=${answers.filter(a => a).length}`
      ].join('&');

      await wx.pro.navigateTo({ 
        url: `/pages/test/result/test-result?${queryParams}`
      });
      
    } catch (error: any) {
      console.error('提交测试结果失败:', error);
      this.setData({ loading: false });
      
      await wx.pro.showToast({
        title: error?.message || '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 本地MBTI计算逻辑（备用）
  calculateMBTILocally(answers: MBTIAnswer[]): any {
    const { questions } = this.data;
    const scores = { I: 0, E: 0, N: 0, S: 0, T: 0, F: 0, P: 0, J: 0 };
    
    answers.forEach((answer, index) => {
      const question = questions[index];
      if (!question) return;
      
      // 根据维度和选项计算得分（5选项计分系统）
      const dimension = question.dimension;
      let scoreWeight = 0;
      
      // A=强烈倾向第一维度(+2), B=倾向第一维度(+1), C=中性(0), D=倾向第二维度(+1), E=强烈倾向第二维度(+2)
      switch(answer.answer) {
        case 'A': scoreWeight = 2; break;
        case 'B': scoreWeight = 1; break;
        case 'C': scoreWeight = 0; break;
        case 'D': scoreWeight = -1; break;
        case 'E': scoreWeight = -2; break;
        default: scoreWeight = 0; break;
      }
      
      if (dimension === 'IE') {
        // 正分给I，负分给E
        if (scoreWeight > 0) {
          scores.I += scoreWeight;
        } else if (scoreWeight < 0) {
          scores.E += Math.abs(scoreWeight);
        }
      } else if (dimension === 'NS') {
        // 正分给N，负分给S
        if (scoreWeight > 0) {
          scores.N += scoreWeight;
        } else if (scoreWeight < 0) {
          scores.S += Math.abs(scoreWeight);
        }
      } else if (dimension === 'TF') {
        // 正分给T，负分给F
        if (scoreWeight > 0) {
          scores.T += scoreWeight;
        } else if (scoreWeight < 0) {
          scores.F += Math.abs(scoreWeight);
        }
      } else if (dimension === 'PJ') {
        // 正分给P，负分给J
        if (scoreWeight > 0) {
          scores.P += scoreWeight;
        } else if (scoreWeight < 0) {
          scores.J += Math.abs(scoreWeight);
        }
      }
    });
    
    const mbtiType = [
      scores.I > scores.E ? 'I' : 'E',
      scores.N > scores.S ? 'N' : 'S', 
      scores.T > scores.F ? 'T' : 'F',
      scores.P > scores.J ? 'P' : 'J'
    ].join('');
    
    return {
      mbtiType,
      dimensions: {
        IE: { score: scores.I > scores.E ? scores.I : scores.E, preference: scores.I > scores.E ? 'I' : 'E' },
        NS: { score: scores.N > scores.S ? scores.N : scores.S, preference: scores.N > scores.S ? 'N' : 'S' },
        TF: { score: scores.T > scores.F ? scores.T : scores.F, preference: scores.T > scores.F ? 'T' : 'F' },
        PJ: { score: scores.P > scores.J ? scores.P : scores.J, preference: scores.P > scores.J ? 'P' : 'J' }
      },
      keywords: [],
      description: `本地计算的${mbtiType}类型结果`,
      duration: Math.floor((Date.now() - this.data.startTime) / 1000)
    };
  },

  handlePrevQuestion() {
    const { currentQuestion } = this.data;
    if (currentQuestion > 0) {
      this.setData({ currentQuestion: currentQuestion - 1 });
      this.updateProgress();
    }
  },

  updateProgress() {
    const { currentQuestion, totalQuestions } = this.data;
    const progress = totalQuestions > 0 ? Math.round((currentQuestion / totalQuestions) * 100) : 0;
    this.setData({ progress });
  }
}); 