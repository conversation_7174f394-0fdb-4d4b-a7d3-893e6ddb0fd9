<view class="test-container">
  <!-- 开始测试界面 -->
  <view class="start-section" wx:if="{{!testStarted}}">
    <view class="start-content">
      <view class="test-title">MBTI性格测试</view>
      <view class="test-desc">通过{{totalQuestions}}个问题，了解你的性格类型</view>
      <view class="test-info">
        <view class="info-item">
          <view class="info-icon">⏱️</view>
          <view class="info-text">约5-10分钟</view>
        </view>
        <view class="info-item">
          <view class="info-icon">📊</view>
          <view class="info-text">16种人格类型</view>
        </view>
        <view class="info-item">
          <view class="info-icon">🔒</view>
          <view class="info-text">隐私保护</view>
        </view>
      </view>
      
      <view class="test-tips" wx:if="{{totalQuestions > 0}}">
        <text class="tips-text">已加载 {{totalQuestions}} 道题目，每题有5个选项，请根据真实情况诚实作答</text>
      </view>
      
      <view class="start-button" bindtap="handleStartTest" hover-class="start-button-hover">
        <text class="start-text">开始测试</text>
      </view>
    </view>
  </view>

  <!-- 测试题目界面 -->
  <view class="question-section" wx:if="{{testStarted}}">
    <!-- 进度条 -->
    <view class="progress-section" wx:if="{{showProgress}}">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progress}}%"></view>
      </view>
      <view class="progress-text">{{currentQuestion + 1}} / {{totalQuestions}} ({{progress}}%)</view>
    </view>

    <!-- 题目内容 -->
    <view class="question-content" wx:if="{{questions[currentQuestion]}}">
      <view class="question-number">第 {{currentQuestion + 1}} 题</view>
      <view class="question-title">{{questions[currentQuestion].question}}</view>
      
      <view class="options-list">
        <!-- 5个选项循环显示 -->
        <view 
          wx:for="{{['A', 'B', 'C', 'D', 'E']}}" 
          wx:key="*this"
          class="option-item {{answers[currentQuestion] === item ? 'selected' : ''}}" 
          bindtap="handleAnswerSelect"
          data-option="{{item}}"
          hover-class="option-item-hover"
        >
          <view class="option-label">{{item}}</view>
          <view class="option-text">{{questions[currentQuestion].options[item]}}</view>
          <view class="option-arrow">></view>
        </view>
      </view>
      
      <!-- 维度提示 -->
      <view class="dimension-hint">
        <text class="hint-text">测试维度: {{questions[currentQuestion].dimension}}</text>
        <text class="scoring-hint">A=非常同意 B=同意 C=中性 D=不同意 E=非常不同意</text>
      </view>
    </view>

    <!-- 导航按钮 -->
    <view class="navigation-buttons">
      <view class="prev-button" wx:if="{{currentQuestion > 0}}" bindtap="handlePrevQuestion" hover-class="nav-button-hover">
        <text class="nav-text">上一题</text>
      </view>
      
      <view class="spacer" wx:if="{{currentQuestion === 0}}"></view>
      
      <view class="finish-button" wx:if="{{currentQuestion === totalQuestions - 1}}" bindtap="handleFinishTest" hover-class="nav-button-hover">
        <text class="nav-text">完成测试</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{testStarted ? '提交中...' : '加载题目中...'}}</text>
  </view>
  
  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{!loading && totalQuestions === 0 && !testStarted}}">
    <view class="error-icon">❌</view>
    <text class="error-text">题目加载失败</text>
    <view class="retry-button" bindtap="handleRetryLoad" hover-class="retry-button-hover">
      <text class="retry-text">重新加载</text>
    </view>
  </view>
</view> 