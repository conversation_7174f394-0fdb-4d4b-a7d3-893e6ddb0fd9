@import '../../../styles/variables.scss';

.test-container {
  min-height: 100vh;
  background-color: $background-color;
}

// 开始测试界面
.start-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .start-content {
    text-align: center;
    max-width: 600rpx;
  }
  
  .test-title {
    font-size: 48rpx;
    font-weight: 700;
    color: $text-color;
    margin-bottom: 20rpx;
  }
  
  .test-desc {
    font-size: 28rpx;
    color: $light-text;
    margin-bottom: 60rpx;
    line-height: 1.5;
  }
  
  .test-info {
    margin-bottom: 60rpx;
  }
  
  .info-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;
  }
  
  .info-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
  }
  
  .info-text {
    font-size: 28rpx;
    color: $text-color;
  }
  
  .start-button {
    background: linear-gradient(135deg, $primary-color, #40a9ff);
    border-radius: 50rpx;
    padding: 30rpx 80rpx;
    display: inline-block;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
    
    &:active {
      transform: scale(0.95);
      box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
    }
  }
  
  .start-text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
  }
  
  // 测试提示
  .test-tips {
    background: rgba(24, 144, 255, 0.1);
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 40rpx;
    
    .tips-text {
      font-size: 24rpx;
      color: $primary-color;
      text-align: center;
    }
  }
  
  // 悬停效果
  .start-button-hover {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  }
}

// 测试题目界面
.question-section {
  min-height: 100vh;
  padding: 40rpx 30rpx;
}

// 进度条
.progress-section {
  margin-bottom: 60rpx;
  
  .progress-bar {
    width: 100%;
    height: 8rpx;
    background: $border-color;
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 20rpx;
  }
  
  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, $primary-color, #40a9ff);
    border-radius: 4rpx;
    transition: width 0.3s ease;
  }
  
  .progress-text {
    text-align: center;
    font-size: 24rpx;
    color: $light-text;
  }
}

// 题目内容
.question-content {
  .question-number {
    text-align: center;
    font-size: 24rpx;
    color: $primary-color;
    margin-bottom: 20rpx;
    font-weight: 600;
  }
  
  .question-title {
    font-size: 36rpx;
    font-weight: 600;
    color: $text-color;
    text-align: center;
    margin-bottom: 60rpx;
    line-height: 1.4;
  }
  
  .options-list {
    margin-bottom: 40rpx;
    
    .option-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 30rpx 25rpx;
      margin-bottom: 15rpx;
      display: flex;
      align-items: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
      border: 2rpx solid transparent;
      transition: all 0.3s ease;
      
      // 选中状态
      &.selected {
        background: rgba(24, 144, 255, 0.1);
        border-color: $primary-color;
        box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.2);
        
        .option-label {
          background: $primary-color;
          color: #fff;
        }
        
        .option-text {
          color: $primary-color;
          font-weight: 600;
        }
        
        .option-arrow {
          color: $primary-color;
        }
      }
    }
    
    .option-label {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background: $border-color;
      color: $light-text;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 600;
      margin-right: 25rpx;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }
    
    .option-text {
      flex: 1;
      font-size: 26rpx;
      color: $text-color;
      line-height: 1.4;
      word-break: break-all;
    }
    
    .option-arrow {
      font-size: 24rpx;
      color: $light-text;
      margin-left: 15rpx;
      transition: color 0.3s ease;
      flex-shrink: 0;
    }
  }
  
  // 维度提示
  .dimension-hint {
    text-align: center;
    padding: 25rpx 20rpx;
    background: rgba(24, 144, 255, 0.08);
    border-radius: 12rpx;
    border: 1rpx solid rgba(24, 144, 255, 0.2);
    
    .hint-text {
      font-size: 22rpx;
      color: $light-text;
      display: block;
      margin-bottom: 10rpx;
    }
    
    .scoring-hint {
      font-size: 20rpx;
      color: $primary-color;
      display: block;
      font-weight: 500;
      line-height: 1.3;
    }
  }
}

// 悬停效果
.option-item-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  background: rgba(24, 144, 255, 0.05);
}

// 导航按钮
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 60rpx;
  
  .spacer {
    width: 120rpx;
  }
  
  .prev-button,
  .finish-button {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50rpx;
    padding: 20rpx 40rpx;
    transition: all 0.3s ease;
    
    .nav-text {
    color: $text-color;
    font-size: 24rpx;
  }
  }
  
  .finish-button {
    background: linear-gradient(135deg, $success-color, #73d13d);
    
    .nav-text {
      color: #fff;
      font-weight: 600;
    }
  }
}

.nav-button-hover {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.95);
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid $border-color;
    border-top: 4rpx solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: $light-text;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40rpx;
  
  .error-icon {
    font-size: 80rpx;
    margin-bottom: 30rpx;
  }
  
  .error-text {
    font-size: 28rpx;
    color: $error-color;
    margin-bottom: 40rpx;
    text-align: center;
  }
  
  .retry-button {
    background: linear-gradient(135deg, $primary-color, #40a9ff);
    border-radius: 50rpx;
    padding: 24rpx 60rpx;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
    
    .retry-text {
      color: #fff;
      font-size: 28rpx;
      font-weight: 600;
    }
  }
}

.retry-button-hover {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 测试开始页面样式 - 使用CSS变量系统 */

.test-start-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.test-start-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  // 进度条
  .progress-bar {
    margin-top: var(--spacing-md);
    
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-sm);
      
      .progress-text {
        font-size: var(--font-size-sm);
        color: rgba(255, 255, 255, 0.9);
        font-weight: var(--font-weight-medium);
      }
      
      .progress-number {
        font-size: var(--font-size-sm);
        color: rgba(255, 255, 255, 0.9);
        font-weight: var(--font-weight-semibold);
      }
    }
    
    .progress-track {
      width: 100%;
      height: 8rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius-full);
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: var(--text-color-inverse);
        border-radius: var(--border-radius-full);
        transition: width var(--transition-normal) var(--ease-in-out);
      }
    }
  }
}

// 题目区域
.question-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .question-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1rpx solid var(--border-color-primary);
    
    .question-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
      
      .question-number {
        font-size: var(--font-size-sm);
        color: var(--primary-color);
        font-weight: var(--font-weight-medium);
        margin-bottom: var(--spacing-sm);
      }
      
      .question-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-primary);
        line-height: var(--line-height-relaxed);
      }
    }
    
    .question-content {
      .question-text {
        font-size: var(--font-size-lg);
        color: var(--text-color-primary);
        line-height: var(--line-height-relaxed);
        margin-bottom: var(--spacing-lg);
        text-align: center;
      }
      
      .question-image {
        width: 100%;
        max-width: 400rpx;
        height: 200rpx;
        background: var(--background-color-tertiary);
        border-radius: var(--border-radius-md);
        margin: 0 auto var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1rpx solid var(--border-color-secondary);
        
        .iconfont {
          font-size: var(--font-size-4xl);
          color: var(--text-color-tertiary);
        }
      }
    }
  }
}

// 选项区域
.options-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .options-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    
    .option-item {
      background: var(--background-color-elevated);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-lg);
      border: 2rpx solid var(--border-color-primary);
      transition: all var(--transition-normal) var(--ease-in-out);
      cursor: pointer;
      
      &:active {
        transform: scale(0.98);
        border-color: var(--primary-color);
        background: var(--primary-color-sub);
      }
      
      &.selected {
        border-color: var(--primary-color);
        background: var(--primary-color-sub);
        
        .option-content .option-text {
          color: var(--primary-color);
          font-weight: var(--font-weight-semibold);
        }
      }
      
      .option-content {
        display: flex;
        align-items: center;
        
        .option-icon {
          width: 48rpx;
          height: 48rpx;
          background: var(--background-color-tertiary);
          border-radius: var(--border-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: var(--spacing-md);
          flex-shrink: 0;
          transition: all var(--transition-normal) var(--ease-in-out);
          
          .iconfont {
            font-size: var(--font-size-lg);
            color: var(--text-color-tertiary);
          }
        }
        
        .option-text {
          flex: 1;
          font-size: var(--font-size-base);
          color: var(--text-color-primary);
          line-height: var(--line-height-normal);
          transition: all var(--transition-normal) var(--ease-in-out);
        }
      }
      
      &.selected .option-icon {
        background: var(--primary-color);
        
        .iconfont {
          color: var(--text-color-inverse);
        }
      }
    }
  }
}

// 操作按钮
.action-section {
  padding: 0 var(--spacing-md) var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
  
  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &.prev-btn {
      background: var(--background-color-elevated);
      color: var(--text-color-secondary);
      border: 1rpx solid var(--border-color-primary);
      
      &:active {
        background: var(--background-color-tertiary);
        transform: scale(0.98);
      }
      
      &:disabled {
        opacity: 0.5;
        pointer-events: none;
      }
    }
    
    &.next-btn {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      color: var(--text-color-inverse);
      border: none;
      box-shadow: var(--shadow-md);
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-lg);
      }
      
      &:disabled {
        opacity: 0.5;
        pointer-events: none;
      }
    }
    
    &.submit-btn {
      background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
      color: var(--text-color-inverse);
      border: none;
      box-shadow: var(--shadow-md);
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-lg);
      }
    }
  }
}

// 提示信息
.tip-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .tip-card {
    background: var(--warning-color-sub);
    border: 1rpx solid var(--warning-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    
    .tip-content {
      display: flex;
      align-items: flex-start;
      
      .tip-icon {
        font-size: var(--font-size-lg);
        color: var(--warning-color);
        margin-right: var(--spacing-sm);
        flex-shrink: 0;
      }
      
      .tip-text {
        font-size: var(--font-size-sm);
        color: var(--warning-color-dark);
        line-height: var(--line-height-normal);
      }
    }
  }
}

// 加载状态
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4xl) var(--spacing-lg);
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid var(--border-color-primary);
    border-top: 4rpx solid var(--primary-color);
    border-radius: var(--border-radius-full);
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
  }
  
  .loading-text {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 750rpx) {
  .test-start-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .question-section,
  .options-section,
  .tip-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .question-card {
    padding: var(--spacing-lg);
    
    .question-title {
      font-size: var(--font-size-lg);
    }
    
    .question-text {
      font-size: var(--font-size-base);
    }
  }
  
  .option-item {
    padding: var(--spacing-md);
    
    .option-content .option-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: var(--spacing-sm);
      
      .iconfont {
        font-size: var(--font-size-base);
      }
    }
  }
  
  .action-section {
    padding: 0 var(--spacing-sm) var(--spacing-lg);
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .action-btn {
    height: 80rpx;
    font-size: var(--font-size-base);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .test-start-container {
    background-color: var(--background-color-secondary);
  }
  
  .test-start-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .question-card,
  .option-item {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .option-item {
    &:active {
      background: var(--primary-color-sub);
    }
    
    &.selected {
      background: var(--primary-color-sub);
      border-color: var(--primary-color);
    }
    
    .option-icon {
      background: var(--background-color-tertiary);
      
      .iconfont {
        color: var(--text-color-tertiary);
      }
    }
    
    &.selected .option-icon {
      background: var(--primary-color);
      
      .iconfont {
        color: var(--text-color-inverse);
      }
    }
  }
  
  .prev-btn {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-tertiary);
    }
  }
  
  .tip-card {
    background: var(--warning-color-sub);
    border-color: var(--warning-color);
  }
  
  .loading-spinner {
    border-color: var(--border-color-primary);
    border-top-color: var(--primary-color);
  }
} 