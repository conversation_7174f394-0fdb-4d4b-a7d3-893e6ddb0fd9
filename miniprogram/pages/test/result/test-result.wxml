<view class="result-container">
  <!-- 结果展示 -->
  <view class="result-section" wx:if="{{mbtiInfo}}">
    <!-- MBTI类型卡片 -->
    <view class="mbti-card">
      <view class="mbti-header">
        <view class="mbti-type">{{mbtiType}}</view>
        <view class="mbti-title">{{mbtiInfo.title}}</view>
      </view>
      <view class="mbti-image">
        <image src="{{mbtiInfo.image}}" mode="aspectFit" />
      </view>
      <view class="mbti-desc">{{mbtiInfo.description}}</view>
    </view>

    <!-- 优势分析 -->
    <view class="analysis-section">
      <view class="section-title">性格优势</view>
      <view class="strengths-list">
        <view class="strength-item" wx:for="{{mbtiInfo.strengths}}" wx:key="*this">
          <view class="strength-icon">✨</view>
          <view class="strength-text">{{item}}</view>
        </view>
      </view>
    </view>

    <!-- 发展建议 -->
    <view class="analysis-section">
      <view class="section-title">发展建议</view>
      <view class="weaknesses-list">
        <view class="weakness-item" wx:for="{{mbtiInfo.weaknesses}}" wx:key="*this">
          <view class="weakness-icon">💡</view>
          <view class="weakness-text">{{item}}</view>
        </view>
      </view>
    </view>

    <!-- 职业推荐 -->
    <view class="analysis-section">
      <view class="section-title">适合职业</view>
      <view class="careers-list">
        <view class="career-item" wx:for="{{mbtiInfo.careers}}" wx:key="*this">
          <view class="career-icon">💼</view>
          <view class="career-text">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <view class="action-button primary" bindtap="handleViewDetailedReport">
        <text class="button-text">查看详细报告</text>
      </view>
      <view class="action-button secondary" bindtap="handleShareResult">
        <text class="button-text">分享结果</text>
      </view>
    </view>
    
    <view class="bottom-buttons">
      <view class="bottom-button" bindtap="handleRetakeTest">
        <text class="bottom-text">重新测试</text>
      </view>
      <view class="bottom-button" bindtap="handleGoHome">
        <text class="bottom-text">返回首页</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">生成结果中...</text>
  </view>
</view> 