/* 测试结果页面样式 - 使用CSS变量系统 */

.test-result-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.result-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 结果概览
.result-overview {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .overview-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1rpx solid var(--border-color-primary);
    
    .result-icon {
      width: 120rpx;
      height: 120rpx;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      border-radius: var(--border-radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--spacing-md);
      box-shadow: var(--shadow-lg);
      
      .iconfont {
        font-size: var(--font-size-4xl);
        color: var(--text-color-inverse);
      }
    }
    
    .result-type {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--primary-color);
      margin-bottom: var(--spacing-sm);
      letter-spacing: 2rpx;
    }
    
    .result-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    .result-desc {
      font-size: var(--font-size-base);
      color: var(--text-color-secondary);
      line-height: var(--line-height-normal);
      margin-bottom: var(--spacing-lg);
    }
    
    .result-score {
      display: flex;
      justify-content: center;
      gap: var(--spacing-xl);
      
      .score-item {
        text-align: center;
        
        .score-value {
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-bold);
          color: var(--primary-color);
          margin-bottom: var(--spacing-xs);
        }
        
        .score-label {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
}

// 四维分析
.dimensions-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
  }
  
  .dimensions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    
    .dimension-card {
      background: var(--background-color-elevated);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-lg);
      text-align: center;
      box-shadow: var(--shadow-sm);
      border: 1rpx solid var(--border-color-primary);
      
      .dimension-header {
        margin-bottom: var(--spacing-md);
        
        .dimension-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .dimension-subtitle {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
        }
      }
      
      .dimension-chart {
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto var(--spacing-md);
        position: relative;
        
        .chart-circle {
          width: 100%;
          height: 100%;
          border-radius: var(--border-radius-full);
          background: conic-gradient(
            var(--primary-color) 0deg,
            var(--primary-color) var(--percentage),
            var(--border-color-secondary) var(--percentage),
            var(--border-color-secondary) 360deg
          );
          display: flex;
          align-items: center;
          justify-content: center;
          
          .chart-center {
            width: 80rpx;
            height: 80rpx;
            background: var(--background-color-elevated);
            border-radius: var(--border-radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            
            .chart-value {
              font-size: var(--font-size-lg);
              font-weight: var(--font-weight-bold);
              color: var(--primary-color);
            }
          }
        }
      }
      
      .dimension-result {
        .result-label {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .result-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
    }
  }
}

// 详细分析
.analysis-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .analysis-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1rpx solid var(--border-color-primary);
    
    .analysis-header {
      text-align: center;
      margin-bottom: var(--spacing-lg);
      
      .analysis-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-primary);
        margin-bottom: var(--spacing-sm);
      }
      
      .analysis-subtitle {
        font-size: var(--font-size-base);
        color: var(--text-color-secondary);
        line-height: var(--line-height-normal);
      }
    }
    
    .analysis-content {
      .analysis-item {
        margin-bottom: var(--spacing-lg);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-sm);
          display: flex;
          align-items: center;
          
          .item-icon {
            width: 32rpx;
            height: 32rpx;
            background: var(--primary-color-sub);
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-sm);
            
            .iconfont {
              font-size: var(--font-size-sm);
              color: var(--primary-color);
            }
          }
        }
        
        .item-content {
          font-size: var(--font-size-base);
          color: var(--text-color-secondary);
          line-height: var(--line-height-relaxed);
          padding-left: 48rpx;
        }
      }
    }
  }
}

// 操作按钮
.action-section {
  padding: 0 var(--spacing-md) var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  
  .action-btn {
    width: 100%;
    height: 88rpx;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &.primary-btn {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      color: var(--text-color-inverse);
      border: none;
      box-shadow: var(--shadow-md);
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-lg);
      }
    }
    
    &.secondary-btn {
      background: var(--background-color-elevated);
      color: var(--primary-color);
      border: 1rpx solid var(--primary-color);
      
      &:active {
        background: var(--primary-color-sub);
        transform: scale(0.98);
      }
    }
    
    &.share-btn {
      background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
      color: var(--text-color-inverse);
      border: none;
      box-shadow: var(--shadow-md);
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-lg);
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .result-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .result-overview,
  .dimensions-section,
  .analysis-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .overview-card {
    padding: var(--spacing-lg);
    
    .result-icon {
      width: 100rpx;
      height: 100rpx;
      
      .iconfont {
        font-size: var(--font-size-3xl);
      }
    }
    
    .result-type {
      font-size: var(--font-size-2xl);
    }
    
    .result-score {
      flex-direction: column;
      gap: var(--spacing-md);
    }
  }
  
  .dimensions-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .dimension-card {
    padding: var(--spacing-md);
    
    .dimension-chart {
      width: 100rpx;
      height: 100rpx;
      
      .chart-center {
        width: 60rpx;
        height: 60rpx;
        
        .chart-value {
          font-size: var(--font-size-base);
        }
      }
    }
  }
  
  .analysis-card {
    padding: var(--spacing-lg);
    
    .analysis-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .action-section {
    padding: 0 var(--spacing-sm) var(--spacing-lg);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .test-result-container {
    background-color: var(--background-color-secondary);
  }
  
  .result-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .overview-card,
  .dimension-card,
  .analysis-card {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .result-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .dimension-chart .chart-circle {
    background: conic-gradient(
      var(--primary-color) 0deg,
      var(--primary-color) var(--percentage),
      var(--border-color-primary) var(--percentage),
      var(--border-color-primary) 360deg
    );
    
    .chart-center {
      background: var(--background-color-elevated);
    }
  }
  
  .item-icon {
    background: var(--primary-color-sub);
    
    .iconfont {
      color: var(--primary-color);
    }
  }
  
  .secondary-btn {
    background: var(--background-color-elevated);
    border-color: var(--primary-color);
    
    &:active {
      background: var(--primary-color-sub);
    }
  }
} 