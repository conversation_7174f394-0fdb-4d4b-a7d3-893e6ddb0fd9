import dayjs from 'dayjs';

interface IMBTIInfo {
  type: string;
  title: string;
  description: string;
  strengths: string[];
  weaknesses: string[];
  careers: string[];
  image: string;
}

interface IPageData {
  mbtiType: string;
  mbtiInfo: IMBTIInfo | null;
  answers: string[];
  loading: boolean;
  resultInfo: {
    type: string;
    name: string;
    description: string;
    traits: string[];
  };
  mbtiTypes: {
    type: string;
    name: string;
    description: string;
    icon: string;
  }[];
  resultActions: {
    id: string;
    title: string;
    desc: string;
    icon: string;
  }[];
}

interface ICustomMethods {
  handleLoadResult(): void;
  handleLoadMBTIInfo(type: string): void;
  handleViewDetailedReport(): void;
  handleShareResult(): void;
  handleRetakeTest(): void;
  handleGoHome(): void;
}

Page<IPageData, ICustomMethods>({
  data: {
    mbtiType: '',
    mbtiInfo: null,
    answers: [],
    loading: false,
    resultInfo: {
      type: '',
      name: '',
      description: '',
      traits: []
    },
    mbtiTypes: [
      {
        type: 'INTJ',
        name: '建筑师',
        description: '富有想象力和战略性的思考者',
        icon: 'icon-zhengyan'
      },
      {
        type: 'INTP',
        name: '逻辑学家',
        description: '具有创新性的发明家',
        icon: 'icon-yiwen'
      },
      {
        type: 'ENTJ',
        name: '指挥官',
        description: '大胆、富有想象力的领导者',
        icon: 'icon-dianzan'
      },
      {
        type: 'ENTP',
        name: '辩论家',
        description: '聪明好奇的思想家',
        icon: 'icon-tiezi'
      }
    ],
    resultActions: [
      {
        id: 'save',
        title: '保存结果',
        desc: '保存测试结果',
        icon: 'icon-shoucang'
      },
      {
        id: 'share',
        title: '分享结果',
        desc: '分享给朋友',
        icon: 'icon-fenxiang'
      },
      {
        id: 'report',
        title: '详细报告',
        desc: '查看完整报告',
        icon: 'icon-tupian'
      },
      {
        id: 'retest',
        title: '重新测试',
        desc: '进行新的测试',
        icon: 'icon-tianxie'
      }
    ]
  },

  onLoad(options: any) {
    this.setData({ 
      mbtiType: options.type || 'INTJ',
      answers: options.answers ? JSON.parse(options.answers) : [],
      loading: true
    });
    
    this.handleLoadMBTIInfo(options.type || 'INTJ');
  },

  handleLoadResult() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const { type, answers } = currentPage.options;
    
    this.setData({ 
      mbtiType: type || 'INTJ',
      answers: answers ? JSON.parse(answers) : [],
      loading: true
    });
    
    this.handleLoadMBTIInfo(type || 'INTJ');
  },

  handleLoadMBTIInfo(type: string) {
    // 模拟MBTI类型信息
    const mbtiData = {
      'INTJ': {
        type: 'INTJ',
        title: '建筑师',
        description: '富有想象力和战略性的思考者，一切都要经过深思熟虑。',
        strengths: ['战略思维', '独立自主', '追求完美', '知识渊博'],
        weaknesses: ['过于完美主义', '不善于表达情感', '可能显得傲慢'],
        careers: ['科学家', '工程师', '投资顾问', '战略分析师'],
        image: '/images/mbti-intj.png'
      },
      'INTP': {
        type: 'INTP',
        title: '逻辑学家',
        description: '创新的发明家，对知识有着不可抑制的渴望。',
        strengths: ['逻辑分析', '创新思维', '客观理性', '追求真理'],
        weaknesses: ['可能过于理论化', '不善于处理情感', '容易拖延'],
        careers: ['程序员', '哲学家', '研究员', '数学家'],
        image: '/images/mbti-intp.png'
      },
      'ENTJ': {
        type: 'ENTJ',
        title: '指挥官',
        description: '大胆、富有想象力的领导者，总是能找到或创造解决方案。',
        strengths: ['领导能力', '决策果断', '高效执行', '战略眼光'],
        weaknesses: ['可能过于专制', '不耐心', '过于直接'],
        careers: ['企业家', '高管', '律师', '政治家'],
        image: '/images/mbti-entj.png'
      },
      'ENTP': {
        type: 'ENTP',
        title: '辩论家',
        description: '聪明好奇的思想家，不会放过任何智力挑战。',
        strengths: ['创新思维', '适应性强', '善于辩论', '多才多艺'],
        weaknesses: ['可能不够专注', '容易厌倦', '过于好辩'],
        careers: ['创业者', '记者', '律师', '营销专家'],
        image: '/images/mbti-entp.png'
      }
    };
    
    const mbtiInfo = mbtiData[type as keyof typeof mbtiData] || mbtiData['INTJ'];
    
    this.setData({
      mbtiInfo,
      loading: false
    });
  },

  async handleViewDetailedReport() {
    try {
      await wx.pro.navigateTo({ 
      url: `/pages/report/detail/detail?type=${this.data.mbtiType}`
    });
    } catch (error) {
      console.error('跳转到详细报告失败:', error);
      wx.pro.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    }
  },

  async handleShareResult() {
    try {
      await wx.pro.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    } catch (error) {
      console.error('显示分享菜单失败:', error);
      wx.pro.showToast({
        title: '分享功能暂不可用',
        icon: 'none'
      });
    }
  },

  async handleRetakeTest() {
    try {
      await wx.pro.navigateTo({ url: '/pages/test/start/test-start' });
    } catch (error) {
      console.error('跳转到测试页面失败:', error);
      wx.pro.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    }
  },

  async handleGoHome() {
    try {
      await wx.pro.switchTab({ url: '/pages/home/<USER>' });
    } catch (error) {
      console.error('跳转到首页失败:', error);
      wx.pro.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    }
  },

  onShareAppMessage() {
    return {
      title: `我的MBTI类型是${this.data.mbtiType} - ${this.data.mbtiInfo?.title}`,
      path: '/pages/home/<USER>',
      imageUrl: this.data.mbtiInfo?.image
    };
  }
}); 