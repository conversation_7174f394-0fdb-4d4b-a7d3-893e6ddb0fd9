import dayjs from 'dayjs';
import { request } from '../../utils/request';
import { anonymousManager } from '../../utils/anonymous';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  isAnonymous: boolean;
  anonymousId: string;
  userInfo: any;
  testHistory: Array<{
    id: string;
    mbtiType: string;
    createTime: string;
    score: number;
  }>;
  loading: boolean;
}

interface ITestMethods {
  initPage(): Promise<void>;
  handleStartTest(): Promise<void>;
  showAnonymousOption(): Promise<void>;
  createAnonymousUser(): Promise<void>;
  navigateToLogin(): Promise<void>;
  startTest(): Promise<void>;
  loadTestHistory(): Promise<void>;
  handleHistoryItemTap(e: any): Promise<void>;
  handleViewAllHistory(): Promise<void>;
}

Page<IPageData, ITestMethods>({
  data: {
    isAnonymous: false,
    anonymousId: '',
    userInfo: null,
    testHistory: [],
    loading: false
  },

  onLoad() {
    this.initPage();
  },

  onShow() {
    this.loadTestHistory();
  },

  async initPage() {
    const isAnonymous = wx.getStorageSync('isAnonymous') || false;
    const anonymousId = wx.getStorageSync('anonymousId') || '';
    const app = getApp();
    
    this.setData({
      isAnonymous,
      anonymousId,
      userInfo: app.getCurrentUser()
    });
  },

  async handleStartTest() {
    const app = getApp();
    const isLoggedIn = app.checkLoginStatus();
    
    if (!isLoggedIn && !this.data.isAnonymous) {
      await this.showAnonymousOption();
      return;
    }

    await this.startTest();
  },

  async showAnonymousOption() {
    try {
      const result = await wx.showModal({
        title: '选择测试方式',
        content: '您可以选择匿名测试或登录后测试',
        confirmText: '匿名测试',
        cancelText: '登录测试'
      });

      if (result.confirm) {
        await this.createAnonymousUser();
      } else {
        await this.navigateToLogin();
      }
    } catch (error) {
      console.error('选择测试方式失败:', error);
    }
  },

  async createAnonymousUser() {
    try {
      const nickName = `匿名用户${Math.floor(Math.random() * 10000)}`;
      const userId = await anonymousManager.createAnonymous(nickName);
      
      this.setData({
        isAnonymous: true,
        anonymousId: userId
      });

      await this.startTest();
    } catch (error) {
      console.error('创建匿名用户失败:', error);
      wx.showToast({ title: '创建失败', icon: 'none' });
    }
  },

  async navigateToLogin() {
    try {
      await wx.pro.switchTab({ url: '/pages/me/me' });
    } catch (error) {
      console.error('跳转登录失败:', error);
    }
  },

  async startTest() {
    try {
      await wx.navigateTo({ url: '/pages/test/start/test-start' });
    } catch (error) {
      console.error('开始测试失败:', error);
      wx.showToast({ title: '启动失败', icon: 'none' });
    }
  },

  async loadTestHistory() {
    const app = getApp();
    const userInfo = app.getCurrentUser();
    
    if (!userInfo?.id && !this.data.isAnonymous) return;

    this.setData({ loading: true });

    try {
      const userId = userInfo?.id || this.data.anonymousId;
      const response = await request({
        url: `/test/history?userId=${userId}&page=1&size=5`,
        method: 'GET'
      });
      
      const formattedHistory = response.data.list.map((item: any) => ({
        ...item,
        createTime: dayjs(item.createTime).format('MM-DD HH:mm')
      }));

      this.setData({
        testHistory: formattedHistory,
        loading: false
      });
    } catch (error) {
      console.error('加载测试历史失败:', error);
      this.setData({ loading: false });
    }
  },

  async handleHistoryItemTap(e: any) {
    const { testId } = e.currentTarget.dataset;
    try {
      await wx.navigateTo({ url: `/pages/test/result/test-result?testId=${testId}` });
    } catch (error) {
      console.error('查看历史记录失败:', error);
    }
  },

  async handleViewAllHistory() {
    try {
      await wx.navigateTo({ url: '/pages/history/history' });
    } catch (error) {
      console.error('查看全部历史失败:', error);
    }
  }
}); 