<view class="test-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">MBTI人格测试</text>
    <text class="page-subtitle">了解自己的人格类型，发现更好的自己</text>
  </view>

  <!-- 开始测试按钮 -->
  <view class="start-section">
    <button class="start-btn" bindtap="handleStartTest">
      <!-- <image class="start-icon" src="" mode="aspectFit"></image> -->
      <text class="start-text">开始测试</text>
    </button>
    <text class="start-desc">约需15-20分钟完成</text>
  </view>

  <!-- 测试说明 -->
  <view class="intro-section">
    <view class="intro-title">
      <text class="title-text">测试说明</text>
    </view>
    
    <view class="intro-list">
      <view class="intro-item">
        <text class="item-icon">📝</text>
        <text class="item-text">包含93道专业题目</text>
      </view>
      <view class="intro-item">
        <text class="item-icon">⏱️</text>
        <text class="item-text">建议在安静环境下完成</text>
      </view>
      <view class="intro-item">
        <text class="item-icon">🎯</text>
        <text class="item-text">根据第一感觉选择答案</text>
      </view>
      <view class="intro-item">
        <text class="item-icon">📊</text>
        <text class="item-text">获得详细的人格分析报告</text>
      </view>
    </view>
  </view>

  <!-- 测试历史 -->
  <view class="history-section" wx:if="{{testHistory.length > 0}}">
    <view class="history-header">
      <text class="history-title">最近测试</text>
      <text class="view-all" bindtap="handleViewAllHistory">查看全部</text>
    </view>
    
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{testHistory}}" 
        wx:key="id"
        data-test-id="{{item.id}}"
        bindtap="handleHistoryItemTap"
      >
        <view class="history-info">
          <text class="history-type">{{item.mbtiType}}</text>
          <text class="history-time">{{item.createTime}}</text>
        </view>
        <view class="history-score">
          <text class="score-text">得分: {{item.score}}</text>
        </view>
        <text class="history-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 用户状态提示 -->
  <view class="status-section" wx:if="{{isAnonymous}}">
    <view class="status-card">
      <text class="status-icon">👤</text>
      <view class="status-info">
        <text class="status-title">匿名测试模式</text>
        <text class="status-desc">您的测试结果将仅保存在本地</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 