/* 测试页面样式 - 使用CSS变量系统 */

.test-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.test-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 测试介绍卡片
.intro-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .intro-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1rpx solid var(--border-color-primary);
    
    .intro-header {
      text-align: center;
      margin-bottom: var(--spacing-lg);
      
      .intro-icon {
        width: 120rpx;
        height: 120rpx;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        box-shadow: var(--shadow-lg);
        
        .iconfont {
          font-size: var(--font-size-4xl);
          color: var(--text-color-inverse);
        }
      }
      
      .intro-title {
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-color-primary);
        margin-bottom: var(--spacing-sm);
      }
      
      .intro-subtitle {
        font-size: var(--font-size-base);
        color: var(--text-color-secondary);
        line-height: var(--line-height-normal);
      }
    }
    
    .intro-content {
      .intro-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: var(--spacing-md);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-icon {
          width: 48rpx;
          height: 48rpx;
          background: var(--primary-color-sub);
          border-radius: var(--border-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: var(--spacing-md);
          flex-shrink: 0;
          
          .iconfont {
            font-size: var(--font-size-lg);
            color: var(--primary-color);
          }
        }
        
        .item-content {
          flex: 1;
          
          .item-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-color-primary);
            margin-bottom: var(--spacing-xs);
          }
          
          .item-desc {
            font-size: var(--font-size-sm);
            color: var(--text-color-secondary);
            line-height: var(--line-height-normal);
          }
        }
      }
    }
  }
}

// 测试类型选择
.test-types {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .types-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .type-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      transform: translateY(-2rpx);
      box-shadow: var(--shadow-md);
    }
    
    .type-icon {
      width: 80rpx;
      height: 80rpx;
      background: var(--primary-color-sub);
      border-radius: var(--border-radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--spacing-md);
      
      .iconfont {
        font-size: var(--font-size-2xl);
        color: var(--primary-color);
      }
    }
    
    .type-title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-xs);
    }
    
    .type-desc {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      line-height: var(--line-height-normal);
      margin-bottom: var(--spacing-md);
    }
    
    .type-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--font-size-xs);
      color: var(--text-color-tertiary);
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        
        .iconfont {
          font-size: var(--font-size-xs);
        }
      }
    }
    
    // 推荐标识
    &.recommended {
      border-color: var(--primary-color);
      background: linear-gradient(135deg, var(--background-color-elevated), var(--primary-color-sub));
      
      .recommend-badge {
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        background: var(--primary-color);
        color: var(--text-color-inverse);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
      }
    }
  }
}

// 历史测试记录
.history-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      position: relative;
      padding-left: var(--spacing-md);
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4rpx;
        height: 20rpx;
        background: var(--primary-color);
        border-radius: 2rpx;
      }
    }
    
    .view-all-btn {
      font-size: var(--font-size-sm);
      color: var(--primary-color);
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-color-sub);
      border-radius: var(--border-radius-sm);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:active {
        background: var(--primary-color);
        color: var(--text-color-inverse);
      }
    }
  }
  
  .history-list {
    .history-item {
      background: var(--background-color-elevated);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      box-shadow: var(--shadow-sm);
      border: 1rpx solid var(--border-color-primary);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:active {
        transform: translateX(4rpx);
        box-shadow: var(--shadow-md);
      }
      
      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
        
        .history-date {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
        }
        
        .history-type {
          font-size: var(--font-size-sm);
          color: var(--primary-color);
          background: var(--primary-color-sub);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--border-radius-sm);
          font-weight: var(--font-weight-medium);
        }
      }
      
      .history-content {
        .history-result {
          font-size: var(--font-size-base);
          color: var(--text-color-secondary);
          margin-bottom: var(--spacing-sm);
        }
        
        .history-score {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          font-size: var(--font-size-sm);
          color: var(--text-color-tertiary);
          
          .score-value {
            font-weight: var(--font-weight-semibold);
            color: var(--primary-color);
          }
        }
      }
    }
  }
}

// 开始测试按钮
.action-section {
  padding: 0 var(--spacing-md) var(--spacing-xl);
  
  .start-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-color-inverse);
    border: none;
    border-radius: var(--border-radius-xl);
    padding: 0;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-xl);
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .test-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .intro-section,
  .test-types,
  .history-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .intro-card {
    padding: var(--spacing-lg);
    
    .intro-header .intro-icon {
      width: 100rpx;
      height: 100rpx;
      
      .iconfont {
        font-size: var(--font-size-3xl);
      }
    }
    
    .intro-title {
      font-size: var(--font-size-xl);
    }
  }
  
  .types-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .type-card {
    padding: var(--spacing-md);
    
    .type-icon {
      width: 60rpx;
      height: 60rpx;
      
      .iconfont {
        font-size: var(--font-size-xl);
      }
    }
  }
  
  .action-section {
    padding: 0 var(--spacing-sm) var(--spacing-lg);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .test-container {
    background-color: var(--background-color-secondary);
  }
  
  .test-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .intro-card,
  .type-card,
  .history-item {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .intro-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .type-icon {
    background: var(--primary-color-sub);
    
    .iconfont {
      color: var(--primary-color);
    }
  }
  
  .item-icon {
    background: var(--primary-color-sub);
    
    .iconfont {
      color: var(--primary-color);
    }
  }
  
  .view-all-btn {
    background: var(--primary-color-sub);
    color: var(--primary-color);
    
    &:active {
      background: var(--primary-color);
      color: var(--text-color-inverse);
    }
  }
  
  .history-type {
    background: var(--primary-color-sub);
    color: var(--primary-color);
  }
} 