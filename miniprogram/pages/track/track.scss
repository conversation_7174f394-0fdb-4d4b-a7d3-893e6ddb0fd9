/* 轨迹页面样式 - 使用CSS变量系统 */

.track-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.track-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  // 时间段切换
  .period-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    padding: 4rpx;
    margin-top: var(--spacing-md);
    
    .period-tab {
      flex: 1;
      text-align: center;
      padding: var(--spacing-md) 0;
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.7);
      font-weight: var(--font-weight-medium);
      border-radius: var(--border-radius-sm);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &.active {
        background: rgba(255, 255, 255, 0.9);
        color: var(--primary-color);
        font-weight: var(--font-weight-semibold);
      }
    }
  }
}

// 统计卡片
.statistics-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .stat-item {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
    
    .stat-number {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--primary-color);
      margin-bottom: var(--spacing-xs);
      line-height: 1;
    }
    
    .stat-label {
      font-size: var(--font-size-sm);
      color: var(--text-color-secondary);
      font-weight: var(--font-weight-medium);
    }
  }
}

// 轨迹列表
.track-section {
  padding: 0 var(--spacing-md) var(--spacing-md);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      position: relative;
      padding-left: var(--spacing-md);
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4rpx;
        height: 20rpx;
        background: var(--primary-color);
        border-radius: 2rpx;
      }
    }
    
    .add-button {
      width: 64rpx;
      height: 64rpx;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
      border-radius: var(--border-radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: var(--shadow-md);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:active {
        transform: scale(0.95);
        box-shadow: var(--shadow-lg);
      }
      
      .iconfont {
        color: var(--text-color-inverse);
        font-size: var(--font-size-lg);
      }
    }
  }
  
  // 时间轴样式
  .track-timeline {
    position: relative;
    padding-left: var(--spacing-xl);
    
    &::before {
      content: '';
      position: absolute;
      left: 12rpx;
      top: 0;
      bottom: 0;
      width: 2rpx;
      background: var(--border-color-primary);
    }
  }
  
  .track-item {
    position: relative;
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    border: 1rpx solid var(--border-color-primary);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      transform: translateX(4rpx);
      box-shadow: var(--shadow-md);
    }
    
    // 时间轴节点
    &::before {
      content: '';
      position: absolute;
      left: -34rpx;
      top: 32rpx;
      width: 20rpx;
      height: 20rpx;
      background: var(--background-color-elevated);
      border: 4rpx solid var(--primary-color);
      border-radius: var(--border-radius-full);
      z-index: 1;
    }
    
    .track-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-sm);
      
      .track-date {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        color: var(--text-color-primary);
      }
      
      .track-type {
        font-size: var(--font-size-sm);
        color: var(--primary-color);
        font-weight: var(--font-weight-semibold);
        background: var(--primary-color-sub);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
      }
    }
    
    .track-content {
      .track-description {
        font-size: var(--font-size-base);
        color: var(--text-color-secondary);
        line-height: var(--line-height-normal);
        margin-bottom: var(--spacing-sm);
      }
      
      .track-keywords {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
        
        .keyword {
          font-size: var(--font-size-xs);
          color: var(--text-color-tertiary);
          background: var(--background-color-tertiary);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--border-radius-sm);
        }
      }
    }
    
    .track-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: var(--spacing-sm);
      padding-top: var(--spacing-sm);
      border-top: 1rpx solid var(--border-color-secondary);
      
      .track-score {
        font-size: var(--font-size-sm);
        color: var(--text-color-secondary);
      }
      
      .track-actions {
        display: flex;
        gap: var(--spacing-sm);
        
        .action-btn {
          font-size: var(--font-size-xs);
          color: var(--text-color-tertiary);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--border-radius-sm);
          background: var(--background-color-tertiary);
          transition: all var(--transition-fast) var(--ease-in-out);
          
          &:active {
            background: var(--primary-color);
            color: var(--text-color-inverse);
          }
        }
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: var(--spacing-4xl) var(--spacing-lg);
  
  .empty-icon {
    font-size: var(--font-size-4xl);
    color: var(--text-color-tertiary);
    margin-bottom: var(--spacing-lg);
  }
  
  .empty-title {
    font-size: var(--font-size-lg);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
  }
  
  .empty-desc {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-xl);
  }
  
  .start-btn {
    background: var(--primary-color);
    color: var(--text-color-inverse);
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      transform: scale(0.98);
      background: var(--primary-color-dark);
    }
  }
}

// 错误状态
.error-state {
  text-align: center;
  padding: var(--spacing-4xl) var(--spacing-lg);
  
  .error-icon {
    font-size: var(--font-size-4xl);
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
  }
  
  .error-title {
    font-size: var(--font-size-lg);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
  }
  
  .error-desc {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-xl);
  }
  
  .retry-btn {
    background: var(--error-color);
    color: var(--text-color-inverse);
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      transform: scale(0.98);
      background: var(--error-color-dark);
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .track-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .statistics-section .statistics-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .track-section {
    padding: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .track-item {
    padding: var(--spacing-sm);
    
    .track-header .track-type {
      font-size: var(--font-size-xs);
      padding: var(--spacing-xs);
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .track-container {
    background-color: var(--background-color-secondary);
  }
  
  .track-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .stat-item,
  .track-item {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .track-timeline::before {
    background: var(--border-color-primary);
  }
  
  .track-item::before {
    background: var(--background-color-elevated);
    border-color: var(--primary-color);
  }
  
  .track-item .track-footer {
    border-top-color: var(--border-color-primary);
  }
  
  .keyword {
    background: var(--background-color-tertiary);
  }
  
  .action-btn {
    background: var(--background-color-tertiary);
    
    &:active {
      background: var(--primary-color);
    }
  }
} 