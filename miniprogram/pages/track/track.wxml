<view class="track-container">
  <!-- 页面头部 -->
  <view class="track-header">
    <view class="header-content">
      <view class="header-title">性格轨迹</view>
      <view class="header-subtitle">追踪你的MBTI性格变化历程</view>
    </view>
    
    <!-- 时间段切换 -->
    <view class="period-tabs">
      <view class="period-tab {{currentPeriod === 'week' ? 'active' : ''}}" 
            bindtap="handlePeriodChange" data-period="week">
        本周
      </view>
      <view class="period-tab {{currentPeriod === 'month' ? 'active' : ''}}" 
            bindtap="handlePeriodChange" data-period="month">
        本月
      </view>
      <view class="period-tab {{currentPeriod === 'year' ? 'active' : ''}}" 
            bindtap="handlePeriodChange" data-period="year">
        本年
      </view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="statistics-section" wx:if="{{trackRecords.length > 0}}">
    <view class="statistics-grid">
      <view class="stat-item">
        <view class="stat-number">{{trackRecords.length}}</view>
        <view class="stat-label">测试次数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{currentMBTI || '--'}}</view>
        <view class="stat-label">当前类型</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{averageScore || '--'}}</view>
        <view class="stat-label">平均得分</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{streakDays || 0}}</view>
        <view class="stat-label">连续天数</view>
      </view>
    </view>
  </view>

  <!-- 图表区域 -->
  <view class="chart-section" wx:if="{{trackRecords.length > 0 && showChart}}">
    <view class="chart-title">得分趋势</view>
    <view class="chart-container">
      <!-- 这里可以集成图表组件 -->
      <canvas canvas-id="trendChart" style="width: 100%; height: 100%;"></canvas>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{error}}">
    <text class="iconfont icon-shibai error-icon"></text>
    <view class="error-text">{{error}}</view>
    <button class="retry-btn" bindtap="handleRefresh">重新加载</button>
  </view>

  <!-- 轨迹列表 -->
  <view class="track-section" wx:if="{{trackRecords.length > 0 && !error}}">
    <view class="section-header">
      <view class="section-title">测试记录</view>
      <view class="add-button" bindtap="handleAddRecord">
        <text class="iconfont icon-tianjia"></text>
      </view>
    </view>
    
    <view class="track-timeline">
      <view class="track-item {{item.status || 'completed'}}" 
            wx:for="{{trackRecords}}" 
            wx:key="id"
            bindtap="handleRecordTap"
            data-id="{{item.id}}">
        <view class="track-header">
          <view class="track-date">{{item.date}}</view>
          <view class="track-type">{{item.mbtiType}}</view>
        </view>
        <view class="track-content">
          <view class="track-title">MBTI性格测试</view>
          <view class="track-desc">{{item.description}}</view>
          <view class="track-score" wx:if="{{item.score}}">
            得分: <text class="score-value">{{item.score}}</text>分
          </view>
        </view>
        <view class="track-trend" wx:if="{{item.trend}}">
          <text class="iconfont {{item.trend === 'up' ? 'icon-TOP' : item.trend === 'down' ? 'icon-shouqi' : 'icon-hengxian'}}"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="iconfont icon-shijian"></text>
    <view class="empty-title">暂无轨迹记录</view>
    <view class="empty-description">完成你的第一次MBTI测试，开始记录性格成长历程</view>
    <button class="empty-btn" bindtap="handleAddRecord">开始测试</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 