import dayjs from 'dayjs';
import { login<PERSON>anager, LoginStatus } from '../../utils/login-manager';
import { mbtiAPI, type MBTIResult } from '../../utils/api';

interface ITrackRecord {
  id: string;
  date: string;
  mbtiType: string;
  score: number;
  description: string;
  trend: 'up' | 'down' | 'stable';
  dimensions: {
    IE: { score: number; preference: 'I' | 'E' };
    NS: { score: number; preference: 'N' | 'S' };
    TF: { score: number; preference: 'T' | 'F' };
    PJ: { score: number; preference: 'P' | 'J' };
  };
  keywords: string[];
  duration: number;
}

interface IChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
  }>;
}

interface IPageData {
  trackRecords: ITrackRecord[];
  chartData: IChartData;
  currentPeriod: 'week' | 'month' | 'year';
  loading: boolean;
  currentMBTI: string;
  averageScore: number | null;
  streakDays: number;
  showChart: boolean;
  error: string | null;
}

interface ICustomMethods {
  handleLoadTrackData(): Promise<void>;
  handlePeriodChange(e: WechatMiniprogram.TouchEvent): void;
  handleRecordTap(e: WechatMiniprogram.TouchEvent): void;
  handleAddRecord(): Promise<void>;
  updateChartData(): void;
  calculateStreakDays(records: ITrackRecord[]): number;
  calculateOverallScore(dimensions: MBTIResult['dimensions']): number;
  checkLoginStatus(): Promise<boolean>;
  handleLogin(): Promise<void>;
  checkAndLoadData(): Promise<void>;
  showLoginPrompt(): Promise<void>;
  handleRefresh(): Promise<void>;
  handleDeleteRecord(e: WechatMiniprogram.TouchEvent): Promise<void>;
}

Page<IPageData, ICustomMethods>({
  data: {
    trackRecords: [],
    chartData: {
      labels: [],
      datasets: []
    },
    currentPeriod: 'month',
    loading: false,
    currentMBTI: '',
    averageScore: null,
    streakDays: 0,
    showChart: false,
    error: null
  },

  onLoad() {
    this.checkAndLoadData();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.checkAndLoadData();
  },

  // 下拉刷新
  async onPullDownRefresh() {
    await this.handleLoadTrackData();
    wx.stopPullDownRefresh();
  },

  async handleLoadTrackData() {
    this.setData({ loading: true, error: null });
    
    try {
      // 调用真实的API接口
      const historyData: MBTIResult[] = await mbtiAPI.getHistory();
      
      // 转换数据格式
      const trackRecords: ITrackRecord[] = historyData.map((item, index) => {
        // 计算趋势（基于时间顺序）
        let trend: 'up' | 'down' | 'stable' = 'stable';
        if (index > 0) {
          const currentScore = this.calculateOverallScore(item.dimensions);
          const prevScore = this.calculateOverallScore(historyData[index - 1].dimensions);
          if (currentScore > prevScore) {
            trend = 'up';
          } else if (currentScore < prevScore) {
            trend = 'down';
          }
        }

        return {
          id: String(item.id),
          date: dayjs(item.createdAt).format('MM-DD'),
          mbtiType: item.mbtiType,
          score: this.calculateOverallScore(item.dimensions),
          description: item.description,
          trend,
          dimensions: item.dimensions,
          keywords: item.keywords,
          duration: item.duration
        };
      });

      // 按时间倒序排列（最新的在前面）
      trackRecords.sort((a, b) => dayjs(b.date, 'MM-DD').valueOf() - dayjs(a.date, 'MM-DD').valueOf());
      
      // 计算统计数据
      const currentMBTI = trackRecords.length > 0 ? trackRecords[0].mbtiType : '';
      const averageScore = trackRecords.length > 0 
        ? Math.round(trackRecords.reduce((sum, r) => sum + r.score, 0) / trackRecords.length)
        : null;
      const streakDays = this.calculateStreakDays(trackRecords);
      
      this.setData({
        trackRecords,
        loading: false,
        currentMBTI,
        averageScore,
        streakDays,
        showChart: trackRecords.length > 0,
        error: null
      });
      
      this.updateChartData();
      
    } catch (error) {
      console.error('加载轨迹数据失败:', error);
      this.setData({ 
        loading: false, 
        error: '加载数据失败，请重试',
        trackRecords: [],
        showChart: false
      });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 计算整体得分
  calculateOverallScore(dimensions: MBTIResult['dimensions']): number {
    const scores = Object.values(dimensions).map(d => d.score);
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  },

  handlePeriodChange(e: WechatMiniprogram.TouchEvent) {
    const { period } = e.currentTarget.dataset;
    this.setData({ currentPeriod: period as 'week' | 'month' | 'year' });
    this.updateChartData();
  },

  async handleRecordTap(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    try {
      await wx.navigateTo({ url: `/pages/report/report?reportId=${id}` });
    } catch (error) {
      console.error('跳转失败:', error);
      wx.showToast({
        title: '跳转失败',
        icon: 'none'
      });
    }
  },

  async handleAddRecord() {
    try {
      await wx.navigateTo({ url: '/pages/test/start/test-start' });
    } catch (error) {
      console.error('开始测试失败:', error);
    }
  },

  async handleRefresh() {
    await this.handleLoadTrackData();
  },

  async handleDeleteRecord(e: WechatMiniprogram.TouchEvent) {
    const { id } = e.currentTarget.dataset;
    
    try {
      const result = await wx.showModal({
        title: '确认删除',
        content: '确定要删除这条测试记录吗？删除后无法恢复。',
        confirmText: '删除',
        confirmColor: '#ff4d4f'
      });

      if (result.confirm) {
        // TODO: 调用删除API
        // await mbtiAPI.deleteHistory(Number(id));
        
        // 暂时从本地数据中移除
        const updatedRecords = this.data.trackRecords.filter(record => record.id !== id);
        this.setData({ trackRecords: updatedRecords });
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  updateChartData() {
    const { trackRecords, currentPeriod } = this.data;
    
    // 根据时间段筛选数据
    let filteredRecords = trackRecords;
    if (currentPeriod === 'week') {
      filteredRecords = trackRecords.filter(record => 
        dayjs(record.date, 'MM-DD').isAfter(dayjs().subtract(7, 'day'))
      );
    } else if (currentPeriod === 'month') {
      filteredRecords = trackRecords.filter(record => 
        dayjs(record.date, 'MM-DD').isAfter(dayjs().subtract(30, 'day'))
      );
    }
    
    const labels = filteredRecords.map(record => record.date);
    const scores = filteredRecords.map(record => record.score);
    
    const chartData = {
      labels,
      datasets: [
        {
          label: 'MBTI得分',
          data: scores,
          borderColor: '#006C68',
          backgroundColor: 'rgba(0, 108, 104, 0.1)'
        }
      ]
    };
    
    this.setData({ chartData });
  },

  // 检查登录状态并加载数据
  async checkAndLoadData() {
    const isLoggedIn = await this.checkLoginStatus();
    if (!isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }
    await this.handleLoadTrackData();
  },

  // 显示登录提示
  async showLoginPrompt() {
    try {
      const result = await wx.showModal({
        title: '请先登录',
        content: '性格轨迹功能需要登录后才能使用，是否立即登录？',
        confirmText: '立即登录',
        cancelText: '取消'
      });

      if (result.confirm) {
        await this.handleLogin();
      } else {
        // 用户取消，跳转回首页
        await wx.switchTab({ url: '/pages/home/<USER>' });
      }
    } catch (error) {
      console.error('显示登录提示失败:', error);
    }
  },

  // 检查登录状态
  async checkLoginStatus(): Promise<boolean> {
    try {
      const status = loginManager.getStatus();
      return status === LoginStatus.LOGGED_IN;
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  },

  // 处理登录
  async handleLogin() {
    try {
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        // 登录成功后重新加载数据
        setTimeout(() => {
          this.handleLoadTrackData();
        }, 1000);
      } else {
        wx.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  calculateStreakDays(records: ITrackRecord[]): number {
    if (records.length === 0) return 0;
    
    let streak = 1;
    const today = dayjs();
    
    for (let i = 1; i < records.length; i++) {
      const currentDate = dayjs(records[i - 1].date, 'MM-DD');
      const prevDate = dayjs(records[i].date, 'MM-DD');
      
      if (currentDate.diff(prevDate, 'day') === 1) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  }
}); 