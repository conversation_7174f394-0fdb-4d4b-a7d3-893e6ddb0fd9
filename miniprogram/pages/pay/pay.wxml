<view class="pay-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">购买次数</text>
          <text class="page-subtitle">购买分析次数和轨迹次数，享受更好的体验</text>
  </view>

  <!-- 商品列表 -->
  <view class="products-list">
    <view 
      class="product-item {{item.isPopular ? 'popular' : ''}} {{selectedProduct === item.id ? 'selected' : ''}}" 
      wx:for="{{products}}" 
      wx:key="id"
      data-product-id="{{item.id}}"
      bindtap="handleProductSelect"
    >
      <!-- 热门标签 -->
      <view wx:if="{{item.isPopular}}" class="popular-badge">
        <text class="badge-text">热门</text>
      </view>

      <!-- 商品信息 -->
      <view class="product-info">
        <view class="product-header">
          <text class="product-name">{{item.name}}</text>
          <view class="price-info">
            <text class="current-price">¥{{item.price}}</text>
            <text class="original-price">¥{{item.originalPrice}}</text>
          </view>
        </view>
        
        <text class="product-description">{{item.description}}</text>
        
        <view class="product-features">
          <view class="feature-item" wx:for="{{item.features}}" wx:key="*this" wx:for-item="feature">
            <text class="feature-icon">✓</text>
            <text class="feature-text">{{feature}}</text>
          </view>
        </view>
        
        <text class="duration-text">{{item.duration}}天有效期</text>
      </view>

      <!-- 选择指示器 -->
      <view class="selection-indicator">
        <view class="indicator-circle {{selectedProduct === item.id ? 'selected' : ''}}"></view>
      </view>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="payment-section">
    <button 
      class="purchase-btn {{loading ? 'loading' : ''}}" 
      bindtap="handlePurchase"
      disabled="{{loading}}"
    >
      <text wx:if="{{!loading}}" class="btn-text">立即购买</text>
      <view wx:if="{{loading}}" class="loading-spinner"></view>
    </button>
    
    <button class="restore-btn" bindtap="handleRestore">
      <text class="restore-text">恢复购买</text>
    </button>
  </view>

  <!-- 服务说明 -->
  <view class="service-info">
    <view class="info-item">
      <text class="info-icon">🔒</text>
      <text class="info-text">安全支付，隐私保护</text>
    </view>
    <view class="info-item">
      <text class="info-icon">🔄</text>
      <text class="info-text">7天无理由退款</text>
    </view>
    <view class="info-item">
      <text class="info-icon">📱</text>
      <text class="info-text">多设备同步</text>
    </view>
  </view>
</view> 