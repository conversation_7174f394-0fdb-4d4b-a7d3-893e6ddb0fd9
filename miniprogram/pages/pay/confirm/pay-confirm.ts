import dayjs from 'dayjs';

interface IOrderInfo {
  orderId: string;
  productName: string;
  price: number;
  originalPrice: number;
  description: string;
}

interface IPaymentMethod {
  id: string;
  name: string;
  icon: string;
  selected: boolean;
}

interface IPageData {
  orderInfo: IOrderInfo;
  paymentMethods: IPaymentMethod[];
  selectedMethod: string;
  loading: boolean;
}

interface ICustomMethods {
  handleLoadOrderInfo(): void;
  handlePaymentMethodSelect(e: WechatMiniprogram.TouchEvent): void;
  handleConfirmPayment(): Promise<void>;
  processPayment(): Promise<string>;
  handleCancelPayment(): void;
  handleViewAgreement(): void;
}

Page<IPageData, ICustomMethods>({
  data: {
    orderInfo: {
      orderId: '',
      productName: 'MBTI详细报告',
      price: 9.9,
      originalPrice: 19.9,
      description: '包含完整的性格分析、职业建议、人际关系指导等'
    },
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: 'icon-qian',
        selected: true
      },
      {
        id: 'alipay',
        name: '支付宝',
        icon: 'icon-qian',
        selected: false
      }
    ],
    selectedMethod: 'wechat',
    loading: false
  },

  onLoad() {
    this.handleLoadOrderInfo();
  },

  handleLoadOrderInfo() {
    // 生成订单ID
    const orderId = 'MBTI' + dayjs().format('YYYYMMDDHHmmss') + Math.random().toString(36).substr(2, 6).toUpperCase();
    
    this.setData({
      'orderInfo.orderId': orderId
    });
  },

  handlePaymentMethodSelect(e: WechatMiniprogram.TouchEvent) {
    const { method } = e.currentTarget.dataset;
    
    // 更新支付方式选择
    const paymentMethods = this.data.paymentMethods.map(item => ({
      ...item,
      selected: item.id === method
    }));
    
    this.setData({
      paymentMethods,
      selectedMethod: method
    });
  },

  async handleConfirmPayment() {
    this.setData({ loading: true });
    
    try {
      // 模拟支付流程
      await this.processPayment();
      
      // 支付成功后跳转到结果页面
      wx.navigateTo({ 
        url: '/pages/test/result/test-result?type=INTJ&fromPayment=true'
      });
    } catch (error) {
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  async processPayment() {
    return new Promise<string>((resolve, reject) => {
      // 模拟支付API调用
      setTimeout(() => {
        // 模拟90%成功率
        if (Math.random() > 0.1) {
          resolve('success');
        } else {
          reject(new Error('Payment failed'));
        }
      }, 2000);
    });
  },

  handleCancelPayment() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消支付吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  handleViewAgreement() {
    wx.navigateTo({ url: '/pages/pay/agreement/agreement' });
  }
}); 