/* 支付确认页面样式 - 使用CSS变量系统 */

.pay-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: var(--spacing-md);
}

// 订单信息
.order-section {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
  
  .order-header {
    margin-bottom: var(--spacing-xl);
  }
  
  .order-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .order-id {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
  }
  
  .product-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-top: 1rpx solid var(--border-color-primary);
  }
  
  .product-info {
    flex: 1;
  }
  
  .product-name {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .product-desc {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    line-height: var(--line-height-normal);
  }
  
  .product-price {
    text-align: right;
  }
  
  .current-price {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--error-color);
    margin-bottom: var(--spacing-xs);
  }
  
  .original-price {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    text-decoration: line-through;
  }
}

// 支付方式
.payment-section {
  background: var(--background-color-elevated);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color-primary);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }
  
  .payment-methods {
    .payment-method {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-xl) 0;
      border-bottom: 1rpx solid var(--border-color-primary);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: var(--background-color-tertiary);
      }
      
      &.selected {
        background: var(--primary-color-sub);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-xl) var(--spacing-md);
        margin: 0 calc(-1 * var(--spacing-md));
        border-left: 4rpx solid var(--primary-color);
      }
    }
    
    .method-info {
      display: flex;
      align-items: center;
    }
    
    .method-icon {
      width: 60rpx;
      height: 60rpx;
      margin-right: var(--spacing-md);
      background: var(--background-color-tertiary);
      border-radius: var(--border-radius-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      
      .iconfont {
        font-size: var(--font-size-xl);
        color: var(--text-color-tertiary);
      }
    }
    
    .method-name {
      font-size: var(--font-size-base);
      color: var(--text-color-primary);
      font-weight: var(--font-weight-medium);
    }
    
    .method-check {
      .check-circle {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid var(--border-color-primary);
        border-radius: var(--border-radius-full);
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &.checked {
          border-color: var(--primary-color);
          background: var(--primary-color);
          position: relative;
          
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20rpx;
            height: 20rpx;
            background: var(--text-color-inverse);
            border-radius: var(--border-radius-full);
          }
        }
      }
    }
  }
}

// 支付协议
.agreement-section {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  
  .agreement-text {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    line-height: var(--line-height-normal);
  }
  
  .agreement-link {
    color: var(--primary-color);
    text-decoration: underline;
  }
}

// 支付操作
.payment-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--background-color-elevated);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border-top: 1rpx solid var(--border-color-primary);
  
  .total-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
  }
  
  .total-label {
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    font-weight: var(--font-weight-medium);
  }
  
  .total-price {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--error-color);
  }
  
  .action-buttons {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-md);
  }
  
  .cancel-button {
    background: var(--background-color-tertiary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl) var(--spacing-md);
    text-align: center;
    color: var(--text-color-secondary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:active {
      background: var(--background-color-quaternary);
      transform: scale(0.98);
    }
  }
  
  .confirm-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl) var(--spacing-md);
    text-align: center;
    color: var(--text-color-inverse);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal) var(--ease-in-out);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-xl);
    }
    
    &:disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .pay-container {
    padding: var(--spacing-sm);
  }
  
  .order-section,
  .payment-section {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
  }
  
  .order-title {
    font-size: var(--font-size-base);
  }
  
  .current-price {
    font-size: var(--font-size-xl);
  }
  
  .payment-method {
    padding: var(--spacing-lg) 0;
    
    &.selected {
      padding: var(--spacing-lg) var(--spacing-sm);
      margin: 0 calc(-1 * var(--spacing-sm));
    }
  }
  
  .method-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: var(--spacing-sm);
    
    .iconfont {
      font-size: var(--font-size-lg);
    }
  }
  
  .payment-actions {
    padding: var(--spacing-lg);
    
    .action-buttons {
      gap: var(--spacing-sm);
    }
    
    .cancel-button,
    .confirm-button {
      padding: var(--spacing-lg) var(--spacing-sm);
    }
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .pay-container {
    background-color: var(--background-color-secondary);
  }
  
  .order-section,
  .payment-section {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .product-card {
    border-top-color: var(--border-color-primary);
  }
  
  .payment-method {
    border-bottom-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-tertiary);
    }
    
    &.selected {
      background: var(--primary-color-sub);
      border-left-color: var(--primary-color);
    }
  }
  
  .method-icon {
    background: var(--background-color-tertiary);
    
    .iconfont {
      color: var(--text-color-tertiary);
    }
  }
  
  .method-check .check-circle {
    border-color: var(--border-color-primary);
    
    &.checked {
      border-color: var(--primary-color);
      background: var(--primary-color);
    }
  }
  
  .payment-actions {
    background: var(--background-color-elevated);
    border-top-color: var(--border-color-primary);
  }
  
  .cancel-button {
    background: var(--background-color-tertiary);
    color: var(--text-color-secondary);
    
    &:active {
      background: var(--background-color-quaternary);
    }
  }
} 