<view class="pay-container">
  <!-- 订单信息 -->
  <view class="order-section">
    <view class="order-header">
      <view class="order-title">订单确认</view>
      <view class="order-id">订单号：{{orderInfo.orderId}}</view>
    </view>
    
    <view class="product-card">
      <view class="product-info">
        <view class="product-name">{{orderInfo.productName}}</view>
        <view class="product-desc">{{orderInfo.description}}</view>
      </view>
      <view class="product-price">
        <view class="current-price">¥{{orderInfo.price}}</view>
        <view class="original-price">¥{{orderInfo.originalPrice}}</view>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section">
    <view class="section-title">选择支付方式</view>
    <view class="payment-methods">
      <view 
        class="payment-method {{item.selected ? 'selected' : ''}}" 
        wx:for="{{paymentMethods}}" 
        wx:key="id"
        bindtap="handlePaymentMethodSelect"
        data-method="{{item.id}}"
      >
        <view class="method-info">
          <image class="method-icon" src="{{item.icon}}" mode="aspectFit" />
          <view class="method-name">{{item.name}}</view>
        </view>
        <view class="method-check">
          <view class="check-circle {{item.selected ? 'checked' : ''}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付协议 -->
  <view class="agreement-section">
    <view class="agreement-text">
      点击确认支付即表示同意
      <text class="agreement-link" bindtap="handleViewAgreement">《支付服务协议》</text>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="payment-actions">
    <view class="total-section">
      <view class="total-label">实付金额</view>
      <view class="total-price">¥{{orderInfo.price}}</view>
    </view>
    
    <view class="action-buttons">
      <view class="cancel-button" bindtap="handleCancelPayment">
        <text class="cancel-text">取消</text>
      </view>
      <view class="confirm-button" bindtap="handleConfirmPayment">
        <text class="confirm-text">确认支付</text>
      </view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-mask" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">支付处理中...</text>
    </view>
  </view>
</view> 