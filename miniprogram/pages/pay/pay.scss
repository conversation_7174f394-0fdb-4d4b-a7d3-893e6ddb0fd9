/* 支付页面样式 - 使用CSS变量系统 */

.pay-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary);
  padding: 0;
}

// 页面头部
.pay-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  padding: var(--spacing-xl) var(--spacing-md);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
  margin-bottom: var(--spacing-md);
  
  .header-content {
    text-align: center;
    
    .header-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-inverse);
      margin-bottom: var(--spacing-xs);
    }
    
    .header-subtitle {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 产品介绍
.product-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .product-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1rpx solid var(--border-color-primary);
    
    .product-header {
      text-align: center;
      margin-bottom: var(--spacing-lg);
      
      .product-icon {
        width: 120rpx;
        height: 120rpx;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        box-shadow: var(--shadow-lg);
        
        .iconfont {
          font-size: var(--font-size-4xl);
          color: var(--text-color-inverse);
        }
      }
      
      .product-title {
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--text-color-primary);
        margin-bottom: var(--spacing-sm);
      }
      
      .product-subtitle {
        font-size: var(--font-size-base);
        color: var(--text-color-secondary);
        line-height: var(--line-height-normal);
      }
    }
    
    .product-features {
      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: var(--spacing-md);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .feature-icon {
          width: 48rpx;
          height: 48rpx;
          background: var(--primary-color-sub);
          border-radius: var(--border-radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: var(--spacing-md);
          flex-shrink: 0;
          
          .iconfont {
            font-size: var(--font-size-lg);
            color: var(--primary-color);
          }
        }
        
        .feature-content {
          flex: 1;
          
          .feature-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-color-primary);
            margin-bottom: var(--spacing-xs);
          }
          
          .feature-desc {
            font-size: var(--font-size-sm);
            color: var(--text-color-secondary);
            line-height: var(--line-height-normal);
          }
        }
      }
    }
  }
}

// 价格方案
.pricing-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
  }
  
  .pricing-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    
    .pricing-card {
      background: var(--background-color-elevated);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-lg);
      text-align: center;
      box-shadow: var(--shadow-sm);
      border: 2rpx solid var(--border-color-primary);
      transition: all var(--transition-normal) var(--ease-in-out);
      position: relative;
      
      &:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-md);
      }
      
      &.selected {
        border-color: var(--primary-color);
        background: var(--primary-color-sub);
        
        .pricing-title {
          color: var(--primary-color);
        }
        
        .pricing-price {
          color: var(--primary-color);
        }
      }
      
      &.recommended {
        border-color: var(--primary-color);
        background: linear-gradient(135deg, var(--background-color-elevated), var(--primary-color-sub));
        
        .recommend-badge {
          position: absolute;
          top: var(--spacing-sm);
          right: var(--spacing-sm);
          background: var(--primary-color);
          color: var(--text-color-inverse);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--border-radius-sm);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
        }
      }
      
      .pricing-header {
        margin-bottom: var(--spacing-md);
        
        .pricing-title {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .pricing-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
      
      .pricing-price {
        margin-bottom: var(--spacing-md);
        
        .price-amount {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .price-unit {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
        }
      }
      
      .pricing-features {
        .feature-list {
          list-style: none;
          padding: 0;
          margin: 0;
          
          .feature-item {
            font-size: var(--font-size-sm);
            color: var(--text-color-secondary);
            margin-bottom: var(--spacing-xs);
            
            &:last-child {
              margin-bottom: 0;
            }
            
            &::before {
              content: '✓';
              color: var(--success-color);
              font-weight: var(--font-weight-bold);
              margin-right: var(--spacing-xs);
            }
          }
        }
      }
    }
  }
}

// 支付方式
.payment-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-md);
  }
  
  .payment-methods {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1rpx solid var(--border-color-primary);
    box-shadow: var(--shadow-sm);
    
    .payment-method {
      display: flex;
      align-items: center;
      padding: var(--spacing-lg);
      border-bottom: 1rpx solid var(--border-color-secondary);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: var(--background-color-tertiary);
      }
      
      &.selected {
        background: var(--primary-color-sub);
        border-left: 4rpx solid var(--primary-color);
        
        .method-info .method-name {
          color: var(--primary-color);
          font-weight: var(--font-weight-semibold);
        }
      }
      
      .method-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--background-color-tertiary);
        border-radius: var(--border-radius-sm);
        
        .iconfont {
          font-size: var(--font-size-lg);
          color: var(--text-color-tertiary);
        }
      }
      
      .method-info {
        flex: 1;
        
        .method-name {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .method-desc {
          font-size: var(--font-size-sm);
          color: var(--text-color-secondary);
          line-height: var(--line-height-normal);
        }
      }
      
      .method-radio {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid var(--border-color-primary);
        border-radius: var(--border-radius-full);
        position: relative;
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &.selected {
          border-color: var(--primary-color);
          background: var(--primary-color);
          
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16rpx;
            height: 16rpx;
            background: var(--text-color-inverse);
            border-radius: var(--border-radius-full);
          }
        }
      }
    }
  }
}

// 订单信息
.order-section {
  margin: 0 var(--spacing-md) var(--spacing-md);
  
  .order-card {
    background: var(--background-color-elevated);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1rpx solid var(--border-color-primary);
    box-shadow: var(--shadow-sm);
    
    .order-header {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-md);
    }
    
    .order-items {
      .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-sm) 0;
        border-bottom: 1rpx solid var(--border-color-secondary);
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-label {
          font-size: var(--font-size-base);
          color: var(--text-color-secondary);
        }
        
        .item-value {
          font-size: var(--font-size-base);
          color: var(--text-color-primary);
          font-weight: var(--font-weight-medium);
          
          &.total {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--primary-color);
          }
        }
      }
    }
  }
}

// 支付按钮
.pay-action {
  padding: 0 var(--spacing-md) var(--spacing-xl);
  
  .pay-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-color-inverse);
    border: none;
    border-radius: var(--border-radius-xl);
    padding: 0;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
      );
      transition: left var(--transition-slow) var(--ease-in-out);
    }
    
    &:hover::before {
      left: 100%;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: var(--shadow-xl);
    }
    
    &:disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .pay-header {
    padding: var(--spacing-lg) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
  
  .product-section,
  .pricing-section,
  .payment-section,
  .order-section {
    margin: 0 var(--spacing-sm) var(--spacing-md);
  }
  
  .product-card {
    padding: var(--spacing-lg);
    
    .product-icon {
      width: 100rpx;
      height: 100rpx;
      
      .iconfont {
        font-size: var(--font-size-3xl);
      }
    }
    
    .product-title {
      font-size: var(--font-size-xl);
    }
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .pricing-card {
    padding: var(--spacing-md);
    
    .pricing-price .price-amount {
      font-size: var(--font-size-2xl);
    }
  }
  
  .payment-method {
    padding: var(--spacing-md);
    
    .method-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: var(--spacing-sm);
      
      .iconfont {
        font-size: var(--font-size-base);
      }
    }
  }
  
  .pay-action {
    padding: 0 var(--spacing-sm) var(--spacing-lg);
  }
}

// 黑夜模式特殊样式
[data-theme="dark"] {
  .pay-container {
    background-color: var(--background-color-secondary);
  }
  
  .pay-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .product-card,
  .pricing-card,
  .payment-methods,
  .order-card {
    background: var(--background-color-elevated);
    border-color: var(--border-color-primary);
  }
  
  .product-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
  }
  
  .feature-icon {
    background: var(--primary-color-sub);
    
    .iconfont {
      color: var(--primary-color);
    }
  }
  
  .pricing-card {
    &.selected {
      background: var(--primary-color-sub);
      border-color: var(--primary-color);
    }
  }
  
  .payment-method {
    border-bottom-color: var(--border-color-primary);
    
    &:active {
      background: var(--background-color-tertiary);
    }
    
    &.selected {
      background: var(--primary-color-sub);
      border-left-color: var(--primary-color);
    }
    
    .method-icon {
      background: var(--background-color-tertiary);
    }
  }
  
  .order-item {
    border-bottom-color: var(--border-color-primary);
  }
} 