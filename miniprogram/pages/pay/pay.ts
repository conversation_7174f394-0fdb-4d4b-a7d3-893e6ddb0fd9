import { ordersAPI } from '../../utils/api';
import { loginManager, LoginStatus } from '../../utils/login-manager';

interface IPageData {
  products: Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    originalPrice: number;
    duration: number;
    features: string[];
    isPopular: boolean;
  }>;
  selectedProduct: string;
  loading: boolean;
}

interface IPageMethods {
  loadProducts(): Promise<void>;
  handleProductSelect(e: any): void;
  handlePurchase(): Promise<void>;
  handleRestore(): Promise<void>;
  checkLoginStatus(): Promise<boolean>;
  handleLogin(): Promise<void>;
  checkAndLoadData(): Promise<void>;
  showLoginPrompt(): Promise<void>;
}

Page<IPageData, IPageMethods>({
  data: {
    products: [
      {
        id: 'analysis_basic',
        name: '基础分析包',
        description: '包含5次MBTI性格分析',
        price: 19.9,
        originalPrice: 29.9,
        duration: 0,
        features: ['5次详细分析报告', '职业发展建议', '人际关系分析'],
        isPopular: false
      },
      {
        id: 'analysis_premium',
        name: '高级分析包',
        description: '包含20次MBTI性格分析',
        price: 69.9,
        originalPrice: 99.9,
        duration: 0,
        features: ['20次详细分析报告', '职业发展建议', '人际关系分析', '个人成长指导'],
        isPopular: true
      },
      {
        id: 'track_package',
        name: '轨迹记录包',
        description: '包含10次性格轨迹记录',
        price: 29.9,
        originalPrice: 39.9,
        duration: 0,
        features: ['10次轨迹记录', '性格变化追踪', '成长历程分析'],
        isPopular: false
      }
    ],
    selectedProduct: 'analysis_premium',
    loading: false
  },

  onLoad() {
    this.checkAndLoadData();
  },

  async loadProducts() {
    this.setData({ loading: true });

    try {
      // 使用默认的次数包商品，移除VIP相关商品
      this.setData({
        products: this.data.products,
        loading: false
      });
    } catch (error) {
      console.error('加载商品失败:', error);
      this.setData({ loading: false });
    }
  },

  handleProductSelect(e: any) {
    const { productId } = e.currentTarget.dataset;
    this.setData({ selectedProduct: productId });
  },

  async handlePurchase() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const selectedProduct = this.data.products.find(p => p.id === this.data.selectedProduct);
      if (!selectedProduct) {
        throw new Error('商品不存在');
      }

      const order = await ordersAPI.createOrder(
        parseInt(this.data.selectedProduct.replace(/\D/g, '')) // 提取数字ID
      );

      // 调用微信支付 - 这里需要后端返回微信支付参数
      // 注意：实际的微信支付参数需要后端生成
      await wx.requestPayment({
        timeStamp: String(Math.floor(Date.now() / 1000)),
        nonceStr: 'nonceStr',
        package: 'prepay_id=' + order.id,
        signType: 'MD5',
        paySign: 'paySign'
      });

      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });

      // 跳转到支付确认页面
      setTimeout(() => {
        wx.navigateTo({ url: `/pages/pay/confirm/pay-confirm?orderId=${order.id}` });
      }, 1500);

    } catch (error) {
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  async handleRestore() {
    try {
      // 由于后端接口文档中没有验证支付API，使用模拟逻辑
      wx.showToast({
        title: '没有可恢复的订单',
        icon: 'none'
      });
    } catch (error) {
      console.error('恢复购买失败:', error);
      wx.showToast({
        title: '恢复失败',
        icon: 'none'
      });
    }
  },

  // 检查登录状态并加载数据
  async checkAndLoadData() {
    const isLoggedIn = await this.checkLoginStatus();
    if (!isLoggedIn) {
      await this.showLoginPrompt();
      return;
    }
    this.loadProducts();
  },

  // 显示登录提示
  async showLoginPrompt() {
    try {
      const result = await wx.pro.showModal({
        title: '请先登录',
        content: '购买次数包需要登录后才能使用，是否立即登录？',
        confirmText: '立即登录',
        cancelText: '取消'
      });

      if (result.confirm) {
        await this.handleLogin();
      } else {
        // 用户取消，跳转回首页
        await wx.pro.switchTab({ url: '/pages/home/<USER>' });
      }
    } catch (error) {
      console.error('显示登录提示失败:', error);
    }
  },

  // 检查登录状态
  async checkLoginStatus(): Promise<boolean> {
    try {
      return await loginManager.checkLoginStatus();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  },

  // 处理登录
  async handleLogin(): Promise<void> {
    try {
      console.log('开始用户授权登录...');
      const result = await loginManager.userAuthLogin();
      
      if (result.success) {
        console.log('登录成功:', result.type);
        // 登录成功后重新加载数据
        this.loadProducts();
      } else {
        console.error('登录失败:', result.error);
        await wx.pro.showToast({
          title: result.error || '登录失败',
          icon: 'none'
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);
      await wx.pro.showToast({
        title: error?.message || '登录失败，请重试',
        icon: 'none'
      });
    }
  }
}); 