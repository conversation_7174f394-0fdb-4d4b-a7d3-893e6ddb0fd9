// 日期工具函数 - 替代dayjs插件在微信小程序中的使用
import dayjs from 'dayjs';

// 相对时间计算
export const getRelativeTime = (date: string | Date): string => {
  const now = dayjs();
  const target = dayjs(date);
  const diffInMs = now.diff(target);
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInMonths = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 30));
  const diffInYears = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 365));

  if (diffInMinutes < 1) {
    return '刚刚';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  } else if (diffInDays < 30) {
    return `${diffInDays}天前`;
  } else if (diffInMonths < 12) {
    return `${diffInMonths}个月前`;
  } else {
    return `${diffInYears}年前`;
  }
};

// 格式化日期
export const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return dayjs(date).format(format);
};

// 获取今天日期
export const getToday = (format: string = 'YYYY-MM-DD'): string => {
  return dayjs().format(format);
};

// 获取当前时间
export const getNow = (format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return dayjs().format(format);
};

// 日期加减
export const addDays = (date: string | Date, days: number): string => {
  return dayjs(date).add(days, 'day').format('YYYY-MM-DD');
};

export const subtractDays = (date: string | Date, days: number): string => {
  return dayjs(date).subtract(days, 'day').format('YYYY-MM-DD');
};

// 判断是否是同一天
export const isSameDay = (date1: string | Date, date2: string | Date): boolean => {
  return dayjs(date1).format('YYYY-MM-DD') === dayjs(date2).format('YYYY-MM-DD');
};

// 判断是否是今天
export const isToday = (date: string | Date): boolean => {
  return isSameDay(date, new Date());
};

// 判断是否是昨天
export const isYesterday = (date: string | Date): boolean => {
  const yesterday = dayjs().subtract(1, 'day');
  return dayjs(date).format('YYYY-MM-DD') === yesterday.format('YYYY-MM-DD');
};

// 获取友好的日期显示
export const getFriendlyDate = (date: string | Date): string => {
  if (isToday(date)) {
    return '今天';
  } else if (isYesterday(date)) {
    return '昨天';
  } else {
    const daysDiff = dayjs().diff(dayjs(date), 'day');
    if (daysDiff < 7) {
      return `${daysDiff}天前`;
    } else {
      return formatDate(date, 'MM-DD');
    }
  }
};

// 获取友好的时间显示（包含时间）
export const getFriendlyDateTime = (date: string | Date): string => {
  const friendlyDate = getFriendlyDate(date);
  const time = formatDate(date, 'HH:mm');
  
  if (friendlyDate === '今天' || friendlyDate === '昨天') {
    return `${friendlyDate} ${time}`;
  } else {
    return `${friendlyDate} ${time}`;
  }
};

// 常用日期格式常量
export const DATE_FORMATS = {
  FULL: 'YYYY-MM-DD HH:mm:ss',
  DATE_ONLY: 'YYYY-MM-DD',
  TIME_ONLY: 'HH:mm:ss',
  MONTH_DAY: 'MM-DD',
  YEAR_MONTH: 'YYYY-MM',
  CHINESE_DATE: 'YYYY年MM月DD日',
  CHINESE_DATETIME: 'YYYY年MM月DD日 HH:mm:ss'
} as const;

// 默认导出常用函数
export default {
  getRelativeTime,
  formatDate,
  getToday,
  getNow,
  addDays,
  subtractDays,
  isSameDay,
  isToday,
  isYesterday,
  getFriendlyDate,
  getFriendlyDateTime,
  DATE_FORMATS
}; 