import { request, get, post, put, del } from './request';

// 接口类型定义
export interface UserInfo {
  id: number;
  openid: string;
  nickname: string;
  avatar: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  registerSource: string;
  analysisCredits: number;
  trackCredits: number;
  hasTakenTest: boolean;
  status: number;
  createdAt: string;
  updatedAt: string;
}

export interface MBTIQuestion {
  id: number;
  question: string;
  dimension: 'IE' | 'NS' | 'TF' | 'PJ';
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
    E: string;
  };
  orderNum: number;
  isActive: boolean;
}

export interface MBTIAnswer {
  questionId: number;
  answer: 'A' | 'B' | 'C' | 'D' | 'E';
}

export interface MBTIResult {
  id: number;
  userId: number;
  mbtiType: string;
  dimensions: {
    IE: { score: number; preference: 'I' | 'E' };
    NS: { score: number; preference: 'N' | 'S' };
    TF: { score: number; preference: 'T' | 'F' };
    PJ: { score: number; preference: 'P' | 'J' };
  };
  keywords: string[];
  description: string;
  duration: number;
  createdAt: string;
}

export interface TrajectoryItem {
  id: number;
  mbtiType: string;
  testDate: string;
  summary: string;
  isDeleted: boolean;
}

export interface AIReport {
  id: number;
  userId: number;
  mbtiType: string;
  type: 'career' | 'relationship' | 'personal';
  title: string;
  content: string;
  recommendations: string[];
  isPaid: boolean;
  price: number;
  createdAt: string;
}

export interface UserStatistics {
  totalTests: number;
  mostFrequentType: string;
  testHistory: {
    date: string;
    count: number;
  }[];
  typeDistribution: {
    type: string;
    count: number;
  }[];
}

export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  credit: number;
  type: 'analysis' | 'track' | 'credits';
  isActive: boolean;
  createdAt: string;
}

export interface Order {
  id: string;
  userId: number;
  productId: number;
  orderNo: string;
  amount: number;
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  payType: 'wechat' | 'alipay';
  transactionId?: string;
  createdAt: string;
  paidAt?: string;
}

export interface Credits {
  analysisCredits: number;
  trackCredits: number;
}

export interface InviteCode {
  code: string;
  creditType: 'analysis' | 'track';
  creditCount: number;
}

export interface ShareResponse {
  shareId: string;
  shareUrl: string;
}

// API 接口定义 - 与后端接口文档保持一致
export const API = {
  // 健康检查
  HEALTH: '/health',

  // 用户相关
  USER: {
    LOGIN: '/api/v1/auth/login',
    LOGOUT: '/api/v1/auth/logout',
    PROFILE: '/api/v1/user/profile',
    STATISTICS: '/api/v1/user/statistics'
  },

  // MBTI测试相关
  MBTI: {
    QUESTIONS: '/api/v1/mbti/questions',
    SUBMIT: '/api/v1/mbti/submit',
    HISTORY: '/api/v1/mbti/history'
  },

  // 轨迹相关
  TRAJECTORY: {
    LIST: '/api/v1/trajectory/list',
    DELETE: '/api/v1/trajectory/delete'
  },

  // AI报告相关
  AI: {
    REPORT: '/api/v1/ai/report',
    REPORTS: '/api/v1/ai/reports'
  },

  // 充值系统相关
  PRODUCTS: {
    LIST: '/api/v1/products'
  },

  // 订单相关
  ORDERS: {
    CREATE: '/api/v1/order/create',
    HISTORY: '/api/v1/order/history'
  },

  // 支付相关
  PAYMENT: {
    CALLBACK: '/api/v1/payment/callback'
  },

  // 次数管理
  CREDITS: {
    BALANCE: '/api/v1/credits',
    USE: '/api/v1/credits/use'
  },

  // 邀请码
  INVITE: {
    REDEEM: '/api/v1/invite/redeem'
  },

  // 分享相关
  SHARE: {
    CREATE: '/api/v1/share/create'
  }
};

// 用户相关API
export const userAPI = {
  // 微信登录
  async login(code: string, userInfo: WechatMiniprogram.UserInfo) {
    const response = await post(API.USER.LOGIN, { code, userInfo });
    return response.data;
  },

  // 用户登出
  async logout(): Promise<void> {
    await post(API.USER.LOGOUT);
  },

  // 获取用户信息
  async getProfile(): Promise<UserInfo> {
    const response = await get<UserInfo>(API.USER.PROFILE);
    return response.data;
  },

  // 获取用户统计数据
  async getStatistics(): Promise<UserStatistics> {
    const response = await get<UserStatistics>(API.USER.STATISTICS);
    return response.data;
  }
};

// MBTI测试相关API
export const mbtiAPI = {
  // 获取测试题目（40-60题）
  async getQuestions(): Promise<MBTIQuestion[]> {
    const response = await get<MBTIQuestion[]>(API.MBTI.QUESTIONS);
    return response.data;
  },

  // 提交测试答案
  async submitAnswers(answers: MBTIAnswer[], duration?: number): Promise<MBTIResult> {
    const response = await post(API.MBTI.SUBMIT, { answers, duration });
    return response.data;
  },

  // 获取测试历史
  async getHistory(): Promise<MBTIResult[]> {
    const response = await get<MBTIResult[]>(API.MBTI.HISTORY);
    return response.data;
  }
};

// 轨迹相关API
export const trajectoryAPI = {
  // 获取轨迹列表
  async getTrajectoryList(): Promise<TrajectoryItem[]> {
    const response = await get<TrajectoryItem[]>(API.TRAJECTORY.LIST);
    return response.data;
  },

  // 删除轨迹记录
  async deleteTrajectory(id: number): Promise<void> {
    await del(`${API.TRAJECTORY.DELETE}/${id}`);
  }
};

// AI报告相关API
export const aiAPI = {
  // 生成AI分析报告
  async generateReport(mbtiType: string, reportType: 'career' | 'relationship' | 'personal'): Promise<AIReport> {
    const response = await post(API.AI.REPORT, { mbtiType, reportType });
    return response.data;
  },

  // 获取AI报告列表
  async getReports(): Promise<AIReport[]> {
    const response = await get<AIReport[]>(API.AI.REPORTS);
    return response.data;
  }
};

// 充值系统相关API
export const productsAPI = {
  // 获取商品列表
  async getProducts(type?: string): Promise<Product[]> {
    const params = type ? { type } : {};
    const response = await get<Product[]>(API.PRODUCTS.LIST, params);
    return response.data;
  }
};

// 订单相关API
export const ordersAPI = {
  // 创建支付订单
  async createOrder(productId: number): Promise<Order> {
    const response = await post(API.ORDERS.CREATE, { productId });
    return response.data;
  },

  // 获取订单历史
  async getOrderHistory(page: number = 1, pageSize: number = 10): Promise<{
    list: Order[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const response = await get(API.ORDERS.HISTORY, { page, pageSize });
    return response.data;
  }
};

// 次数管理API
export const creditsAPI = {
  // 获取用户次数余额
  async getCredits(): Promise<Credits> {
    const response = await get<Credits>(API.CREDITS.BALANCE);
    return response.data;
  },

  // 使用次数
  async useCredits(type: 'analysis' | 'track'): Promise<{
    message: string;
    balance: Credits;
  }> {
    const response = await post(API.CREDITS.USE, { type });
    return response.data;
  }
};

// 邀请码API
export const inviteAPI = {
  // 兑换邀请码
  async redeemCode(code: string): Promise<{ message: string }> {
    const response = await post(API.INVITE.REDEEM, { code });
    return response.data;
  }
};

// 分享相关API
export const shareAPI = {
  // 创建分享
  async createShare(data: {
    type: 'result' | 'report';
    content: string;
    imageUrl?: string;
  }): Promise<ShareResponse> {
    const response = await post(API.SHARE.CREATE, data);
    return response.data;
  }
};

// 工具函数
export const apiUtils = {
  // 带重试的请求
  async requestWithRetry<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error as Error;
        
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
        }
      }
    }
    
    throw lastError!;
  },

  // 批量请求
  async batchRequest<T>(requests: (() => Promise<T>)[]): Promise<T[]> {
    return Promise.all(requests.map(request => request()));
  },

  // 带缓存的请求
  async requestWithCache<T>(
    key: string,
    requestFn: () => Promise<T>,
    cacheTime: number = 5 * 60 * 1000 // 5分钟
  ): Promise<T> {
    const cached = wx.getStorageSync(key);
    const now = Date.now();
    
    if (cached && cached.timestamp && (now - cached.timestamp) < cacheTime) {
      return cached.data;
    }
    
    const data = await requestFn();
    wx.setStorageSync(key, {
      data,
      timestamp: now
    });
    
    return data;
  }
};

// 导出所有API
export default {
  user: userAPI,
  mbti: mbtiAPI,
  trajectory: trajectoryAPI,
  ai: aiAPI,
  products: productsAPI,
  orders: ordersAPI,
  credits: creditsAPI,
  invite: inviteAPI,
  share: shareAPI,
  utils: apiUtils
}; 