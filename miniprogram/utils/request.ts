import { loginManager, LoginStatus } from './login-manager';

// 请求配置接口
interface IRequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  header?: Record<string, string>;
  timeout?: number;
  showLoading?: boolean;
  loadingText?: string;
  enableAutoRetry?: boolean;  // 是否启用自动重试
  retryCount?: number;        // 最大重试次数
}

// 响应数据接口
interface IResponseData<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 全局配置
const CONFIG = {
  BASE_URL: 'https://www.tutorchat.cn', // 替换为你的实际API地址
//   BASE_URL: 'http://localhost:8081', // 替换为你的实际API地址
  TIMEOUT: 30000,
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json'
  },
  MAX_RETRY_COUNT: 2
};

// 请求拦截器
const requestInterceptor = (config: IRequestConfig): IRequestConfig => {
  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = CONFIG.BASE_URL + config.url;
  }

  // 添加默认请求头
  config.header = {
    ...CONFIG.DEFAULT_HEADERS,
    ...config.header
  };

  // 添加用户token
  const token = wx.getStorageSync('userToken');
  if (token) {
    config.header.Authorization = `Bearer ${token}`;
  }

  // 设置默认超时时间
  if (!config.timeout) {
    config.timeout = CONFIG.TIMEOUT;
  }

  // 设置默认重试配置
  if (config.enableAutoRetry === undefined) {
    config.enableAutoRetry = true;
  }
  if (!config.retryCount) {
    config.retryCount = CONFIG.MAX_RETRY_COUNT;
  }

  return config;
};

// 响应拦截器
const responseInterceptor = <T>(response: any): IResponseData<T> => {
  const { statusCode, data } = response;
  
  console.log('响应拦截器 - statusCode:', statusCode, 'data:', data);
  
  // HTTP状态码检查
  if (statusCode !== 200) {
    console.error('HTTP状态码错误:', statusCode);
    throw new Error(`HTTP错误: ${statusCode}`);
  }

  // 检查响应数据是否存在
  if (!data) {
    console.error('响应数据为空');
    throw new Error('服务器响应数据为空');
  }

  // 业务状态码检查
  if (data.code !== 0 && data.code !== 200) {
    console.error('业务状态码错误:', data.code, '错误信息:', data.message);
    
    // 特殊处理401错误
    if (data.code === 401 || statusCode === 401) {
      handleAuthError();
      throw new Error('登录已过期，请重新登录');
    }
    
    throw new Error(data.message || '请求失败');
  }

  return {
    code: data.code,
    message: data.message || '请求成功',
    data: data.data,
    success: data.code === 0 || data.code === 200
  };
};

// 处理认证错误
const handleAuthError = async () => {
  console.log('检测到认证错误，处理登录过期...');
  
  // 检查当前登录状态
  const currentStatus = loginManager.getStatus();
  if (currentStatus === LoginStatus.LOGGED_IN) {
    // 处理登录过期
    await loginManager.handleLoginExpired();
  }
};

// 错误处理
const handleError = async (error: any, config: IRequestConfig) => {
  console.error('请求错误详情:', error);
  
  // 网络错误
  if (error.errMsg && error.errMsg.includes('request:fail')) {
    await wx.pro.showToast({
      title: '网络连接失败',
      icon: 'none'
    });
    return;
  }

  // 401 未授权错误
  if (error.statusCode === 401 || error.message?.includes('登录已过期') || error.message?.includes('用户未登录')) {
    // 处理登录过期
    await loginManager.handleLoginExpired();
    
    await wx.pro.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });
    return;
  }

  // 403 权限错误
  if (error.statusCode === 403) {
    await wx.pro.showToast({
      title: '权限不足',
      icon: 'none'
    });
    return;
  }

  // 404 资源不存在
  if (error.statusCode === 404) {
    await wx.pro.showToast({
      title: '请求的资源不存在',
      icon: 'none'
    });
    return;
  }

  // 500 服务器错误
  if (error.statusCode >= 500) {
    await wx.pro.showToast({
      title: '服务器异常，请稍后重试',
      icon: 'none'
    });
    return;
  }

  // 其他错误
  await wx.pro.showToast({
    title: error.message || '请求失败',
    icon: 'none'
  });
};

// 自动重试机制
const executeWithRetry = async <T>(
  operation: () => Promise<T>, 
  config: IRequestConfig,
  currentCount = 0
): Promise<T> => {
  try {
    return await operation();
  } catch (error: any) {
    console.log(`请求失败，当前重试次数: ${currentCount}/${config.retryCount || 0}`);
    
    // 如果是认证错误，不进行重试
    if (error.statusCode === 401 || error.message?.includes('登录已过期') || error.message?.includes('用户未登录')) {
      throw error;
    }
    
    // 如果达到最大重试次数，抛出错误
    if (!config.enableAutoRetry || currentCount >= (config.retryCount || 0)) {
      throw error;
    }
    
    // 计算延迟时间（指数退避算法）
    const delay = Math.min(1000 * Math.pow(2, currentCount), 5000);
    console.log(`等待 ${delay}ms 后进行第 ${currentCount + 1} 次重试...`);
    
    // 等待一段时间后重试
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return executeWithRetry(operation, config, currentCount + 1);
  }
};

// 主请求函数
export const request = async <T = any>(config: IRequestConfig): Promise<IResponseData<T>> => {
  const processedConfig = requestInterceptor(config);

  // 检查登录态（如果需要）
  // 排除不需要登录态的接口
  const noAuthPaths = [
    '/health',
    '/api/v1/mbti/questions',       // MBTI题目接口（不需要登录）
    '/api/v1/user/login',           // 登录接口
    '/api/v1/auth/login',           // 备用登录接口
    '/api/v1/auth/register',        // 注册接口  
    '/api/v1/auth/refresh',         // 刷新token接口
    '/api/v1/user/check'            // 检查用户接口
  ];
  
  const needsAuth = !noAuthPaths.some(path => processedConfig.url.includes(path));
  
  if (needsAuth) {
    try {
      const isLoggedIn = await loginManager.checkLoginStatus();
      if (!isLoggedIn) {
        console.warn('用户未登录，但请求需要认证');
        // 抛出认证错误，让调用方处理
        throw new Error('用户未登录');
      }
    } catch (error) {
      console.warn('登录态检查失败:', error);
      throw error; // 认证相关的错误应该抛出
    }
  }

  const executeRequest = async (): Promise<IResponseData<T>> => {
    try {
      // 显示loading
      if (processedConfig.showLoading !== false) {
        await wx.pro.showLoading({
          title: processedConfig.loadingText || '加载中...',
          mask: true
        });
      }

      // 使用 wx-promise-pro 的 Promise 化 API
      const response = await wx.pro.request({
        url: processedConfig.url,
        method: processedConfig.method || 'GET',
        data: processedConfig.data,
        header: processedConfig.header,
        timeout: processedConfig.timeout
      });

      console.log('请求成功 - URL:', processedConfig.url, '响应:', response);
      
      // 处理响应
      const result = responseInterceptor<T>(response);
      return result;

    } catch (error) {
      console.error('请求执行失败:', error);
      await handleError(error, processedConfig);
      throw error;
    } finally {
      // 隐藏loading
      if (processedConfig.showLoading !== false) {
        wx.pro.hideLoading();
      }
    }
  };

  // 如果启用了自动重试，使用重试机制
  if (processedConfig.enableAutoRetry) {
    return executeWithRetry(executeRequest, processedConfig);
  } else {
    return executeRequest();
  }
};

// 便捷方法
export const get = <T = any>(url: string, data?: any, config?: Partial<IRequestConfig>) => {
  return request<T>({
    url,
    method: 'GET',
    data,
    ...config
  });
};

export const post = <T = any>(url: string, data?: any, config?: Partial<IRequestConfig>) => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config
  });
};

export const put = <T = any>(url: string, data?: any, config?: Partial<IRequestConfig>) => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config
  });
};

export const del = <T = any>(url: string, data?: any, config?: Partial<IRequestConfig>) => {
  return request<T>({
    url,
    method: 'DELETE',
    data,
    ...config
  });
};

// 文件上传
export const uploadFile = async (
  url: string, 
  filePath: string, 
  name: string = 'file', 
  formData?: Record<string, any>,
  config?: Partial<IRequestConfig>
) => {
  try {
    // 检查登录态
    const isLoggedIn = await loginManager.checkLoginStatus();
    if (!isLoggedIn) {
      throw new Error('用户未登录');
    }
    
    await wx.pro.showLoading({ title: '上传中...', mask: true });

    const token = wx.getStorageSync('userToken');
    const header: Record<string, string> = {};
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }

    // 使用 wx.pro.uploadFile，它返回 Promise<上传结果>
    const res = await wx.pro.uploadFile({
      url: url.startsWith('http') ? url : CONFIG.BASE_URL + url,
      filePath,
      name,
      formData,
      header
    }) as any;

    try {
      const data = JSON.parse(res.data);
      
      // 检查业务状态码
      if (data.code !== 0 && data.code !== 200) {
        if (data.code === 401) {
          await handleAuthError();
          throw new Error('登录已过期，请重新登录');
        }
        throw new Error(data.message || '上传失败');
      }
      
      return data;
    } catch (parseError: any) {
      if (parseError.message?.includes('登录已过期')) {
        throw parseError;
      }
      throw new Error('解析响应数据失败');
    }
  } catch (error: any) {
    await handleError(error, { url, ...config });
    throw error;
  } finally {
    wx.pro.hideLoading();
  }
};

// 导出配置和登录管理器
export { CONFIG, loginManager }; 