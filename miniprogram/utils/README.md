# 工具使用说明

## 网络请求工具 (request.ts)

### 基本用法

```typescript
import { request, get, post, put, del } from './utils/request';

// 基本请求
const response = await request({
  url: '/api/data',
  method: 'GET',
  showLoading: true
});

// 便捷方法
const data = await get('/api/users');
const result = await post('/api/users', { name: '<PERSON>' });
const updated = await put('/api/users/1', { name: '<PERSON>' });
const deleted = await del('/api/users/1');
```

### 配置选项

```typescript
interface IRequestConfig {
  url: string;                    // 请求URL
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';  // 请求方法
  data?: any;                     // 请求数据
  header?: Record<string, string>; // 请求头
  timeout?: number;               // 超时时间
  showLoading?: boolean;          // 是否显示loading
  loadingText?: string;           // loading文本
}
```

### 文件上传

```typescript
import { uploadFile } from './utils/request';

const result = await uploadFile(
  '/api/upload',
  filePath,
  'file',
  { userId: '123' }
);
```

## 全局状态管理 (store.ts)

### 基本用法

```typescript
import { globalStore } from './utils/store';

// 在页面中订阅状态变化
Page({
  data: {
    userInfo: null,
    isLoggedIn: false
  },

  onLoad() {
    // 订阅状态变化
    this.unsubscribe = globalStore.subscribe((state) => {
      this.setData({
        userInfo: state.userInfo,
        isLoggedIn: state.isLoggedIn
      });
    });
  },

  onUnload() {
    // 取消订阅
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  }
});
```

### 状态管理方法

```typescript
// 检查登录状态
const isLoggedIn = globalStore.isLoggedIn();

// 获取用户信息
const userInfo = globalStore.getUserInfo();

// 获取token
const token = globalStore.getToken();

// 更新用户信息
globalStore.updateUserInfo({ nickName: '新昵称' });

// 设置MBTI类型
globalStore.setMbtiType('INTJ');

// 设置次数状态
globalStore.setCredits(10, 5);

// 检查次数状态
const hasAnalysisCredits = globalStore.hasAnalysisCredits();
const hasTrackCredits = globalStore.hasTrackCredits();
```

## API接口 (api.ts)

### 用户相关API

```typescript
import { userAPI } from './utils/api';

// 登录
const loginResult = await userAPI.login(code, userInfo);

// 获取用户信息
const profile = await userAPI.getProfile();

// 更新用户信息
await userAPI.updateProfile({ nickName: '新昵称' });

// 登出
await userAPI.logout();
```

### MBTI测试API

```typescript
import { mbtiAPI } from './utils/api';

// 获取测试题目
const questions = await mbtiAPI.getQuestions();

// 提交测试答案
const result = await mbtiAPI.submitAnswers(['A', 'B', 'C']);

// 获取测试结果
const testResult = await mbtiAPI.getResult('test_id');

// 获取测试历史
const history = await mbtiAPI.getHistory(1, 10);
```

### 报告相关API

```typescript
import { reportAPI } from './utils/api';

// 获取报告列表
const reports = await reportAPI.getReportList();

// 获取报告详情
const report = await reportAPI.getReportDetail('report_id');

// 生成新报告
await reportAPI.generateReport('INTJ');
```

### 支付相关API

```typescript
import { paymentAPI } from './utils/api';

// 创建订单
const order = await paymentAPI.createOrder('product_id', 99);

// 验证支付
await paymentAPI.verifyPayment('order_id', paymentResult);

// 获取支付历史
const history = await paymentAPI.getPaymentHistory();
```

## 在App.ts中使用

```typescript
// app.ts
import { globalStore } from './utils/store';
import { request } from './utils/request';

App({
  onLaunch() {
    // 初始化全局状态
    this.initGlobalState();
  },

  async initGlobalState() {
    await globalStore.init();
  },

  // 登录方法
  async login() {
    if (globalStore.isLoggedIn()) {
      return globalStore.getUserInfo();
    }

    const userInfo = await this.getUserProfile();
    if (userInfo) {
      await globalStore.login(userInfo);
      return globalStore.getUserInfo();
    }
  },

  // 登出方法
  logout() {
    globalStore.logout();
  },

  // 检查登录状态
  checkLoginStatus() {
    return globalStore.isLoggedIn();
  },

  // 检查次数状态
checkCreditsStatus() {
  return globalStore.hasAnalysisCredits() && globalStore.hasTrackCredits();
},

  // 网络请求工具
  request
});
```

## 在页面中使用

```typescript
// pages/example/example.ts
import { globalStore } from '../../utils/store';
import { mbtiAPI } from '../../utils/api';

Page({
  data: {
    userInfo: null,
    questions: [],
    loading: false
  },

  onLoad() {
    // 订阅全局状态
    this.unsubscribe = globalStore.subscribe((state) => {
      this.setData({
        userInfo: state.userInfo
      });
    });

    // 加载数据
    this.loadQuestions();
  },

  onUnload() {
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  },

  async loadQuestions() {
    try {
      this.setData({ loading: true });
      
      const response = await mbtiAPI.getQuestions();
      this.setData({
        questions: response.data,
        loading: false
      });
    } catch (error) {
      console.error('加载失败:', error);
      this.setData({ loading: false });
    }
  },

  async handleLogin() {
    const app = getApp();
    await app.login();
  },

  checkCreditsAccess() {
    const app = getApp();
    const hasCredits = app.checkAnalysisCredits();
    
    if (!hasCredits) {
      wx.showModal({
        title: '次数不足',
        content: '您的分析次数不足，是否立即充值？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/pay/pay' });
          }
        }
      });
      return false;
    }
    
    return true;
  }
});
```

## 错误处理

```typescript
try {
  const response = await mbtiAPI.getQuestions();
  // 处理成功响应
} catch (error: any) {
  // 根据错误类型进行不同处理
  if (error.statusCode === 401) {
    // 未授权，重新登录
    const app = getApp();
    app.logout();
  } else if (error.errMsg && error.errMsg.includes('request:fail')) {
    // 网络错误
    wx.showToast({
      title: '网络连接失败',
      icon: 'none'
    });
  } else {
    // 其他错误
    wx.showToast({
      title: error.message || '请求失败',
      icon: 'error'
    });
  }
}
```

## 配置说明

### 网络请求配置

在 `request.ts` 中修改 `CONFIG` 对象：

```typescript
const CONFIG = {
  BASE_URL: 'https://your-api-domain.com', // 修改为你的API地址
  TIMEOUT: 10000,                          // 超时时间
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json'
  }
};
```

### 状态管理配置

全局状态管理支持以下功能：
- 用户登录状态管理
- 用户信息管理
- 次数状态管理
- MBTI类型管理
- Token管理
- 状态变化订阅

## 注意事项

1. 确保在使用前已经正确配置了API地址
2. 在页面卸载时记得取消状态订阅
3. 网络请求会自动处理loading状态和错误提示
4. 全局状态会在应用启动时自动初始化
5. 所有API调用都支持TypeScript类型检查 