/**
 * 主题管理器
 * 用于管理小程序的亮色/黑夜模式切换
 */

export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark'
}

export interface ThemeConfig {
  mode: ThemeMode;
  autoFollowSystem: boolean;
}

class ThemeManager {
  private currentTheme: ThemeMode = ThemeMode.LIGHT;
  private autoFollowSystem: boolean = false;
  private listeners: Array<(theme: ThemeMode) => void> = [];

  constructor() {
    this.init();
  }

  /**
   * 初始化主题管理器
   */
  private init(): void {
    // 从本地存储读取主题配置
    try {
      const savedConfig = wx.getStorageSync('themeConfig');
      if (savedConfig) {
        this.currentTheme = savedConfig.mode || ThemeMode.LIGHT;
        this.autoFollowSystem = savedConfig.autoFollowSystem || false;
      }
    } catch (error) {
      console.error('读取主题配置失败:', error);
    }

    // 应用当前主题
    this.applyTheme(this.currentTheme);

    // 监听系统主题变化
    this.listenSystemThemeChange();
  }

  /**
   * 获取当前主题模式
   */
  getCurrentTheme(): ThemeMode {
    return this.currentTheme;
  }

  /**
   * 获取主题配置
   */
  getThemeConfig(): ThemeConfig {
    return {
      mode: this.currentTheme,
      autoFollowSystem: this.autoFollowSystem
    };
  }

  /**
   * 切换主题模式
   */
  toggleTheme(): ThemeMode {
    const newTheme = this.currentTheme === ThemeMode.LIGHT ? ThemeMode.DARK : ThemeMode.LIGHT;
    this.setTheme(newTheme);
    return newTheme;
  }

  /**
   * 设置主题模式
   */
  setTheme(theme: ThemeMode): void {
    if (this.currentTheme === theme) return;

    this.currentTheme = theme;
    this.applyTheme(theme);
    this.saveThemeConfig();
    this.notifyListeners(theme);
  }

  /**
   * 设置是否跟随系统主题
   */
  setAutoFollowSystem(enabled: boolean): void {
    this.autoFollowSystem = enabled;
    this.saveThemeConfig();

    if (enabled) {
      // 如果启用跟随系统，立即应用系统主题
      this.applySystemTheme();
    }
  }

  /**
   * 应用主题到页面
   */
  private applyTheme(theme: ThemeMode): void {
    try {
      // 设置页面根元素的data-theme属性
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const pageElement = currentPage.getTabBar?.() || currentPage;
        
        // 通过setData更新页面状态
        if (currentPage.setData) {
          currentPage.setData({
            themeMode: theme,
            isDarkMode: theme === ThemeMode.DARK
          });
        }
      }

      // 更新全局状态
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.themeMode = theme;
        app.globalData.isDarkMode = theme === ThemeMode.DARK;
      }

      console.log(`主题已切换为: ${theme}`);
    } catch (error) {
      console.error('应用主题失败:', error);
    }
  }

  /**
   * 监听系统主题变化
   */
  private listenSystemThemeChange(): void {
    // 微信小程序暂不支持直接监听系统主题变化
    // 可以通过页面显示时检查系统主题
    // 这里提供一个模拟的实现
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      console.log('系统信息:', systemInfo);
      
      // 注意：微信小程序目前不提供系统主题信息
      // 这里只是预留接口，实际使用时需要用户手动切换
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  }

  /**
   * 应用系统主题
   */
  private applySystemTheme(): void {
    // 由于微信小程序限制，这里使用默认的亮色主题
    // 实际项目中可以通过其他方式获取系统主题
    this.setTheme(ThemeMode.LIGHT);
  }

  /**
   * 保存主题配置到本地存储
   */
  private saveThemeConfig(): void {
    try {
      const config: ThemeConfig = {
        mode: this.currentTheme,
        autoFollowSystem: this.autoFollowSystem
      };
      wx.setStorageSync('themeConfig', config);
    } catch (error) {
      console.error('保存主题配置失败:', error);
    }
  }

  /**
   * 订阅主题变化
   */
  subscribe(listener: (theme: ThemeMode) => void): () => void {
    this.listeners.push(listener);
    
    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(theme: ThemeMode): void {
    this.listeners.forEach(listener => {
      try {
        listener(theme);
      } catch (error) {
        console.error('主题变化监听器执行失败:', error);
      }
    });
  }

  /**
   * 获取主题相关的样式类名
   */
  getThemeClass(theme?: ThemeMode): string {
    const currentTheme = theme || this.currentTheme;
    return currentTheme === ThemeMode.DARK ? 'theme-dark' : 'theme-light';
  }

  /**
   * 检查是否为黑夜模式
   */
  isDarkMode(): boolean {
    return this.currentTheme === ThemeMode.DARK;
  }

  /**
   * 获取主题相关的颜色值
   */
  getThemeColors(): Record<string, string> {
    const isDark = this.isDarkMode();
    
    return {
      primary: isDark ? '#00A8A3' : '#006C68',
      background: isDark ? '#1a1a1a' : '#ffffff',
      backgroundSecondary: isDark ? '#262626' : '#f8f9fa',
      text: isDark ? '#ffffff' : '#1a1a1a',
      textSecondary: isDark ? '#b3b3b3' : '#666666',
      border: isDark ? '#404040' : '#e8e8e8',
      shadow: isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.08)'
    };
  }
}

// 创建单例实例
export const themeManager = new ThemeManager();

// 导出便捷方法
export const getCurrentTheme = () => themeManager.getCurrentTheme();
export const setTheme = (theme: ThemeMode) => themeManager.setTheme(theme);
export const toggleTheme = () => themeManager.toggleTheme();
export const isDarkMode = () => themeManager.isDarkMode();
export const subscribeThemeChange = (listener: (theme: ThemeMode) => void) => 
  themeManager.subscribe(listener); 