// 登录管理器 - 实现微信小程序登录最佳实践
import { userAPI } from './api';
import { tokenDebug } from './token-debug';

// 登录状态枚举
export enum LoginStatus {
  NOT_LOGGED_IN = 'NOT_LOGGED_IN',
  LOGGING_IN = 'LOGGING_IN',
  LOGGED_IN = 'LOGGED_IN',
  LOGIN_FAILED = 'LOGIN_FAILED',
  SESSION_EXPIRED = 'SESSION_EXPIRED'
}

// 登录类型
export enum LoginType {
  SILENT = 'SILENT',        // 静默登录（仅验证现有token）
  USER_AUTH = 'USER_AUTH'   // 用户授权登录
}

// 登录结果接口
export interface LoginResult {
  success: boolean;
  user?: any;
  token?: string;
  type: LoginType;
  error?: string;
}

// 熔断器类
class CircuitBreaker {
  private failureCount = 0;
  private nextAttemptTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold = 3,
    private recoveryTimeout = 5000,
    private name = 'unnamed'
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttemptTime) {
        throw new Error(`${this.name} 熔断器已打开，请稍后重试`);
      } else {
        this.state = 'HALF_OPEN';
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
  }

  private onFailure() {
    this.failureCount++;
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      this.nextAttemptTime = Date.now() + this.recoveryTimeout;
      console.warn(`${this.name} 熔断器已打开`);
    }
  }

  reset() {
    this.failureCount = 0;
    this.state = 'CLOSED';
    this.nextAttemptTime = 0;
  }
}

// 单例队列处理器
class SingletonQueue {
  private pending: Promise<any> | null = null;
  private listeners: Array<{ resolve: Function; reject: Function }> = [];

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.pending) {
      // 如果有正在执行的操作，等待其完成
      return new Promise((resolve, reject) => {
        this.listeners.push({ resolve, reject });
      });
    }

    this.pending = operation()
      .then(result => {
        // 通知所有等待的listener
        this.listeners.forEach(listener => listener.resolve(result));
        return result;
      })
      .catch(error => {
        // 通知所有等待的listener
        this.listeners.forEach(listener => listener.reject(error));
        throw error;
      })
      .finally(() => {
        this.pending = null;
        this.listeners = [];
      });

    return this.pending;
  }
}

class LoginManager {
  private status: LoginStatus = LoginStatus.NOT_LOGGED_IN;
  private circuitBreaker = new CircuitBreaker(3, 5000, 'LoginManager');
  private singletonQueue = new SingletonQueue();
  private sessionKey: string | null = null;
  private listeners: Array<(status: LoginStatus, data?: any) => void> = [];

  // 订阅登录状态变化
  subscribe(callback: (status: LoginStatus, data?: any) => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 通知状态变化
  private notify(status: LoginStatus, data?: any) {
    this.status = status;
    this.listeners.forEach(callback => {
      try {
        callback(status, data);
      } catch (error) {
        console.error('登录状态回调错误:', error);
      }
    });
  }

  // 获取当前状态
  getStatus(): LoginStatus {
    return this.status;
  }

  // 检查session_key是否有效
  async checkSessionKey(): Promise<boolean> {
    try {
      await wx.pro.checkSession();
      return true;
    } catch (error) {
      console.warn('session_key已过期:', error);
      return false;
    }
  }

  // 静默登录（修复版本 - 只验证现有token）
  async silentLogin(): Promise<LoginResult> {
    return this.singletonQueue.execute(async () => {
      return this.circuitBreaker.execute(async () => {
        console.log('开始静默登录...');
        this.notify(LoginStatus.LOGGING_IN);

        try {
          // 1. 检查本地token
          const storedToken = wx.getStorageSync('userToken');
          if (!storedToken) {
            console.log('没有本地token，静默登录失败');
            this.notify(LoginStatus.NOT_LOGGED_IN);
            return {
              success: false,
              type: LoginType.SILENT,
              error: '没有本地token'
            };
          }

          // 2. 验证token有效性（通过获取用户信息）
          try {
            const userInfo = await userAPI.getProfile();
            console.log('token验证成功，静默登录完成');
            this.notify(LoginStatus.LOGGED_IN, { user: userInfo, token: storedToken });
            return {
              success: true,
              user: userInfo,
              token: storedToken,
              type: LoginType.SILENT
            };
          } catch (error: any) {
            console.warn('静默登录token验证失败', error);
            
            // 🔧 修复：只在确定token无效时删除
            if (error?.statusCode === 401 || error?.code === 401 || 
                error?.message?.includes('token') || error?.message?.includes('unauthorized')) {
                             console.warn('🗑️ token确实已失效，清除token');
              tokenDebug.recordTokenDelete('静默登录时token验证失败(401/unauthorized)');
              wx.removeStorageSync('userToken');
              this.notify(LoginStatus.NOT_LOGGED_IN);
              return {
                success: false,
                type: LoginType.SILENT,
                error: 'token已失效'
              };
            } else {
              // 网络或服务器问题，保留token但静默登录失败
              console.warn('静默登录失败但保留token，可能是网络问题', error);
              this.notify(LoginStatus.NOT_LOGGED_IN);
              return {
                success: false,
                type: LoginType.SILENT,
                error: '网络错误，请稍后重试'
              };
            }
          }

        } catch (error: any) {
          console.error('静默登录失败:', error);
          this.notify(LoginStatus.LOGIN_FAILED, { error });
          return {
            success: false,
            type: LoginType.SILENT,
            error: error?.message || '静默登录失败'
          };
        }
      });
    });
  }

  // 用户授权登录
  async userAuthLogin(): Promise<LoginResult> {
    return this.singletonQueue.execute(async () => {
      return this.circuitBreaker.execute(async () => {
        console.log('开始用户授权登录...');
        this.notify(LoginStatus.LOGGING_IN);

        try {
          // 1. 获取用户信息
          const userInfo = await this.getUserProfile();
          if (!userInfo) {
            throw new Error('用户取消授权');
          }

          // 2. 检查session_key
          const hasValidSession = await this.checkSessionKey();
          if (!hasValidSession) {
            console.log('session_key已过期，获取新的登录态');
          }

          // 3. 获取新的code（如果需要）
          let code = this.sessionKey;
          if (!hasValidSession || !code) {
            const loginResult = await wx.pro.login();
            code = loginResult.code;
            this.sessionKey = code;
          }

          // 4. 调用后端登录
          console.log('准备调用后端登录接口...', { code, userInfo });
          const response = await userAPI.login(code, userInfo);
          console.log('后端登录响应:', response);
          const { user, token } = response;

          // 5. 存储token
          console.log('🔐 保存userToken到本地存储');
          wx.setStorageSync('userToken', token);
          tokenDebug.recordTokenSave(token);

          this.notify(LoginStatus.LOGGED_IN, { user, token });
          console.log('用户授权登录成功');

          await wx.pro.showToast({
            title: '登录成功',
            icon: 'success'
          });

          return {
            success: true,
            user,
            token,
            type: LoginType.USER_AUTH
          };

        } catch (error: any) {
          console.error('用户授权登录失败:', error);
          this.notify(LoginStatus.LOGIN_FAILED, { error });
          
          await wx.pro.showToast({
            title: error?.message || '登录失败',
            icon: 'none'
          });

          return {
            success: false,
            type: LoginType.USER_AUTH,
            error: error?.message || '用户授权登录失败'
          };
        }
      });
    });
  }

  // 获取用户信息
  private async getUserProfile(): Promise<WechatMiniprogram.UserInfo | null> {
    try {
      const res = await wx.pro.getUserProfile({
        desc: '用于完善用户资料'
      });
      return res.userInfo;
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      await wx.pro.showToast({
        title: '需要授权才能登录',
        icon: 'none'
      });
      return null;
    }
  }

  // 刷新登录态（优化版本）
  async refreshLogin(): Promise<LoginResult> {
    console.log('刷新登录态...');
    // 清除当前状态
    this.sessionKey = null;
    wx.removeStorageSync('userToken');
    
    // 重新静默登录（只验证现有token）
    return this.silentLogin();
  }

    // 检查是否有有效的登录态（优化版本 - 避免死循环）
  async checkLoginStatus(): Promise<boolean> {
    try {
      // 检查本地token
      const token = wx.getStorageSync('userToken');
      if (!token) {
        console.log('checkLoginStatus: 没有本地token');
        return false;
      }

      console.log('checkLoginStatus: 发现本地token，假设有效');
      // 🔧 修复死循环：简化验证逻辑，只检查token存在性
      // 真正的token验证会在实际API调用时进行
      return true;
    } catch (error: any) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  }

  // 深度验证token有效性（仅在必要时调用）
  async validateTokenDeep(): Promise<boolean> {
    try {
      // 检查本地token
      const token = wx.getStorageSync('userToken');
      if (!token) {
        console.log('validateTokenDeep: 没有本地token');
        return false;
      }

      // 验证token有效性（深度验证）
      try {
        console.log('validateTokenDeep: 验证token有效性...');
        await userAPI.getProfile();
        console.log('validateTokenDeep: token有效');
        return true;
      } catch (error: any) {
        console.warn('validateTokenDeep: token验证失败', error);
        
        // 只在特定错误情况下删除token
        if (error?.statusCode === 401 || error?.code === 401 || 
            error?.message?.includes('token') || error?.message?.includes('unauthorized')) {
          console.warn('🗑️ validateTokenDeep: token确实无效，清除本地token');
          tokenDebug.recordTokenDelete('validateTokenDeep时token验证失败(401/unauthorized)');
          wx.removeStorageSync('userToken');
          return false;
        } else {
          // 网络错误、服务器错误等情况，保留token但返回false
          console.warn('validateTokenDeep: 网络或服务器问题，保留token', error);
          return false;
        }
      }
    } catch (error: any) {
      console.error('深度验证token失败:', error);
      return false;
    }
  }

  // 确保有效的会话（新方法）
  async ensureValidSession(): Promise<boolean> {
    try {
      const isLoggedIn = await this.checkLoginStatus();
      if (!isLoggedIn) {
        console.log('没有有效的登录态');
        this.notify(LoginStatus.NOT_LOGGED_IN);
        return false;
      }

      // 检查session_key
      const hasValidSession = await this.checkSessionKey();
      if (!hasValidSession) {
        console.log('session_key已过期，但token仍有效');
        // session_key过期不影响API调用，因为主要靠token验证
      }

      return true;
    } catch (error: any) {
      console.error('确保有效会话失败:', error);
      return false;
    }
  }

  // 退出登录
  async logout(): Promise<void> {
    try {
      console.log('开始退出登录...');
      
      // 1. 调用后端登出接口
      const token = wx.getStorageSync('userToken');
      if (token) {
        try {
          await userAPI.logout();
        } catch (error: any) {
          console.warn('调用后端登出接口失败:', error);
          // 即使后端接口失败，也继续执行本地清理
        }
      }

      // 2. 清除所有本地存储（使用最佳实践）
      console.log('🗑️ 退出登录，清除所有本地存储');
      tokenDebug.recordTokenDelete('用户主动退出登录');
      wx.clearStorageSync();

      // 3. 重置状态
      this.sessionKey = null;
      this.circuitBreaker.reset();
      this.notify(LoginStatus.NOT_LOGGED_IN);

      // 4. 显示成功提示
      await wx.pro.showToast({
        title: '退出登录成功',
        icon: 'success',
        duration: 2000
      });

      // 5. 延迟跳转到首页并强制重启（最佳实践）
      setTimeout(() => {
        wx.pro.reLaunch({
          url: '/pages/home/<USER>'
        });
      }, 2000);

    } catch (error: any) {
      console.error('退出登录失败:', error);
      await wx.pro.showToast({
        title: '退出失败，请重试',
        icon: 'none'
      });
    }
  }

  // 处理登录过期
  async handleLoginExpired(): Promise<void> {
    console.log('处理登录过期...');
    this.notify(LoginStatus.SESSION_EXPIRED);
    
    // 清除本地token
    wx.removeStorageSync('userToken');
    this.sessionKey = null;
    
    // 通知用户登录过期，但不自动重新登录
    this.notify(LoginStatus.NOT_LOGGED_IN);
    
    console.log('登录已过期，请手动重新登录');
  }

  // 手动触发登录检查
  async performLoginCheck(): Promise<LoginResult> {
    console.log('执行登录检查...');
    return this.silentLogin();
  }
}

// 导出单例实例
export const loginManager = new LoginManager();
export default loginManager; 