import { request } from './request';

// 创建匿名用户
const createAnonymousUser = async (nickName: string) => {
  const response = await request({
    url: '/anonymous/create',
    method: 'POST',
    data: { nickName }
  });
  return response.data.userId;
};

// 绑定微信账号
const bindWechatAccount = async (userId: string, wechatCode: string) => {
  const response = await request({
    url: '/anonymous/bind',
    method: 'POST',
    data: {
      userId,
      wechatCode
    }
  });
  return response.data;
};

class AnonymousManager {
  private static instance: AnonymousManager;
  private anonymousId: string = '';
  private isAnonymous: boolean = false;

  static getInstance() {
    if (!AnonymousManager.instance) {
      AnonymousManager.instance = new AnonymousManager();
    }
    return AnonymousManager.instance;
  }

  async createAnonymous(nickName: string) {
    try {
      const userId = await createAnonymousUser(nickName);
      this.anonymousId = userId;
      this.isAnonymous = true;
      
      // 存储到本地
      wx.setStorageSync('anonymousId', userId);
      wx.setStorageSync('isAnonymous', true);
      
      return userId;
    } catch (error) {
      console.error('创建匿名用户失败:', error);
      throw error;
    }
  }

  async bindWechat(wechatCode: string) {
    if (!this.anonymousId) {
      throw new Error('未找到匿名用户ID');
    }

    try {
      await bindWechatAccount(this.anonymousId, wechatCode);
      
      // 清除匿名状态
      this.isAnonymous = false;
      wx.removeStorageSync('anonymousId');
      wx.removeStorageSync('isAnonymous');
      
      return true;
    } catch (error) {
      console.error('绑定微信失败:', error);
      throw error;
    }
  }

  isAnonymousUser() {
    return this.isAnonymous;
  }

  getAnonymousId() {
    return this.anonymousId;
  }
}

export const anonymousManager = AnonymousManager.getInstance(); 