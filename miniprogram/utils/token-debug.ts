// Token 调试工具
// 用于帮助追踪 userToken 的状态变化

interface TokenDebugInfo {
  hasToken: boolean;
  token?: string;
  tokenLength?: number;
  lastSaved?: string;
  lastChecked?: string;
  checkCount: number;
  deleteCount: number;
}

class TokenDebugManager {
  private checkCount = 0;
  private deleteCount = 0;
  
  // 检查token状态
  checkTokenStatus(): TokenDebugInfo {
    this.checkCount++;
    const token = wx.getStorageSync('userToken');
    const lastSaved = wx.getStorageSync('tokenSavedTime');
    
    return {
      hasToken: !!token,
      token: token ? token.substring(0, 10) + '...' : undefined,
      tokenLength: token?.length,
      lastSaved,
      lastChecked: new Date().toISOString(),
      checkCount: this.checkCount,
      deleteCount: this.deleteCount
    };
  }
  
  // 记录token保存
  recordTokenSave(token: string) {
    console.log('📝 Token调试: 保存token', {
      length: token.length,
      preview: token.substring(0, 10) + '...',
      time: new Date().toISOString()
    });
    
    // 记录保存时间
    wx.setStorageSync('tokenSavedTime', new Date().toISOString());
  }
  
  // 记录token删除
  recordTokenDelete(reason: string) {
    this.deleteCount++;
    console.log('🗑️ Token调试: 删除token', {
      reason,
      deleteCount: this.deleteCount,
      time: new Date().toISOString()
    });
    
    // 记录删除历史
    const deleteHistory = wx.getStorageSync('tokenDeleteHistory') || [];
    deleteHistory.push({
      reason,
      time: new Date().toISOString(),
      count: this.deleteCount
    });
    
    // 只保留最近10次删除记录
    if (deleteHistory.length > 10) {
      deleteHistory.splice(0, deleteHistory.length - 10);
    }
    
    wx.setStorageSync('tokenDeleteHistory', deleteHistory);
  }
  
  // 获取删除历史
  getDeleteHistory() {
    return wx.getStorageSync('tokenDeleteHistory') || [];
  }
  
  // 清理调试数据
  clearDebugData() {
    wx.removeStorageSync('tokenSavedTime');
    wx.removeStorageSync('tokenDeleteHistory');
    this.checkCount = 0;
    this.deleteCount = 0;
    console.log('🧹 Token调试数据已清理');
  }
  
  // 生成调试报告
  generateDebugReport(): string {
    const status = this.checkTokenStatus();
    const history = this.getDeleteHistory();
    
    let report = '📊 Token状态报告\n';
    report += `━━━━━━━━━━━━━━━━━━━━\n`;
    report += `当前状态: ${status.hasToken ? '✅ 有token' : '❌ 无token'}\n`;
    
    if (status.hasToken) {
      report += `Token长度: ${status.tokenLength}\n`;
      report += `Token预览: ${status.token}\n`;
      report += `保存时间: ${status.lastSaved || '未知'}\n`;
    }
    
    report += `检查次数: ${status.checkCount}\n`;
    report += `删除次数: ${status.deleteCount}\n`;
    report += `━━━━━━━━━━━━━━━━━━━━\n`;
    
    if (history.length > 0) {
      report += `最近删除记录:\n`;
      history.slice(-3).forEach((record: any, index: number) => {
        report += `${index + 1}. ${record.reason} (${record.time})\n`;
      });
    } else {
      report += `无删除记录\n`;
    }
    
    return report;
  }
}

// 创建全局实例
const tokenDebug = new TokenDebugManager();

export { tokenDebug, TokenDebugManager };
export type { TokenDebugInfo }; 