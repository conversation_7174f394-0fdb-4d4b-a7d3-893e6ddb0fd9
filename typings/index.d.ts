/// <reference path="./types/index.d.ts" />

interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo,
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
}

// 为wx-promise-pro添加类型定义
declare namespace WechatMiniprogram {
  interface Wx {
    pro: {
      request(options: RequestOption): Promise<RequestSuccessCallbackResult>;
      navigateTo(options: NavigateToOption): Promise<NavigateToSuccessCallbackResult>;
      switchTab(options: SwitchTabOption): Promise<GeneralCallbackResult>;
      showModal(options: ShowModalOption): Promise<ShowModalSuccessCallbackResult>;
      showToast(options: ShowToastOption): Promise<GeneralCallbackResult>;
      showLoading(options: ShowLoadingOption): Promise<GeneralCallbackResult>;
      hideLoading(): Promise<GeneralCallbackResult>;
      getUserProfile(options: GetUserProfileOption): Promise<GetUserProfileSuccessCallbackResult>;
      stopPullDownRefresh(): Promise<GeneralCallbackResult>;
      uploadFile(options: UploadFileOption): Promise<UploadFileSuccessCallbackResult>;
      login(options?: LoginOption): Promise<LoginSuccessCallbackResult>;
      [key: string]: any;
    };
  }
}