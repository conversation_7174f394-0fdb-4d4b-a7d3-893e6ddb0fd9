# MBTI人格测试小程序

<div align="center">

![MBTI Logo](https://img.shields.io/badge/MBTI-人格测试-1890ff?style=for-the-badge&logo=wechat)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)
![WeChat Mini Program](https://img.shields.io/badge/微信小程序-07C160?style=for-the-badge&logo=wechat&logoColor=white)

**专业的MBTI人格测试小程序，提供科学的心理测评、个性化报告和成长轨迹分析**

[功能特性](#-功能特性) • [技术架构](#-技术架构) • [快速开始](#-快速开始) • [开发指南](#-开发指南) • [API文档](#-api文档)

</div>

---

## 📋 目录

- [🎯 项目概述](#-项目概述)
- [✨ 功能特性](#-功能特性)
- [🏗️ 技术架构](#️-技术架构)
- [🚀 快速开始](#-快速开始)
- [📱 页面结构](#-页面结构)
- [🔧 开发指南](#-开发指南)
- [📊 数据流程](#-数据流程)
- [🔒 安全机制](#-安全机制)
- [📈 性能优化](#-性能优化)
- [🧪 测试策略](#-测试策略)
- [📦 部署指南](#-部署指南)
- [🤝 贡献指南](#-贡献指南)
- [📄 许可证](#-许可证)

---

## 🎯 项目概述

MBTI人格测试小程序是一个基于微信小程序平台的专业心理测评应用，旨在帮助用户了解自己的人格类型，提供个性化的成长建议和职业发展指导。

### 🎯 核心价值
- **科学测评**: 基于MBTI理论的专业心理测评
- **个性化报告**: 根据测试结果生成个性化分析报告
- **成长轨迹**: 追踪人格变化，分析成长趋势
- **职业指导**: 提供职业匹配和发展建议
- **社交分享**: 支持结果分享和社交互动

### 🎯 目标用户
- 对自我认知感兴趣的个人
- 寻求职业发展指导的职场人士
- 希望了解团队成员的HR和管理者
- 心理学爱好者和研究者

---

## ✨ 功能特性

### 🧠 核心功能
- **MBTI测试**: 60题专业测评，准确识别16种人格类型
- **结果分析**: 详细的人格特征分析和解释
- **个性化报告**: 多维度的人格分析报告
- **历史记录**: 完整的测试历史和数据追踪
- **成长轨迹**: 人格变化趋势分析和对比

### 💰 次数服务
- **分析次数**: 用于生成详细的分析报告
- **轨迹次数**: 用于记录性格变化轨迹
- **无限测试**: 不受限制的测试次数
- **优先支持**: 专属客服和技术支持

### 📊 数据统计
- **个人统计**: 测试次数、平均得分、偏好类型
- **趋势分析**: 人格变化趋势图表
- **对比分析**: 多次测试结果对比
- **学习进度**: 报告阅读和学习追踪

### 🎁 激励系统
- **积分机制**: 完成任务获得积分奖励
- **兑换码**: 支持积分兑换和优惠券
- **成就系统**: 完成目标获得成就徽章
- **推荐奖励**: 邀请好友获得额外奖励

### 📤 社交功能
- **结果分享**: 生成精美的分享图卡
- **匿名测试**: 支持匿名用户测试
- **访客模式**: 无需登录即可体验
- **社交绑定**: 匿名用户绑定微信账号

### 🛠 管理功能
- **内容管理**: 后台题库和模板管理
- **用户管理**: 用户数据和权限管理
- **订单管理**: 支付订单和次数管理
- **数据统计**: 平台运营数据分析

---

## 🏗️ 技术架构

### 📱 前端技术栈
- **框架**: 微信小程序原生开发
- **语言**: TypeScript
- **样式**: SCSS
- **状态管理**: 自定义全局状态管理
- **网络请求**: 封装wx.request
- **日期处理**: dayjs
- **API封装**: wx-promise-pro

### 🔧 后端技术栈
- **语言**: Node.js / Python / Java (可选)
- **框架**: Express / FastAPI / Spring Boot
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **文件存储**: 阿里云OSS / 腾讯云COS
- **支付**: 微信支付
- **消息推送**: 微信模板消息

### 🗂️ 项目结构
```
mbti/
├── miniprogram/              # 小程序前端代码
│   ├── pages/               # 页面文件
│   │   ├── home/           # 首页
│   │   ├── test/           # 测试相关
│   │   ├── report/         # 报告相关
│   │   ├── track/          # 数据追踪
│   │   ├── me/             # 个人中心
│   │   └── pay/            # 支付相关
│   ├── components/         # 公共组件
│   ├── utils/             # 工具函数
│   │   ├── request.ts     # 网络请求
│   │   ├── store.ts       # 状态管理
│   │   └── api.ts         # API接口
│   ├── images/            # 图片资源
│   └── app.ts             # 应用入口
├── server/                 # 后端服务代码
│   ├── controllers/       # 控制器
│   ├── models/           # 数据模型
│   ├── routes/           # 路由
│   ├── middleware/       # 中间件
│   └── utils/            # 工具函数
├── docs/                  # 项目文档
│   ├── API接口文档.md
│   ├── 数据库设计文档.md
│   └── 功能列表与实现方法.md
└── README.md             # 项目说明
```

---

## 🚀 快速开始

### 📋 环境要求
- Node.js 16.0+
- 微信开发者工具
- MySQL 8.0+
- Redis 6.0+

### 🔧 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/your-username/mbti.git
cd mbti
```

#### 2. 安装依赖
```bash
# 安装小程序依赖
npm install

# 安装后端依赖
cd server
npm install
```

#### 3. 配置数据库
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE mbti CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
mysql -u root -p mbti < docs/database.sql
```

#### 4. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置
vim .env
```

#### 5. 启动服务
```bash
# 启动后端服务
cd server
npm run dev

# 启动小程序开发工具
# 打开微信开发者工具，导入项目
```

### 🔑 配置说明

#### 小程序配置
```json
// project.config.json
{
  "appid": "your-app-id",
  "projectname": "mbti",
  "setting": {
    "compileHotReLoad": true
  }
}
```

#### 后端配置
```javascript
// config/database.js
module.exports = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'mbti'
}
```

---

## 📱 页面结构

### 🏠 主要页面
- **首页 (home)**: 功能导航、内容推荐
- **测试 (test)**: MBTI测试流程
- **报告 (report)**: 报告列表和详情
- **追踪 (track)**: 数据统计和趋势
- **我的 (me)**: 个人中心和设置
- **支付 (pay)**: 次数包购买和支付

### 🔄 页面流程
```
首页 → 测试开始 → 答题过程 → 结果展示 → 报告生成 → 分享/购买
  ↓
个人中心 → 历史记录 → 数据统计 → 次数管理
```

---

## 🔧 开发指南

### 📝 代码规范
- 使用TypeScript严格模式
- 遵循ESLint规则
- 使用Prettier格式化
- 遵循Git提交规范

### 🏗️ 开发流程
1. **功能设计**: 明确需求和接口设计
2. **数据库设计**: 设计表结构和关系
3. **后端开发**: 实现API接口和业务逻辑
4. **前端开发**: 实现页面和交互逻辑
5. **联调测试**: 前后端联调和功能测试
6. **部署上线**: 生产环境部署和监控

### 🔍 调试技巧
- 使用微信开发者工具调试
- 查看网络请求和响应
- 监控页面性能和内存
- 使用真机调试验证

### 📱 前端开发规范

#### TypeScript 规范
- 使用严格模式 TypeScript 配置
- 所有变量和函数必须有明确的类型声明
- 优先使用 `const` 和 `let`，避免使用 `var`
- 使用接口定义对象结构，使用类型别名定义联合类型
- 事件处理函数使用 `handle` 前缀命名

#### SCSS 样式规范
- 使用 BEM 命名规范：`.block__element--modifier`
- 使用 CSS 变量定义主题色彩和尺寸
- 支持 flexbox 和 grid 布局
- 使用 rpx 单位确保多设备适配
- 避免使用过深的嵌套（不超过3层）

#### 组件开发规范
- 组件文件结构：`.ts`、`.wxml`、`.scss`、`.json`
- 使用 `Component()` 构造函数注册组件
- 明确定义组件的属性和事件
- 使用 `triggerEvent` 向父组件传递事件
- 遵循单一职责原则，保持组件简洁

### 🎨 渲染引擎说明

#### WebView 渲染引擎
本项目使用微信小程序传统的 WebView 渲染引擎，支持完整的 CSS 特性和 Web 标准。

**✅ 支持的 CSS 属性：**
- **布局**: `display: flex/grid/block/inline/inline-block/none`
- **定位**: `position: relative/absolute/fixed`
- **Flexbox**: `flex-direction`, `flex-wrap`, `justify-content`, `align-items`, `align-self`, `flex`
- **Grid**: `grid-template-columns`, `grid-template-rows`, `gap`
- **尺寸**: `width`, `height`, `min-width`, `min-height`, `max-width`, `max-height`
- **间距**: `margin`, `padding`
- **边框**: `border`, `border-radius`, `box-sizing`
- **背景**: `background-color`, `background-image`, `background-size`, `background-position`, `background-repeat`, `background-attachment`, `background-origin`, `background-clip`
- **文本**: `color`, `font-size`, `font-weight`, `font-family`, `text-align`, `line-height`, `text-decoration`, `text-indent`, `word-break`
- **变换**: `transform`, `opacity`, `z-index`
- **动画**: `animation`, `@keyframes`, `animation-play-state`, `animation-fill-mode`
- **过渡**: `transition`, `transition-property`, `transition-duration`, `transition-timing-function`

**✅ 支持的选择器：**
- 类选择器: `.class-name`
- ID 选择器: `#id-name`
- 标签选择器: `view`, `text`, `image` 等
- 伪元素: `::before`, `::after`
- 伪类: `:first-child`, `:last-child`, `:active`, `:hover`, `:focus`
- 全局选择器: `:root`
- 滚动条样式: `::-webkit-scrollbar`

**✅ 推荐的布局方式：**
```scss
// 支持 Grid 布局
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

// 支持 Flexbox 布局
.flex-container {
  display: flex;
  flex-wrap: wrap;
  
  .item {
    width: calc(50% - 10rpx);
    margin-bottom: 20rpx;
  }
}

// 支持 :root 选择器
:root {
  --primary-color: #006C68;
  --font-size-base: 28rpx;
}
```

**✅ 推荐的文本处理：**
```html
<!-- 支持在 view 上使用 text-overflow -->
<view style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden;">
  文本内容
</view>

<!-- 支持多行省略 -->
<text style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
  多行文本内容
</text>
```

**✅ 推荐的滚动实现：**
```html
<!-- 支持全局滚动 -->
<view class="page-container">
  <view wx:for="{{items}}" class="item"></view>
</view>

<!-- 支持局部滚动 -->
<scroll-view scroll-y style="height: 100vh;">
  <view wx:for="{{items}}" class="item"></view>
</scroll-view>
```

#### 配置说明
```json
{
  "style": "v2",
  "lazyCodeLoading": "requiredComponents"
}
```

#### 开发检查清单
- [ ] 是否使用了正确的CSS属性？
- [ ] 是否使用了支持的选择器？
- [ ] 是否实现了响应式布局？
- [ ] 是否优化了动画性能？
- [ ] 是否测试了不同设备兼容性？

### 🔧 后端开发规范

#### API 设计规范
- 遵循 RESTful API 设计原则
- 使用统一的响应格式
- 实现完整的错误处理机制
- 使用 JWT 进行身份认证
- 实现请求频率限制

#### 数据库设计规范
- 使用 InnoDB 存储引擎
- 所有表必须有主键
- 使用 utf8mb4 字符集
- 实现软删除机制
- 添加创建时间和更新时间字段

#### 安全规范
- 所有用户输入必须进行验证和过滤
- 使用参数化查询防止 SQL 注入
- 实现 CSRF 防护机制
- 敏感数据必须加密存储
- 实现完整的日志记录

### 📊 数据流程

#### 用户测试流程
1. **用户注册/登录** → 获取用户身份
2. **开始测试** → 加载题库和用户状态
3. **答题过程** → 实时保存答案和进度
4. **提交测试** → 计算MBTI类型和得分
5. **生成报告** → 创建个性化分析报告
6. **保存结果** → 存储到用户历史记录

#### 数据处理流程
1. **数据收集** → 收集用户答题数据
2. **数据清洗** → 验证和清理数据
3. **类型计算** → 根据MBTI算法计算类型
4. **报告生成** → 基于类型生成分析报告
5. **数据存储** → 保存到数据库
6. **统计分析** → 生成趋势和对比分析

### 🔒 安全机制

#### 用户认证
- 微信授权登录
- JWT Token 认证
- Token 自动刷新机制
- 会话超时处理

#### 数据安全
- 敏感数据加密存储
- 数据传输 HTTPS 加密
- 数据库访问权限控制
- 定期数据备份

#### 接口安全
- 请求签名验证
- 频率限制防护
- 参数验证和过滤
- 错误信息脱敏

### 📈 性能优化

#### 前端优化
- 图片懒加载和压缩
- 组件按需加载
- 数据缓存机制
- 减少不必要的重渲染

#### 后端优化
- 数据库索引优化
- 查询结果缓存
- 分页查询优化
- 异步处理机制

#### 网络优化
- 请求合并和批量处理
- 数据压缩传输
- CDN 加速静态资源
- 连接池管理

### 🧪 测试策略

#### 单元测试
- 工具函数测试
- 组件功能测试
- API 接口测试
- 数据库操作测试

#### 集成测试
- 页面流程测试
- 用户交互测试
- 数据流测试
- 第三方服务集成测试

#### 性能测试
- 页面加载性能
- 接口响应时间
- 并发用户测试
- 内存使用监控

### 📦 部署指南

#### 开发环境
```bash
# 启动开发服务器
npm run dev

# 启动数据库
docker-compose up -d

# 启动 Redis
redis-server
```

#### 生产环境
```bash
# 构建生产版本
npm run build

# 部署到服务器
npm run deploy

# 启动生产服务
npm run start
```

#### 监控和维护
- 应用性能监控
- 错误日志收集
- 用户行为分析
- 系统健康检查

### 🤝 贡献指南

#### 代码贡献
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 代码审查和合并

#### 文档贡献
- 更新 API 文档
- 完善开发指南
- 添加使用示例
- 修复文档错误

#### 问题反馈
- 使用 Issue 模板
- 提供详细的错误信息
- 附上复现步骤
- 标注优先级和标签

---

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

---

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/your-username/mbti)
- **问题反馈**: [Issues](https://github.com/your-username/mbti/issues)
- **功能建议**: [Discussions](https://github.com/your-username/mbti/discussions)
- **邮箱联系**: <EMAIL>

---

<div align="center">

**如果这个项目对你有帮助，请给它一个 ⭐️**

Made with ❤️ by [Your Name]

</div> 