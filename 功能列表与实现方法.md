# MBTI小程序功能列表与实现方法

## 📱 项目概述
MBTI人格测试小程序，提供专业的MBTI测试、结果分析、个性化报告和次数包服务。

## 🏗️ 技术架构

### 核心技术栈
- **框架**: 微信小程序原生开发
- **语言**: TypeScript
- **样式**: SCSS
- **状态管理**: 自定义全局状态管理
- **网络请求**: 封装wx.request
- **日期处理**: dayjs
- **API封装**: wx-promise-pro

### 项目结构
```
miniprogram/
├── pages/           # 页面文件
├── components/      # 组件文件
├── utils/          # 工具函数
├── images/         # 图片资源
└── app.ts          # 应用入口
```

## 📋 功能模块列表

### 1. 用户系统模块

#### 1.1 用户登录/注册
**功能描述**: 微信授权登录，获取用户基本信息
**实现方法**:
- 使用 `wx.getUserProfile()` 获取用户信息
- 调用后端登录接口获取token
- 通过 `globalStore.login()` 管理登录状态
- 自动保存token到本地存储

**相关文件**:
- `miniprogram/utils/store.ts` - 登录状态管理
- `miniprogram/app.ts` - 登录方法实现

#### 1.2 用户信息管理
**功能描述**: 管理用户基本信息、MBTI类型、次数状态
**实现方法**:
- 全局状态管理用户信息
- 支持更新昵称、头像、MBTI类型
- 分析次数和轨迹次数管理
- 状态变化自动同步到所有页面

**相关文件**:
- `miniprogram/utils/store.ts` - 用户信息管理
- `miniprogram/pages/me/me.ts` - 个人中心页面

#### 1.3 用户登出
**功能描述**: 清除用户登录状态和本地数据
**实现方法**:
- 清除本地存储的token
- 重置全局状态
- 跳转到登录页面

### 2. MBTI测试模块

#### 2.1 测试题目展示
**功能描述**: 展示MBTI测试题目，支持题目切换
**实现方法**:
- 从API获取测试题目列表
- 使用 `wx:for` 循环渲染题目
- 实现题目切换动画
- 支持进度条显示

**相关文件**:
- `miniprogram/pages/test/start/start.ts` - 测试页面逻辑
- `miniprogram/utils/api.ts` - 题目获取API

#### 2.2 答案收集与提交
**功能描述**: 收集用户答案并提交到服务器
**实现方法**:
- 本地存储用户答案
- 答案验证和完整性检查
- 使用 `mbtiAPI.submitAnswers()` 提交
- 获取测试结果ID

#### 2.3 测试结果展示
**功能描述**: 展示MBTI测试结果和详细分析
**实现方法**:
- 根据测试ID获取结果
- 展示MBTI类型和特征描述
- 提供详细的人格分析
- 支持结果分享功能

**相关文件**:
- `miniprogram/pages/test/result/result.ts` - 结果页面
- `miniprogram/utils/api.ts` - 结果获取API

#### 2.4 测试历史记录
**功能描述**: 查看历史测试记录和结果
**实现方法**:
- 分页加载历史记录
- 支持按时间筛选
- 快速查看历史结果
- 支持重新测试

### 3. 报告系统模块

#### 3.1 报告列表
**功能描述**: 展示可用的MBTI报告类型
**实现方法**:
- 从API获取报告列表
- 分类展示不同报告类型
- 显示报告简介和价格
- 支持搜索和筛选

**相关文件**:
- `miniprogram/pages/report/report.ts` - 报告列表页面
- `miniprogram/utils/api.ts` - 报告相关API

#### 3.2 报告生成
**功能描述**: 根据MBTI类型生成个性化报告
**实现方法**:
- 调用 `reportAPI.generateReport()` 生成报告
- 显示生成进度
- 支持报告预览
- 自动保存到用户报告库

#### 3.3 报告详情
**功能描述**: 展示完整的MBTI分析报告
**实现方法**:
- 展示详细的人格分析
- 职业建议和发展方向
- 人际关系分析
- 支持报告下载和分享

### 4. 支付系统模块

#### 4.1 商品展示
**功能描述**: 展示次数包购买选项
**实现方法**:
- 展示不同套餐的价格和权益
- 支持套餐对比
- 显示优惠信息
- 支持优惠券使用

**相关文件**:
- `miniprogram/pages/pay/confirm/confirm.ts` - 支付确认页面
- `miniprogram/utils/api.ts` - 支付相关API

#### 4.2 订单创建
**功能描述**: 创建支付订单
**实现方法**:
- 调用 `paymentAPI.createOrder()` 创建订单
- 生成订单号
- 计算支付金额
- 支持多种支付方式

#### 4.3 支付处理
**功能描述**: 处理微信支付流程
**实现方法**:
- 调用 `wx.requestPayment()` 发起支付
- 支付结果验证
- 更新用户次数余额
- 发送支付成功通知

### 5. 首页探索模块

#### 5.1 功能导航
**功能描述**: 提供主要功能的快速入口
**实现方法**:
- 网格布局展示功能模块
- 支持功能图标和描述
- 点击跳转到对应页面
- 支持功能推荐

**相关文件**:
- `miniprogram/pages/home/<USER>
- `miniprogram/pages/home/<USER>

#### 5.2 内容推荐
**功能描述**: 推荐MBTI相关内容
**实现方法**:
- 根据用户MBTI类型推荐内容
- 展示热门测试和报告
- 支持个性化推荐
- 内容轮播展示

### 6. 数据追踪模块

#### 6.1 测试数据统计
**功能描述**: 统计用户的测试数据
**实现方法**:
- 记录测试次数和结果
- 统计测试时间分布
- 生成数据图表
- 支持数据导出

**相关文件**:
- `miniprogram/pages/track/track.ts` - 数据追踪页面
- `miniprogram/utils/api.ts` - 数据统计API

#### 6.2 学习进度追踪
**功能描述**: 追踪用户的学习进度
**实现方法**:
- 记录已完成的报告阅读
- 统计学习时长
- 生成学习报告
- 提供学习建议

## 🔧 核心工具实现

### 1. 网络请求工具 (`miniprogram/utils/request.ts`)

**主要功能**:
- 统一请求封装
- 自动token管理
- 错误处理
- Loading状态管理
- 文件上传

**实现方法**:
```typescript
// 请求拦截器
const requestInterceptor = (config: IRequestConfig) => {
  // 添加基础URL和token
  if (!config.url.startsWith('http')) {
    config.url = CONFIG.BASE_URL + config.url;
  }
  
  const token = wx.getStorageSync('userToken');
  if (token) {
    config.header.Authorization = `Bearer ${token}`;
  }
  
  return config;
};

// 响应拦截器
const responseInterceptor = <T>(response: any): IResponseData<T> => {
  const { statusCode, data } = response;
  
  if (statusCode !== 200) {
    throw new Error(`HTTP错误: ${statusCode}`);
  }
  
  if (data.code !== 0 && data.code !== 200) {
    throw new Error(data.message || '请求失败');
  }
  
  return {
    code: data.code,
    message: data.message,
    data: data.data,
    success: true
  };
};
```

### 2. 全局状态管理 (`miniprogram/utils/store.ts`)

**主要功能**:
- 用户状态管理
- 状态订阅机制
- 持久化存储
- VIP状态管理

**实现方法**:
```typescript
class GlobalStore {
  private state: IGlobalState = {
    userInfo: null,
    isLoggedIn: false,
    token: null,
    loading: false
  };

  private listeners: StateUpdateCallback[] = [];

  // 订阅状态变化
  subscribe(callback: StateUpdateCallback): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 通知所有监听器
  private notify() {
    this.listeners.forEach(callback => {
      try {
        callback(this.getState());
      } catch (error) {
        console.error('状态更新回调执行错误:', error);
      }
    });
  }
}
```

### 3. API接口封装 (`miniprogram/utils/api.ts`)

**主要功能**:
- 业务API封装
- 错误重试机制
- 请求缓存
- 批量请求

**实现方法**:
```typescript
// 用户相关API
export const userAPI = {
  async login(code: string, userInfo: any) {
    return post(API.USER.LOGIN, { code, userInfo });
  },
  
  async getProfile() {
    return get(API.USER.PROFILE);
  }
};

// MBTI测试API
export const mbtiAPI = {
  async getQuestions() {
    return get(API.MBTI.QUESTIONS);
  },
  
  async submitAnswers(answers: string[]) {
    return post(API.MBTI.SUBMIT, { answers });
  }
};
```

## 📊 数据流程

### 1. 用户登录流程
```
用户点击登录 → 获取微信授权 → 调用后端登录接口 → 保存token → 更新全局状态 → 跳转到首页
```

### 2. MBTI测试流程
```
进入测试页 → 获取题目列表 → 用户答题 → 提交答案 → 获取测试结果 → 展示结果页 → 保存到历史记录
```

### 3. 报告生成流程
```
选择报告类型 → 检查VIP状态 → 创建订单 → 支付 → 生成报告 → 展示报告详情
```

## 🎨 UI/UX设计

### 1. 设计原则
- **简洁明了**: 界面简洁，信息层次清晰
- **用户友好**: 操作简单，反馈及时
- **一致性**: 保持设计风格统一
- **响应式**: 适配不同屏幕尺寸

### 2. 色彩方案
- **主色调**: 蓝色系 (#1890ff)
- **辅助色**: 绿色 (#52c41a)、橙色 (#fa8c16)
- **中性色**: 灰色系 (#f5f5f5, #d9d9d9, #8c8c8c)

### 3. 组件设计
- **按钮**: 圆角设计，支持不同状态
- **卡片**: 阴影效果，内容分组
- **导航**: 底部Tab导航，顶部标题栏
- **表单**: 统一的输入框和选择器样式

## 🔒 安全考虑

### 1. 数据安全
- 敏感数据加密存储
- API请求使用HTTPS
- Token过期自动处理
- 用户数据脱敏处理

### 2. 支付安全
- 支付参数签名验证
- 支付结果双重验证
- 订单状态实时同步
- 异常支付监控

### 3. 用户隐私
- 最小化权限申请
- 用户数据本地化存储
- 隐私政策明确说明
- 数据删除机制

## 📈 性能优化

### 1. 加载优化
- 图片懒加载
- 数据分页加载
- 请求缓存机制
- 预加载关键资源

### 2. 渲染优化
- 使用 `wx:key` 优化列表渲染
- 避免频繁的 `setData` 调用
- 合理使用 `wx:if` 和 `hidden`
- 组件化开发减少重复渲染

### 3. 网络优化
- 请求合并和批量处理
- 数据压缩传输
- 断点续传支持
- 离线缓存机制

## 🧪 测试策略

### 1. 功能测试
- 用户登录流程测试
- MBTI测试功能测试
- 支付流程测试
- 报告生成测试

### 2. 兼容性测试
- 不同微信版本测试
- 不同设备型号测试
- 网络环境测试
- 系统版本兼容性测试

### 3. 性能测试
- 页面加载时间测试
- 内存使用情况测试
- 网络请求性能测试
- 用户体验测试

## 🚀 部署与发布

### 1. 开发环境
- 本地开发调试
- 微信开发者工具
- 模拟器测试
- 真机调试

### 2. 测试环境
- 测试版本发布
- 功能验证测试
- 性能压力测试
- 用户体验测试

### 3. 生产环境
- 正式版本发布
- 灰度发布策略
- 监控和告警
- 版本回滚机制

## 📝 开发规范

### 1. 代码规范
- TypeScript严格模式
- ESLint代码检查
- Prettier代码格式化
- Git提交规范

### 2. 命名规范
- 文件命名: kebab-case
- 变量命名: camelCase
- 常量命名: UPPER_SNAKE_CASE
- 组件命名: PascalCase

### 3. 注释规范
- 函数注释说明
- 复杂逻辑注释
- API接口注释
- 组件使用说明

## 🔄 维护与更新

### 1. 版本管理
- 语义化版本号
- 更新日志记录
- 功能特性标记
- 兼容性说明

### 2. 监控告警
- 错误日志监控
- 性能指标监控
- 用户行为分析
- 业务数据统计

### 3. 用户反馈
- 意见反馈收集
- 问题报告处理
- 功能需求分析
- 用户满意度调查

## 🧭 人格轨迹功能

### 功能描述
追踪用户多次测试的人格变化，提供趋势分析和对比功能。

### 实现方法

#### 1. 数据收集
```typescript
// 每次测试完成后，记录人格轨迹数据
const recordPersonalityTrajectory = async (testResult: TestResult) => {
  const trajectoryData = {
    userId: store.userInfo.id,
    testId: testResult.id,
    mbtiType: testResult.mbtiType,
    eScore: testResult.scores.E,
    iScore: testResult.scores.I,
    sScore: testResult.scores.S,
    nScore: testResult.scores.N,
    tScore: testResult.scores.T,
    fScore: testResult.scores.F,
    jScore: testResult.scores.J,
    pScore: testResult.scores.P,
    confidenceLevel: testResult.confidence,
    testDate: dayjs().format('YYYY-MM-DD')
  };
  
  await request.post('/personality/trajectory', trajectoryData);
};
```

#### 2. 趋势分析
```typescript
// 获取人格变化趋势图
const getPersonalityTrend = async (userId: string) => {
  const response = await request.get(`/personality/change-chart?userId=${userId}`);
  return response.data.chartData;
};

// 对比两次测试结果
const compareTests = async (testId1: string, testId2: string) => {
  const response = await request.get('/personality/compare', {
    userId: store.userInfo.id,
    testId1,
    testId2
  });
  return response.data.comparisonResult;
};
```

#### 3. 页面实现
```typescript
// pages/track/track.ts
Page({
  data: {
    trendChart: '',
    comparisonData: null,
    testHistory: []
  },

  async onLoad() {
    await this.loadTrendData();
    await this.loadTestHistory();
  },

  async loadTrendData() {
    try {
      const chartData = await getPersonalityTrend(store.userInfo.id);
      this.setData({ trendChart: chartData });
    } catch (error) {
      console.error('加载趋势数据失败:', error);
    }
  },

  async handleTestCompare(e: any) {
    const { testId1, testId2 } = e.currentTarget.dataset;
    try {
      const comparison = await compareTests(testId1, testId2);
      this.setData({ comparisonData: comparison });
    } catch (error) {
      console.error('对比测试失败:', error);
    }
  }
});
```

## 📤 分享与图卡功能

### 功能描述
生成精美的分享图卡，支持结果和报告的社交分享。

### 实现方法

#### 1. 图卡生成
```typescript
// 生成分享图卡
const generateShareCard = async (reportId: string) => {
  const response = await request.post('/share/generate', { reportId });
  return response.data.shareCode;
};

// 获取分享链接
const getShareLink = async (shareCode: string) => {
  const response = await request.get(`/share/link?shareCode=${shareCode}`);
  return response.data.shareLink;
};
```

#### 2. 分享组件
```typescript
// components/share-card/share-card.ts
Component({
  properties: {
    reportId: String,
    testId: String,
    cardType: {
      type: String,
      value: 'result'
    }
  },

  data: {
    shareCode: '',
    shareLink: '',
    cardImage: ''
  },

  methods: {
    async handleGenerateCard() {
      try {
        const shareCode = await generateShareCard(this.properties.reportId);
        const shareLink = await getShareLink(shareCode);
        
        this.setData({
          shareCode,
          shareLink
        });
        
        this.triggerEvent('generated', { shareCode, shareLink });
      } catch (error) {
        console.error('生成分享图卡失败:', error);
      }
    },

    async handleShare() {
      try {
        await wx.shareAppMessage({
          title: '我的MBTI人格测试结果',
          path: `/pages/share/share?code=${this.data.shareCode}`,
          imageUrl: this.data.cardImage
        });
      } catch (error) {
        console.error('分享失败:', error);
      }
    }
  }
});
```

#### 3. 分享页面
```typescript
// pages/share/share.ts
Page({
  data: {
    shareData: null,
    loading: true
  },

  async onLoad(options: any) {
    const { code } = options;
    await this.loadShareData(code);
  },

  async loadShareData(shareCode: string) {
    try {
      const response = await request.get(`/share/data?shareCode=${shareCode}`);
      this.setData({
        shareData: response.data,
        loading: false
      });
    } catch (error) {
      console.error('加载分享数据失败:', error);
      this.setData({ loading: false });
    }
  }
});
```

## 👥 访客与匿名机制

### 功能描述
支持匿名用户测试，无需登录即可体验，后续可绑定微信账号。

### 实现方法

#### 1. 匿名用户创建
```typescript
// 创建匿名用户
const createAnonymousUser = async (nickName: string) => {
  const response = await request.post('/anonymous/create', { nickName });
  return response.data.userId;
};

// 绑定微信账号
const bindWechatAccount = async (userId: string, wechatCode: string) => {
  const response = await request.post('/anonymous/bind', {
    userId,
    wechatCode
  });
  return response.data;
};
```

#### 2. 匿名状态管理
```typescript
// utils/anonymous.ts
class AnonymousManager {
  private static instance: AnonymousManager;
  private anonymousId: string = '';
  private isAnonymous: boolean = false;

  static getInstance() {
    if (!AnonymousManager.instance) {
      AnonymousManager.instance = new AnonymousManager();
    }
    return AnonymousManager.instance;
  }

  async createAnonymous(nickName: string) {
    try {
      const userId = await createAnonymousUser(nickName);
      this.anonymousId = userId;
      this.isAnonymous = true;
      
      // 存储到本地
      wx.setStorageSync('anonymousId', userId);
      wx.setStorageSync('isAnonymous', true);
      
      return userId;
    } catch (error) {
      console.error('创建匿名用户失败:', error);
      throw error;
    }
  }

  async bindWechat(wechatCode: string) {
    if (!this.anonymousId) {
      throw new Error('未找到匿名用户ID');
    }

    try {
      await bindWechatAccount(this.anonymousId, wechatCode);
      
      // 清除匿名状态
      this.isAnonymous = false;
      wx.removeStorageSync('anonymousId');
      wx.removeStorageSync('isAnonymous');
      
      return true;
    } catch (error) {
      console.error('绑定微信失败:', error);
      throw error;
    }
  }

  isAnonymousUser() {
    return this.isAnonymous;
  }

  getAnonymousId() {
    return this.anonymousId;
  }
}

export const anonymousManager = AnonymousManager.getInstance();
```

#### 3. 匿名测试流程
```typescript
// pages/test/test.ts
Page({
  data: {
    isAnonymous: false,
    anonymousId: ''
  },

  async onLoad() {
    const isAnonymous = wx.getStorageSync('isAnonymous') || false;
    const anonymousId = wx.getStorageSync('anonymousId') || '';
    
    this.setData({
      isAnonymous,
      anonymousId
    });
  },

  async handleStartTest() {
    if (!store.userInfo.id && !this.data.isAnonymous) {
      // 引导用户创建匿名账号或登录
      await this.showAnonymousOption();
      return;
    }

    // 开始测试
    await this.startTest();
  },

  async showAnonymousOption() {
    try {
      const result = await wx.showModal({
        title: '选择测试方式',
        content: '您可以选择匿名测试或登录后测试',
        confirmText: '匿名测试',
        cancelText: '登录测试'
      });

      if (result.confirm) {
        await this.createAnonymousUser();
      } else {
        await this.navigateToLogin();
      }
    } catch (error) {
      console.error('选择测试方式失败:', error);
    }
  },

  async createAnonymousUser() {
    try {
      const nickName = `匿名用户${Math.floor(Math.random() * 10000)}`;
      const userId = await anonymousManager.createAnonymous(nickName);
      
      this.setData({
        isAnonymous: true,
        anonymousId: userId
      });

      await this.startTest();
    } catch (error) {
      console.error('创建匿名用户失败:', error);
      wx.showToast({ title: '创建失败', icon: 'error' });
    }
  }
});
```

## 🎁 积分与激励系统

### 功能描述
通过积分机制激励用户参与，支持兑换码和成就系统。

### 实现方法

#### 1. 积分管理
```typescript
// 获取用户积分
const getUserPoints = async (userId: string) => {
  const response = await request.get(`/reward/user?userId=${userId}`);
  return response.data;
};

// 使用兑换码
const useRedeemCode = async (code: string) => {
  const response = await request.post('/reward/redeem', {
    userId: store.userInfo.id,
    code
  });
  return response.data;
};

// 获取成就列表
const getAchievements = async () => {
  const response = await request.get('/reward/achievements');
  return response.data.list;
};
```

#### 2. 积分规则
```typescript
// utils/points.ts
export const POINTS_RULES = {
  TEST_COMPLETE: 10,      // 完成测试
  REPORT_READ: 5,         // 阅读报告
  SHARE_RESULT: 15,       // 分享结果
  INVITE_FRIEND: 50,      // 邀请好友
  DAILY_LOGIN: 2,         // 每日登录
  WEEKLY_ACTIVE: 20       // 周活跃奖励
};

export class PointsManager {
  static async addPoints(source: string, description?: string) {
    const points = POINTS_RULES[source] || 0;
    
    if (points > 0) {
      try {
        await request.post('/reward/add-points', {
          userId: store.userInfo.id,
          points,
          source,
          description
        });
        
        // 更新本地积分显示
        store.updateUserPoints(points);
        
        return points;
      } catch (error) {
        console.error('添加积分失败:', error);
        return 0;
      }
    }
    
    return 0;
  }

  static async checkAchievements() {
    try {
      const achievements = await getAchievements();
      
      for (const achievement of achievements) {
        if (achievement.isCompleted && !achievement.rewardClaimed) {
          await this.claimAchievementReward(achievement.id);
        }
      }
    } catch (error) {
      console.error('检查成就失败:', error);
    }
  }

  static async claimAchievementReward(achievementId: string) {
    try {
      await request.post('/reward/claim-achievement', {
        userId: store.userInfo.id,
        achievementId
      });
      
      wx.showToast({
        title: '成就奖励已领取',
        icon: 'success'
      });
    } catch (error) {
      console.error('领取成就奖励失败:', error);
    }
  }
}
```

#### 3. 积分页面
```typescript
// pages/points/points.ts
Page({
  data: {
    pointsInfo: null,
    achievements: [],
    redeemCode: ''
  },

  async onLoad() {
    await this.loadPointsInfo();
    await this.loadAchievements();
  },

  async loadPointsInfo() {
    try {
      const pointsInfo = await getUserPoints(store.userInfo.id);
      this.setData({ pointsInfo });
    } catch (error) {
      console.error('加载积分信息失败:', error);
    }
  },

  async loadAchievements() {
    try {
      const achievements = await getAchievements();
      this.setData({ achievements });
    } catch (error) {
      console.error('加载成就失败:', error);
    }
  },

  async handleRedeemCode() {
    if (!this.data.redeemCode.trim()) {
      wx.showToast({ title: '请输入兑换码', icon: 'none' });
      return;
    }

    try {
      const result = await useRedeemCode(this.data.redeemCode);
      
      wx.showToast({
        title: `获得${result.newPoints}积分`,
        icon: 'success'
      });
      
      // 重新加载积分信息
      await this.loadPointsInfo();
      
      this.setData({ redeemCode: '' });
    } catch (error) {
      console.error('使用兑换码失败:', error);
      wx.showToast({ title: '兑换码无效', icon: 'error' });
    }
  },

  async handleClaimReward(e: any) {
    const { achievementId } = e.currentTarget.dataset;
    await PointsManager.claimAchievementReward(achievementId);
    await this.loadAchievements();
  }
});
```

## 🛠 后台内容管理

### 功能描述
提供后台管理功能，支持题库和报告模板的管理。

### 实现方法

#### 1. 题库管理
```typescript
// 获取题目列表
const getQuestions = async (page: number = 1, size: number = 10) => {
  const response = await request.get('/admin/questions', {
    page,
    size
  });
  return response.data;
};

// 添加题目
const addQuestion = async (questionData: any) => {
  const response = await request.post('/admin/questions', questionData);
  return response.data;
};

// 更新题目
const updateQuestion = async (questionId: string, questionData: any) => {
  const response = await request.put(`/admin/questions/${questionId}`, questionData);
  return response.data;
};

// 删除题目
const deleteQuestion = async (questionId: string) => {
  const response = await request.delete(`/admin/questions/${questionId}`);
  return response.data;
};
```

#### 2. 报告模板管理
```typescript
// 获取报告模板列表
const getReportTemplates = async () => {
  const response = await request.get('/admin/report-templates');
  return response.data.list;
};

// 更新报告模板
const updateReportTemplate = async (templateId: string, templateData: any) => {
  const response = await request.put('/admin/report-template', {
    templateId,
    ...templateData
  });
  return response.data;
};
```

#### 3. 管理页面
```typescript
// pages/admin/questions/questions.ts
Page({
  data: {
    questions: [],
    total: 0,
    page: 1,
    size: 10,
    loading: false
  },

  async onLoad() {
    await this.loadQuestions();
  },

  async loadQuestions() {
    this.setData({ loading: true });
    
    try {
      const result = await getQuestions(this.data.page, this.data.size);
      this.setData({
        questions: result.list,
        total: result.total,
        loading: false
      });
    } catch (error) {
      console.error('加载题目失败:', error);
      this.setData({ loading: false });
    }
  },

  async handleAddQuestion() {
    wx.navigateTo({
      url: '/pages/admin/question-edit/question-edit'
    });
  },

  async handleEditQuestion(e: any) {
    const { questionId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/admin/question-edit/question-edit?id=${questionId}`
    });
  },

  async handleDeleteQuestion(e: any) {
    const { questionId } = e.currentTarget.dataset;
    
    try {
      const result = await wx.showModal({
        title: '确认删除',
        content: '确定要删除这道题目吗？'
      });

      if (result.confirm) {
        await deleteQuestion(questionId);
        wx.showToast({ title: '删除成功', icon: 'success' });
        await this.loadQuestions();
      }
    } catch (error) {
      console.error('删除题目失败:', error);
      wx.showToast({ title: '删除失败', icon: 'error' });
    }
  }
});
```

## 📊 数据统计与分析

### 功能描述
提供用户行为分析和业务数据统计。

### 实现方法

#### 1. 数据埋点
```typescript
// utils/analytics.ts
export class Analytics {
  static track(event: string, properties?: any) {
    const eventData = {
      event,
      properties: {
        userId: store.userInfo.id,
        timestamp: Date.now(),
        ...properties
      }
    };

    // 发送到统计服务
    request.post('/analytics/track', eventData).catch(error => {
      console.error('统计事件发送失败:', error);
    });
  }

  static trackPageView(pageName: string) {
    this.track('page_view', { pageName });
  }

  static trackTestStart() {
    this.track('test_start');
  }

  static trackTestComplete(mbtiType: string, duration: number) {
    this.track('test_complete', { mbtiType, duration });
  }

  static trackReportView(reportId: string) {
    this.track('report_view', { reportId });
  }

  static trackShare(shareType: string) {
    this.track('share', { shareType });
  }

  static trackPurchase(productId: string, amount: number) {
    this.track('purchase', { productId, amount });
  }
}
```

#### 2. 统计接口
```typescript
// 获取用户统计数据
const getUserStats = async (userId: string) => {
  const response = await request.get(`/stats/user?userId=${userId}`);
  return response.data;
};

// 获取测试趋势
const getTestTrend = async (days: number = 30) => {
  const response = await request.get(`/stats/test-trend?days=${days}`);
  return response.data;
};

// 获取平台统计
const getPlatformStats = async () => {
  const response = await request.get('/stats/platform');
  return response.data;
};
```

#### 3. 统计页面
```typescript
// pages/stats/stats.ts
Page({
  data: {
    userStats: null,
    testTrend: [],
    platformStats: null,
    loading: true
  },

  async onLoad() {
    await this.loadStats();
  },

  async loadStats() {
    try {
      const [userStats, testTrend, platformStats] = await Promise.all([
        getUserStats(store.userInfo.id),
        getTestTrend(30),
        getPlatformStats()
      ]);

      this.setData({
        userStats,
        testTrend,
        platformStats,
        loading: false
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      this.setData({ loading: false });
    }
  }
});
```

---

这份功能列表和实现方法涵盖了MBTI小程序的完整功能架构，包括用户系统、测试模块、报告系统、支付系统等核心功能，以及相应的技术实现方案。每个功能都有明确的实现方法和相关文件说明，可以作为开发指南和项目文档使用。 